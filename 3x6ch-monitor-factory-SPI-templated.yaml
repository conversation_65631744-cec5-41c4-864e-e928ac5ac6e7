# 3×6通道监控配置 - 模板化版本
# 使用模板化的packages来减少重复配置，从1486行减少到约200行

substitutions:
  name: "3x6-ch-monitor"
  friendly_name: "3x6-ch-monitor"
  chA1: "ch_A1"
  chA2: "ch_A2"
  chA3: "ch_A3"
  chA4: "ch_A4"
  chA5: "ch_A5"
  chA6: "ch_A6"
  chB1: "ch_B1"
  chB2: "ch_B2"
  chB3: "ch_B3"
  chB4: "ch_B4"
  chB5: "ch_B5"
  chB6: "ch_B6"
  chC1: "ch_C1"
  chC2: "ch_C2"
  chC3: "ch_C3"
  chC4: "ch_C4"
  chC5: "ch_C5"
  chC6: "ch_C6"

esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"

preferences:
  flash_write_interval: 20min

esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino

# 基础配置
logger:
  hardware_uart: UART0
  level: debug
  # 增加SPI相关的调试信息
  logs:
    sensor: error

api:
ota:
  - platform: esphome

wifi:
  ap:
    ssid: "3x6ch-Monitor"
    password: ""

captive_portal:

web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: calibrate
      name: "Calibration"
      sorting_weight: 0
    - id: voltage_status
      name: "Voltage Status"
      sorting_weight: 1
    - id: power_status
      name: "Power Status"
      sorting_weight: 10
    - id: currentA
      name: "CurrentA"
      sorting_weight: 20
    - id: powerA
      name: "PowerA"
      sorting_weight: 30
    - id: energyA
      name: "EnergyA"
      sorting_weight: 40
    - id: currentB
      name: "CurrentB"
      sorting_weight: 21
    - id: powerB
      name: "PowerB"
      sorting_weight: 31
    - id: energyB
      name: "EnergyB"
      sorting_weight: 41
    - id: currentC
      name: "CurrentC"
      sorting_weight: 22
    - id: powerC
      name: "PowerC"
      sorting_weight: 32
    - id: energyC
      name: "EnergyC"
      sorting_weight: 42
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 50
    - id: diagnostic
      name: "Diagnostic"
      sorting_weight: 60

external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
udp:
  port: 1314
    
packet_transport:
  - platform: udp
    update_interval: 1s
    providers:
       - name: develop-platform-1
sensor:
  - platform: packet_transport
    provider: develop-platform-1
    name: unit1_current
    id: unit1_current
    internal: false
    accuracy_decimals: 3
    web_server: 
      sorting_group_id: calibrate
  - platform: packet_transport
    provider: develop-platform-1
    name: unit1_voltage
    id: unit1_voltage
    internal: false
    accuracy_decimals: 3
    web_server: 
      sorting_group_id: calibrate
status_led:
  pin: GPIO2

spi:
  - id: spi_bus
    mosi_pin: 18
    miso_pin: 1
    clk_pin: 19

i2c:
  sda: 5
  scl: 4
  scan: true
  frequency: 400kHz
  id: i2c_bus

# 使用模板化packages
packages:
  # A相配置
  phase_a_instance: !include
    file: templates/bl0906_phase_instance_spi_template.yaml
    vars:
      cs_pin: 3
      phase_name: "A"
      phase_lower: "a"
      instance_id: "0x906B0001"
      uart_id: "uart_bus_a"
      ch1_name: "${chA1}"
      ch2_name: "${chA2}"
      ch3_name: "${chA3}"
      ch4_name: "${chA4}"
      ch5_name: "${chA5}"
      ch6_name: "${chA6}"
      current_group: "currentA"
      power_group: "powerA"
      energy_group: "energyA"
  phase_a_CHGN_calc: !include
    file: templates/CHGN_calc_3phase.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"
  phase_a_calibration: !include
    file: templates/bl0906_phase_calibration_template.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"
      current_group: "currentA"
      power_group: "powerA"

  phase_a_debug: !include
    file: templates/bl0906_phase_debug_buttons.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"

  # B相配置
  phase_b_instance: !include
    file: templates/bl0906_phase_instance_spi_template.yaml
    vars:
      cs_pin: 7
      phase_name: "B"
      phase_lower: "b"
      instance_id: "0x906B0002"
      uart_id: "uart_bus_b"
      ch1_name: "${chB1}"
      ch2_name: "${chB2}"
      ch3_name: "${chB3}"
      ch4_name: "${chB4}"
      ch5_name: "${chB5}"
      ch6_name: "${chB6}"
      current_group: "currentB"
      power_group: "powerB"
      energy_group: "energyB"
  phase_b_CHGN_calc: !include
    file: templates/CHGN_calc_3phase.yaml
    vars:
      phase_name: "B"
      phase_lower: "b"
  phase_b_calibration: !include
    file: templates/bl0906_phase_calibration_template.yaml
    vars:
      phase_name: "B"
      phase_lower: "b"
      current_group: "currentB"
      power_group: "powerB"

  phase_b_debug: !include
    file: templates/bl0906_phase_debug_buttons.yaml
    vars:
      phase_name: "B"
      phase_lower: "b"

  # C相配置
  phase_c_instance: !include
    file: templates/bl0906_phase_instance_spi_template.yaml
    vars:
      cs_pin: 6
      phase_name: "C"
      phase_lower: "c"
      instance_id: "0x906B0003"
      uart_id: "uart_bus_c"
      ch1_name: "${chC1}"
      ch2_name: "${chC2}"
      ch3_name: "${chC3}"
      ch4_name: "${chC4}"
      ch5_name: "${chC5}"
      ch6_name: "${chC6}"
      current_group: "currentC"
      power_group: "powerC"
      energy_group: "energyC"
  phase_c_CHGN_calc: !include
    file: templates/CHGN_calc_3phase.yaml
    vars:
      phase_name: "C"
      phase_lower: "c"
  phase_c_calibration: !include
    file: templates/bl0906_phase_calibration_template.yaml
    vars:
      phase_name: "C"
      phase_lower: "c"
      current_group: "currentC"
      power_group: "powerC"

  phase_c_debug: !include
    file: templates/bl0906_phase_debug_buttons.yaml
    vars:
      phase_name: "C"
      phase_lower: "c"

  # 系统监控
  system_monitoring: !include templates/system_monitoring_3phase.yaml 

button:
  - platform: template
    name: "Clear Storage (Fix Full Storage)"
    icon: "mdi:delete-sweep"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            if (bl0906 != nullptr) {
              bl0906->clear_calibration_storage();
            }
  - platform: template
    name: "Show Storage Status"
    icon: "mdi:information"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            if (bl0906 != nullptr) {
              bl0906->show_storage_status();
            }