packages: 
  bl0906: !include packages/bl0906_factory_6ch_spi.yaml

substitutions:
  name: "6-ch-monitor-30-calib"
  friendly_name: "6-ch-monitor-30-calib"
  ch1: "ch_1"   #可根据需要修改名称
  ch2: "ch_2"   #可根据需要修改名称
  ch3: "ch_3"    #可根据需要修改名称
  ch4: "ch_4"    #可根据需要修改名称
  ch5: "ch_5"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
esphome:
  name: "${name}"
  name_add_mac_suffix: False
  friendly_name: "${friendly_name}"
  project:
    name: carrot8848.6-ch-monitor
    version: "3.0"
  platformio_options:
    # board_build.partitions: ./partitions.csv
    build_flags:
      - -std=gnu++17
  on_boot:
    - priority: 250
      then:
        - switch.turn_on: bl0906_nrst
esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino

preferences:
  flash_write_interval: 30min
# Enable logging
logger:
  hardware_uart: UART0
  level: debug
  # 增加SPI相关的调试信息
  logs:
    sensor: error

udp:
  
  port: 1314

# Enable Home Assistant API
api:
  reboot_timeout: 0s

ota:
  - platform: esphome
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  power_save_mode: none
  manual_ip:
    static_ip: ************
    gateway: *************
    subnet: *************
  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    # ssid: "${name}"
    password: ""
debug:
  update_interval: 5s

captive_portal:
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: calibrate
      name: "Calibrate"
      sorting_weight: 0
    - id: pzem_004T
      name: "PZEM-004T"
      sorting_weight: 10
    - id: current
      name: "Current"
      sorting_weight: 20
    - id: power
      name: "Power"
      sorting_weight: 30
    - id: energy
      name: "Energy"
      sorting_weight: 40
    - id: energy_stats
      name: "Energy Status"
      sorting_weight: 50
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 60


status_led:
  pin: GPIO2
uart:
  # - id: uart_bus2
  #   rx_pin: 5
  #   tx_pin: 4
  #   baud_rate: 115200
  # - id: uart_bus
  #   rx_pin: 6
  #   tx_pin: 7
  #   baud_rate: 19200
  # - id: uart_bus3
  #   rx_pin: 20
  #   tx_pin: 21
  #   baud_rate: 115200

packet_transport:
  - platform: udp
    update_interval: 1s
    providers:
       - name: develop-platform-1
# modbus:
#   - uart_id: uart_bus2
#     id: mod_bus1
sensor:
  - platform: packet_transport
    provider: develop-platform-1
    name: unit1_current
    id: unit1_current
    internal: false
    accuracy_decimals: 3
    web_server: 
      sorting_group_id: calibrate
  - platform: packet_transport
    provider: develop-platform-1
    name: unit1_voltage
    id: unit1_voltage
    internal: false
    accuracy_decimals: 3
    web_server: 
      sorting_group_id: calibrate
  # - platform: packet_transport
  #   provider: 6-ch-monitor-20-calib
  #   id: ch1_current
  #   name: ch1_current
  #   internal: False
  #   accuracy_decimals: 3
  #   filters: 
  #     - sliding_window_moving_average: 
  #         window_size: 20
  #         send_every: 5
  #         send_first_at: 5
  #   web_server: 
  #     sorting_group_id: napu_ui
  - platform: template
    name: "CH1 CHGN"
    id: chgn_reference_value
    accuracy_decimals: 0
    update_interval: 1s
    lambda: |-
      float ch1 = id(ch1_current).state;
      float unit1 = id(unit1_current).state;
      if (unit1 == 0) {
        return NAN; // 避免除以零
      }
      float err = (ch1 - unit1) / unit1;
      float result = (-err / (1 + err)) * 65536.0f;
      return (int32_t)result;
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "Voltage CHGN"
    id: voltage_chgn_reference_value
    accuracy_decimals: 0
    update_interval: 1s
    lambda: |-
      float ch1 = id(bl0906_voltage).state;
      float unit1 = id(unit1_voltage).state;
      if (unit1 == 0) {
        return NAN; // 避免除以零
      }
      float err = (ch1 - unit1) / unit1;
      float result = (-err / (1 + err)) * 65536.0f;
      return (int32_t)result;
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "Voltage Error"
    id: voltage_error
    accuracy_decimals: 3
    update_interval: 1s
    lambda: 
      return ((id(bl0906_voltage).state - id(unit1_voltage).state) / id(unit1_voltage).state) * 100.0;
    unit_of_measurement: "%"
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "Current Error"
    id: current_error
    accuracy_decimals: 3
    update_interval: 1s
    lambda: 
      return ((id(ch1_current).state - id(unit1_current).state) / id(unit1_current).state) * 100.0;
    unit_of_measurement: "%"
    web_server: 
      sorting_group_id: calibrate

  # - platform: pzemac
  #   id: sensor_pzem
  #   update_interval: 60s
  #   current:
  #     name: "PZEM Current"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   voltage:
  #     name: "PZEM Voltage"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   energy:
  #     name: "PZEM Energy"
  #     id: pzem_energy
  #     filters:
  #       # Multiplication factor from Wh to kWh is 0.001
  #       - multiply: 0.001
  #     unit_of_measurement: kWh
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   power:
  #     name: "PZEM Power"
  #     id: pzemac_power
  #     filters:
  #       # Multiplication factor from W to kW is 0.001
  #      - multiply: 0.001
  #     unit_of_measurement: kW
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   frequency:
  #     name: "PZEM Frequency"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   power_factor:
  #     name: "PZEM Power Factor"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   modbus_id: mod_bus1
  # - platform: "energy_statistics"
  #   total: energy
  #   energy_today:
  #     name: "PZEM Energy Today"
  #   energy_yesterday:
  #     name: "PZEM Energy Yesterday"
  #   energy_week:
  #     name: "PZEM Energy Week"
  #   energy_month:
  #     name: "PZEM Energy Month"

  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"

  - platform: debug
    free:
      name: "Heap Free"
    block:
      name: "Heap Max Block"
    loop_time:
      name: "Loop Time"
    cpu_frequency:
      name: "CPU Frequency"
switch:
  - platform: restart
    name: "${name} controller Restart"
  - platform: factory_reset
    name: Restart with Factory Default Settings
    
# interval:
#   - interval: 10s
#     id: read_sensors
#     then: 
#       - component.update: sensor_bl0906
#       - delay: 0.5s
#       - component.update: sensor_pzem

time:
  - platform: sntp
    id: ha_time
    servers: ntp.aliyun.com
text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
    ssid:
      name: Energy_meter Connected SSID
    bssid:
      name: Energy_meter Connected BSSID
    mac_address:
      name: Energy_meter Mac Wifi Address
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address 
  - platform: debug
    device:
      name: "Device Info"
    reset_reason:
      name: "Reset Reason"
    