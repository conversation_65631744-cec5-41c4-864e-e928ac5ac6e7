# 简化测试配置 - 修复abort()问题后的测试
# 只包含基本功能，避免内存问题

esphome:
  name: "bl0906-memory-test"
  friendly_name: "BL0906 Memory Test"

preferences:
  flash_write_interval: 10min

esp32:
  board: esp32dev
  framework:
    type: arduino

# 降低日志级别减少内存消耗
logger:
  level: INFO
  logs:
    sensor: WARN
    bl0906_factory: INFO
    bl0906_number: WARN

# Enable Home Assistant API
api:
  encryption:
    key: "c8OIaeVFYiA5olgZlIJnxVjWeIISZb2l5SKs6nUI1fE="

ota:
  - platform: esphome
  
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  ap:
    ssid: "BL0906-Memory-Test"
    password: ""

captive_portal:

external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s

uart:
  - id: uart_bus1
    rx_pin: 33
    tx_pin: 32
    baud_rate: 9600

# 简化配置：只使用一个BL0906芯片，减少校准组件数量
bl0906_factory:
  - id: sensor_bl0906
    chip_model: bl0906
    communication: uart
    uart_id: uart_bus1
    update_interval: 30s  # 延长更新间隔
    instance_id: 0x906B0001
    calibration_mode: false  # 禁用校准模式减少组件
    calibration:
      enabled: false

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    # 只配置基本传感器
    frequency:
      name: 'BL0906 Frequency'
    temperature:
      name: 'BL0906 Temperature'
    voltage:
      name: 'BL0906 Voltage'
    
    # 只配置前3个通道减少内存占用
    ch1:
      current:
        name: "CH1 Current"
      power:
        name: "CH1 Power"
      energy:
        name: "CH1 Energy"
        
    ch2:
      current:
        name: "CH2 Current"
      power:
        name: "CH2 Power"
      energy:
        name: "CH2 Energy"
        
    ch3:
      current:
        name: "CH3 Current"
      power:
        name: "CH3 Power"
      energy:
        name: "CH3 Energy"

  - platform: debug
    free:
      name: "Heap Free"
    block:
      name: "Heap Max Block"

debug:
  update_interval: 10s

switch:
  - platform: restart
    name: "Restart"

# 内存监控传感器
  - platform: template
    name: "Free Heap"
    lambda: |-
      return ESP.getFreeHeap();
    update_interval: 30s
    unit_of_measurement: "bytes"

  - platform: template
    name: "Min Free Heap"
    lambda: |-
      return ESP.getMinFreeHeap();
    update_interval: 60s
    unit_of_measurement: "bytes"

# 简化的校准数字组件 - 只保留几个
number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "CH1 Current Gain"
      mode: box
    chgn_decimal_2:
      name: "CH2 Current Gain"
      mode: box

# 基本按钮
button:
  # 简化的诊断按钮
  - platform: template
    name: "Simple NVS Diagnose"
    on_press:
      lambda: |-
        ESP_LOGI("test", "开始简化NVS诊断...");
        id(sensor_bl0906)->diagnose_nvs_storage();

  # 简化的恢复按钮
  - platform: template
    name: "Simple Force Recovery"
    on_press:
      lambda: |-
        ESP_LOGI("test", "开始简化强制恢复...");
        id(sensor_bl0906)->force_recover_calibration_data();

  # 内存状态按钮
  - platform: template
    name: "Memory Status"
    on_press:
      lambda: |-
        ESP_LOGI("memory", "=== 内存状态 ===");
        ESP_LOGI("memory", "Free Heap: %d bytes", ESP.getFreeHeap());
        ESP_LOGI("memory", "Max Alloc: %d bytes", ESP.getMaxAllocHeap());
        ESP_LOGI("memory", "Min Free: %d bytes", ESP.getMinFreeHeap());
        ESP_LOGI("memory", "Heap Size: %d bytes", ESP.getHeapSize());

# 状态传感器
text_sensor:
  - platform: template
    name: "System Status"
    lambda: |-
      uint32_t free_heap = ESP.getFreeHeap();
      if (free_heap > 50000) {
        return {"内存充足: " + to_string(free_heap) + " bytes"};
      } else if (free_heap > 20000) {
        return {"内存正常: " + to_string(free_heap) + " bytes"};
      } else {
        return {"内存不足: " + to_string(free_heap) + " bytes"};
      }
    update_interval: 30s

# 启动时检查
esphome:
  on_boot:
    priority: 600
    then:
      - lambda: |-
          ESP_LOGI("boot", "=== 简化配置启动完成 ===");
          ESP_LOGI("boot", "Free Heap: %d bytes", ESP.getFreeHeap());
          ESP_LOGI("boot", "实例ID: 0x%08X", id(sensor_bl0906)->get_instance_id());

# 内存监控
interval:
  - interval: 60s
    then:
      - lambda: |-
          uint32_t free_heap = ESP.getFreeHeap();
          if (free_heap < 20000) {
            ESP_LOGW("memory", "内存不足警告: %d bytes", free_heap);
          }
          if (free_heap < 10000) {
            ESP_LOGE("memory", "内存严重不足: %d bytes", free_heap);
          }
