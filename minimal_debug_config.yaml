# 极简调试配置 - 用于诊断abort()问题
# 逐步添加功能来定位问题源

esphome:
  name: 16-ch-monitor-test
  platform: ESP32
  board: esp32dev
  # 启用调试信息
  platformio_options:
    board_build.f_cpu: 160000000L
    build_flags:
      - -DCORE_DEBUG_LEVEL=3
      - -DCONFIG_ARDUHAL_LOG_COLORS=1
    monitor_filters:
      - esp32_exception_decoder

# 最基本的WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  # 禁用自动重连以减少复杂性
  reboot_timeout: 0s
  power_save_mode: NONE  # 禁用节能模式避免问题

# 基本日志 - 启用详细调试
logger:
  level: VERBOSE
  baud_rate: 115200

# 基本API - 无加密以减少复杂性
api:
  reboot_timeout: 0s

# 禁用OTA以减少内存使用
# ota:

# 禁用Web服务器
# web_server:

# 状态LED用于指示
status_led:
  pin: GPIO2

# 基本传感器用于测试
sensor:
  - platform: uptime
    name: "Uptime"
    update_interval: 30s

  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  - platform: internal_temperature
    name: "ESP32 Temperature"
    update_interval: 30s

# 内存监控
  - platform: template
    name: "Free Heap"
    lambda: |-
      return ESP.getFreeHeap();
    update_interval: 5s
    unit_of_measurement: "bytes"

  - platform: template
    name: "Max Alloc Heap"
    lambda: |-
      return ESP.getMaxAllocHeap();
    update_interval: 5s
    unit_of_measurement: "bytes"

# 基本按钮用于测试
button:
  - platform: template
    name: "Test Memory"
    on_press:
      lambda: |-
        ESP_LOGI("debug", "=== 内存状态 ===");
        ESP_LOGI("debug", "Free Heap: %d bytes", ESP.getFreeHeap());
        ESP_LOGI("debug", "Max Alloc: %d bytes", ESP.getMaxAllocHeap());
        ESP_LOGI("debug", "Min Free Heap: %d bytes", ESP.getMinFreeHeap());
        ESP_LOGI("debug", "Heap Size: %d bytes", ESP.getHeapSize());

  - platform: template
    name: "Test Basic Function"
    on_press:
      lambda: |-
        ESP_LOGI("debug", "基本功能测试通过");

  - platform: restart
    name: "Safe Restart"

# 简单的文本传感器
text_sensor:
  - platform: template
    name: "System Status"
    lambda: |-
      return {"Free: " + to_string(ESP.getFreeHeap()) + " bytes"};
    update_interval: 10s

# 监控重启原因
  - platform: template
    name: "Reset Reason"
    lambda: |-
      esp_reset_reason_t reason = esp_reset_reason();
      switch(reason) {
        case ESP_RST_POWERON: return {"Power On"};
        case ESP_RST_EXT: return {"External Reset"};
        case ESP_RST_SW: return {"Software Reset"};
        case ESP_RST_PANIC: return {"Exception/Panic"};
        case ESP_RST_INT_WDT: return {"Interrupt Watchdog"};
        case ESP_RST_TASK_WDT: return {"Task Watchdog"};
        case ESP_RST_WDT: return {"Other Watchdog"};
        case ESP_RST_DEEPSLEEP: return {"Deep Sleep"};
        case ESP_RST_BROWNOUT: return {"Brownout"};
        case ESP_RST_SDIO: return {"SDIO Reset"};
        default: return {"Unknown"};
      }

# 启动时的内存检查
esphome:
  on_boot:
    priority: 800
    then:
      - lambda: |-
          ESP_LOGI("boot", "=== 启动时内存状态 ===");
          ESP_LOGI("boot", "Free Heap: %d bytes", ESP.getFreeHeap());
          ESP_LOGI("boot", "Max Alloc: %d bytes", ESP.getMaxAllocHeap());
          ESP_LOGI("boot", "启动完成，系统稳定");

# 定期内存检查
interval:
  - interval: 30s
    then:
      - lambda: |-
          uint32_t free_heap = ESP.getFreeHeap();
          if (free_heap < 10000) {  // 少于10KB时警告
            ESP_LOGW("memory", "内存不足警告: %d bytes", free_heap);
          }
          if (free_heap < 5000) {   // 少于5KB时错误
            ESP_LOGE("memory", "内存严重不足: %d bytes", free_heap);
          }
