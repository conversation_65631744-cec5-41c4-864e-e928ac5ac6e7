#include "bl0906_log_interface.h"
#include <time.h>
#include <string.h>

// 静态变量
static bl0906_log_callback_t g_log_callback = bl0906_log_default;
static bl0906_log_level_t g_log_level = BL0906_LOG_LEVEL_INFO;

// 级别名称映射
static const char* level_names[] = {
    "NONE",
    "ERROR",
    "WARN", 
    "INFO",
    "DEBUG",
    "VERBOSE"
};

// 默认日志实现
void bl0906_log_default(bl0906_log_level_t level, const char* tag, const char* format, va_list args) {
    if (level > g_log_level) {
        return;
    }
    
    // 获取时间戳
    time_t now;
    time(&now);
    struct tm* tm_info = localtime(&now);
    
    // 输出格式：[时间] [级别] [标签] 消息
    printf("[%02d:%02d:%02d] [%s] [%s] ", 
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec,
           level < 6 ? level_names[level] : "UNKNOWN",
           tag ? tag : "");
    
    vprintf(format, args);
    printf("\n");
    fflush(stdout);
}

// 设置日志回调函数
void bl0906_log_set_callback(bl0906_log_callback_t callback) {
    g_log_callback = callback ? callback : bl0906_log_default;
}

// 设置日志级别
void bl0906_log_set_level(bl0906_log_level_t level) {
    g_log_level = level;
}

// 日志输出函数
void bl0906_log_printf(bl0906_log_level_t level, const char* tag, const char* format, ...) {
    if (level > g_log_level || !g_log_callback) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    g_log_callback(level, tag, format, args);
    va_end(args);
} 