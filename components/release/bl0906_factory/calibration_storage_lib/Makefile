# BL0906 校准存储库 Makefile - 支持多架构交叉编译

# 默认变量定义
TARGET ?= libcalibration_storage.a
SRCDIR = src
INCDIR = include
OBJDIR = obj

# 编译器配置（可通过命令行覆盖）
CC ?= g++
AR ?= ar
CFLAGS ?= -I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions

# 源文件和目标文件
SOURCES := $(wildcard $(SRCDIR)/*.cpp)
OBJECTS := $(patsubst $(SRCDIR)/%.cpp, $(OBJDIR)/%.o, $(SOURCES))

# 颜色定义
GREEN = \033[0;32m
YELLOW = \033[1;33m
NC = \033[0m

# 默认目标
all: $(TARGET)

# 创建静态库
$(TARGET): $(OBJECTS) | $(OBJDIR)
	@echo -e "$(YELLOW)创建静态库: $(TARGET)$(NC)"
	$(AR) rcs $@ $^
	@echo -e "$(GREEN)✓ 校准存储库构建完成: $(TARGET)$(NC)"

# 编译源文件
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	@echo -e "$(YELLOW)编译: $<$(NC)"
	$(CC) $(CFLAGS) -c $< -o $@

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

# 多架构构建目标
esp32:
	$(MAKE) CC=xtensa-esp32-elf-g++ AR=xtensa-esp32-elf-ar \
	        CFLAGS="-I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions -mlongcalls" \
	        TARGET=libcalibration_storage_esp32.a

esp32s2:
	$(MAKE) CC=xtensa-esp32s2-elf-g++ AR=xtensa-esp32s2-elf-ar \
	        CFLAGS="-I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions -mlongcalls" \
	        TARGET=libcalibration_storage_esp32s2.a

esp32s3:
	$(MAKE) CC=xtensa-esp32s3-elf-g++ AR=xtensa-esp32s3-elf-ar \
	        CFLAGS="-I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions -mlongcalls" \
	        TARGET=libcalibration_storage_esp32s3.a

esp32c3:
	$(MAKE) CC=riscv32-esp-elf-g++ AR=riscv32-esp-elf-ar \
	        CFLAGS="-I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions -march=rv32imc -mabi=ilp32" \
	        TARGET=libcalibration_storage_esp32c3.a

esp32c6:
	$(MAKE) CC=riscv32-esp-elf-g++ AR=riscv32-esp-elf-ar \
	        CFLAGS="-I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions -march=rv32imac -mabi=ilp32" \
	        TARGET=libcalibration_storage_esp32c6.a

esp32h2:
	$(MAKE) CC=riscv32-esp-elf-g++ AR=riscv32-esp-elf-ar \
	        CFLAGS="-I$(INCDIR) -Wall -Os -std=c++17 -fno-rtti -fno-exceptions -march=rv32imac -mabi=ilp32" \
	        TARGET=libcalibration_storage_esp32h2.a

# 构建所有架构
all-archs: esp32 esp32s2 esp32s3 esp32c3 esp32c6 esp32h2

# 清理
clean:
	rm -rf $(OBJDIR) libcalibration_storage*.a
	@echo -e "$(GREEN)✓ 清理完成$(NC)"

# 显示库信息
info:
	@echo "=== 校准存储库信息 ==="
	@ls -la libcalibration_storage*.a 2>/dev/null || echo "未找到库文件"

# 帮助信息
help:
	@echo "BL0906 校准存储库构建系统"
	@echo ""
	@echo "可用目标:"
	@echo "  all        - 构建默认库 ($(TARGET))"
	@echo "  esp32      - 构建ESP32版本"
	@echo "  esp32s2    - 构建ESP32-S2版本"
	@echo "  esp32s3    - 构建ESP32-S3版本"
	@echo "  esp32c3    - 构建ESP32-C3版本"
	@echo "  esp32c6    - 构建ESP32-C6版本"
	@echo "  esp32h2    - 构建ESP32-H2版本"
	@echo "  all-archs  - 构建所有架构版本"
	@echo "  clean      - 清理构建文件"
	@echo "  info       - 显示库信息"
	@echo "  help       - 显示此帮助"

.PHONY: all esp32 esp32s2 esp32s3 esp32c3 esp32c6 esp32h2 all-archs clean info help