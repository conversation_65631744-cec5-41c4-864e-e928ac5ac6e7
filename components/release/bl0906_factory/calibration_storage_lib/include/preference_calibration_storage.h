#pragma once
#include "calibration_storage_base.h"
#include <cstddef>

namespace esphome {
namespace bl0906_factory {

class PreferenceInterface {
public:
    virtual bool read(const char* key, void* data, size_t len) = 0;
    virtual ~PreferenceInterface() {}
};

class PreferenceCalibrationStorage : public CalibrationStorageBase {
public:
    PreferenceCalibrationStorage(PreferenceInterface* pref, const char* key, size_t data_len);

    // 实现CalibrationStorageInterface的纯虚函数
    bool init() override;
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;
    bool verify() override;
    bool erase() override;
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override;
    std::string get_storage_type() const override;

    ~PreferenceCalibrationStorage() override;

protected:
    // 实现CalibrationStorageBase的纯虚函数
    bool read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) override;

private:
    PreferenceInterface* pref_;
    const char* key_;
    size_t data_len_;
};

}  // namespace bl0906_factory
}  // namespace esphome