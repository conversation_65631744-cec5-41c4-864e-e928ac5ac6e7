#pragma once
// 必须先包含接口头文件，保证 CalibrationEntry 类型可见
#include "calibration_storage_interface.h"
#include <vector>
#include <cstdint>
#include <cstring>
#include <algorithm>

namespace esphome {
namespace bl0906_factory {

// 只读场景下的通用存储基类
class CalibrationStorageBase : public CalibrationStorageInterface {
public:
    virtual ~CalibrationStorageBase() = default;
    // 只读接口
    bool load_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries);
protected:
    // 通用的数据验证方法
    bool validate_instance_id(uint32_t instance_id) const;
    // 通用的数据反序列化方法
    bool deserialize_entries(const uint8_t* buffer, size_t buffer_size, std::vector<CalibrationEntry>& entries) const;
    // 只读场景下的原始数据读取接口
    virtual bool read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) = 0;
    // 可选：日志
    void log_instance_operation(const char* operation, uint32_t instance_id, bool success, const char* details = nullptr) const;
    void log_data_validation_error(const char* field, uint32_t value, uint32_t max_value) const;
    // 可选：配置
    virtual size_t get_max_entries_per_instance() const { return 64; }
    virtual const char* get_log_tag() const { return "calibration_storage"; }
private:
    static constexpr size_t ENTRY_SERIALIZED_SIZE = 3;
    static constexpr uint32_t MIN_VALID_INSTANCE_ID = 0x00000001;
    static constexpr uint32_t MAX_VALID_INSTANCE_ID = 0xFFFFFFFE;
};

}  // namespace bl0906_factory
}  // namespace esphome 