# 校准数据持久化存储预编译库实现计划

## 1. 目标

- 将校准数据持久化存储相关的实现（如EEPROM、Preference等）从主工程中抽离，形成独立的、可复用的静态库（.a）。
- 该库仅通过接口头文件与主工程交互，便于在不同项目/固件中集成。

---

## 2. 主要步骤

### 2.1 明确功能边界与接口（细化：发布版只读场景）

1. 明确功能需求
   - 发布版固件的校准数据持久化存储只需要读取（load），不涉及写入（save）等操作。
   - 可选：如有需要，可实现初始化（init）、校验（validate）等只读相关操作。

2. 设计接口（头文件）
   - 只声明读取相关的接口方法，接口更简洁。
   - 示例接口设计：

```cpp
// calibration_storage_interface.h
class CalibrationStorage {
public:
    // 只读接口
    virtual bool load(void* data, size_t len) = 0;
    // 可选：初始化
    virtual bool init() { return true; }
    // 可选：校验
    virtual bool validate() { return true; }
    virtual ~CalibrationStorage() {}
};
```
> 如无特殊需求，`init`、`validate`可提供默认实现，子类可选择重写。

3. 明确接口与实现的分离
   - 只在头文件中声明接口，不包含任何实现细节。
   - 具体实现（如EEPROM、Preference等）在各自的cpp文件中完成，并继承该接口。

4. 明确哪些API对外暴露
   - 仅将接口头文件（如`calibration_storage_interface.h`）放入`include/`目录，供主工程和其他模块引用。
   - 内部实现细节仅在库内部使用，不对外暴露。

5. 设计接口的可扩展性
   - 预留虚析构函数，便于后续扩展。
   - 如有必要，可增加错误码、状态查询等接口。

6. 编写接口文档
   - 在接口头文件和README中，注释每个方法的用途、参数、返回值、异常情况等，便于他人理解和调用。

### 2.2 整理源文件（细化）

1. 确定需要迁移的源文件
   - 明确所有与校准数据只读持久化相关的实现文件，包括但不限于：
     - calibration_storage_base.cpp/h
     - i2c_eeprom_calibration_storage.cpp/h
     - preference_calibration_storage.cpp/h
   - 只迁移与“读取”相关的实现代码，去除或注释掉写入、删除等无关部分。

2. 统一放置到预编译库目录
   - 在 components/bl0906_factory/release/calibration_storage_lib/ 下建立如下结构：
     - include/：放置对外暴露的接口头文件（如calibration_storage_interface.h）。
     - src/：放置所有实现文件（如calibration_storage_base.cpp、i2c_eeprom_calibration_storage.cpp等）。
   - 只将接口头文件放入include/，其余实现细节全部放入src/。

3. 清理和精简代码
   - 检查每个实现文件，只保留与读取相关的代码，去除或注释掉写入、删除等无关实现。
   - 检查头文件，确保只暴露load、init、validate等只读相关接口。
   - 移除与主工程强耦合的部分（如全局变量、宏、外部依赖等），如有必要通过接口/回调注入。

### 2.3 依赖解耦（细化）

1. 识别所有外部依赖
   - 检查所有实现文件（如i2c_eeprom_calibration_storage.cpp、preference_calibration_storage.cpp等）中涉及的外部依赖，包括：
     - 硬件驱动（如I2C、SPI等底层通信接口）
     - 日志系统
     - 配置管理
     - 主工程的全局变量、宏、工具函数等

2. 依赖解耦的基本原则
   - 不直接依赖主工程实现，所有依赖通过接口、回调或抽象类注入。
   - 只依赖标准C/C++库或本库内部代码，避免外部强耦合。

3. 具体解耦方法

   3.1 硬件驱动依赖
   - 将底层硬件操作（如I2C/SPI读写）抽象为接口或回调函数，由主工程实现并在初始化时注入。
   - 示例（以I2C为例）：

```cpp
// 定义I2C操作接口
class I2CInterface {
public:
    virtual bool read(uint8_t addr, uint8_t* data, size_t len) = 0;
    virtual bool write(uint8_t addr, const uint8_t* data, size_t len) = 0;
    virtual ~I2CInterface() {}
};

// 在存储实现类中持有I2CInterface指针
class I2CEepromCalibrationStorage : public CalibrationStorage {
public:
    I2CEepromCalibrationStorage(I2CInterface* i2c) : i2c_(i2c) {}
    // ...
private:
    I2CInterface* i2c_;
};
```

   3.2 日志与错误处理
   - 日志输出、错误报告等同样通过接口或回调注入，不直接依赖主工程日志系统。
   - 示例：

```cpp
typedef void (*LogCallback)(const char* msg);
class CalibrationStorage {
public:
    void set_log_callback(LogCallback cb) { log_cb_ = cb; }
protected:
    void log(const char* msg) { if (log_cb_) log_cb_(msg); }
private:
    LogCallback log_cb_ = nullptr;
};
```

   3.3 配置与参数
   - 需要外部配置的参数（如存储地址、校准数据长度等）通过构造函数或初始化方法传入，不直接读取主工程配置。

   3.4 宏与全局变量
   - 移除对主工程宏定义、全局变量的依赖，必要时通过参数传递或接口获取。

4. 代码示例与注释
   - 在接口和实现文件中，详细注释每个依赖的注入方式和用途，便于后续维护和集成。

5. 检查与验证
   - 检查所有实现文件，确保无主工程直接依赖。
   - 编译时仅依赖本库和标准库，确保解耦彻底。

### 2.4 编写构建脚本（细化：Makefile方式）

1. 构建目标
   - 将 src/ 目录下的所有实现文件编译为一个静态库（如 libcalibration_storage.a）。
   - 头文件统一放在 include/ 目录，供主工程引用。

2. Makefile 结构与内容
   - 推荐的 Makefile 结构如下：

```makefile
# 变量定义
TARGET = libcalibration_storage.a
SRCDIR = src
INCDIR = include
OBJDIR = obj

CC = g++
CFLAGS = -I$(INCDIR) -Wall -O2 -std=c++20

SOURCES := $(wildcard $(SRCDIR)/*.cpp)
OBJECTS := $(patsubst $(SRCDIR)/%.cpp, $(OBJDIR)/%.o, $(SOURCES))

# 默认目标
all: $(TARGET)

# 生成静态库
$(TARGET): $(OBJECTS)
	ar rcs $@ $^

# 编译源文件
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 创建obj目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

# 清理
clean:
	rm -rf $(OBJDIR) $(TARGET)
```

3. 使用说明
   - 在 calibration_storage_lib 目录下执行 `make`，即可生成 libcalibration_storage.a。
   - 主工程在链接时添加该静态库，并将 include/ 目录加入头文件搜索路径。
   - 如需清理中间文件和库文件，执行 `make clean`。

4. 可选扩展
   - 如需支持交叉编译，可在 Makefile 中通过变量切换编译器（如 CC=xtensa-esp32-elf-g++ 等）。
   - 可根据实际需求调整 CFLAGS、SRCDIR、INCDIR 等变量。

5. 集成说明
   - 在 README.md 中补充编译和集成方法，便于主工程开发者快速上手。

### 2.5 头文件整理

- 将所有对外接口头文件统一放在`release/calibration_storage_lib/include/`目录下，便于主工程引用。

### 2.6 集成与测试

- 在主工程的构建脚本中，添加对该静态库和头文件的引用。
- 编写简单的集成测试，确保功能无误。

### 2.7 文档补充

- 在`release/calibration_storage_lib/README.md`中补充使用说明，包括接口文档、依赖说明、集成方法等。

---

## 3. 目录结构建议

```plaintext
components/bl0906_factory/release/calibration_storage_lib/
  ├── include/
  │   ├── calibration_storage_interface.h
  │   ├── calibration_storage_base.h
  │   └── ...（其他对外头文件）
  ├── src/
  │   ├── calibration_storage_base.cpp
  │   ├── i2c_eeprom_calibration_storage.cpp
  │   ├── preference_calibration_storage.cpp
  │   └── ...（其他实现文件）
  ├── Makefile 或 CMakeLists.txt
  └── README.md
```

---

## 4. 关键注意事项

- **接口抽象**：所有与硬件、日志等相关的操作都应通过接口/回调实现，避免直接依赖主工程实现。
- **依赖最小化**：库本身不应依赖主工程的其他模块，便于独立复用。
- **版本管理**：建议在库目录下维护`VERSION`文件，便于后续升级和维护。

---

## 5. ESPHome 框架下的实现建议

### 推荐方式
在 ESPHome 框架下，建议采用**接口/抽象类**的方式实现校准数据存储的解耦与扩展。

### 理由
- ESPHome 采用 C++ 面向对象设计，组件体系普遍使用继承和多态，接口/抽象类方式与其风格一致。
- 便于后续扩展不同存储方式（如 EEPROM、Preference 等），主流程只依赖接口，易于维护。
- 类型安全，易于调试和集成。

### 简单示例
```cpp
// 定义接口/抽象类
class CalibrationStorage {
public:
    virtual bool save(const void* data, size_t len) = 0;
    virtual bool load(void* data, size_t len) = 0;
    virtual ~CalibrationStorage() {}
};

// 实现具体存储方式
class EEPROMCalibrationStorage : public CalibrationStorage {
    // ...具体实现...
};

class PreferenceCalibrationStorage : public CalibrationStorage {
    // ...具体实现...
};
```

主流程只依赖 `CalibrationStorage` 接口，具体实现可灵活切换，便于与 ESPHome 组件体系集成。

如需进一步细化每一步的操作或需要接口抽象示例，请随时告知！ 

---

## 6. 存储实现的独立静态库与ESPHome集成方案

1. **独立静态库设计**
   - 将 eeprom 和 preference 两种持久化存储实现分别编译为独立的静态库（如 `libcalibration_storage_eeprom.a` 和 `libcalibration_storage_preference.a`）。
   - 每个库只包含对应实现的代码和接口，互不干扰。

2. **头文件组织**
   - 公共接口头文件（如 `calibration_storage_interface.h`）放在各自 `include/` 目录，供两种实现共用。
   - 各自实现的头文件（如 `i2c_eeprom_calibration_storage.h`、`preference_calibration_storage.h`）分别放在各自库的 `src/` 或 `include/` 下。

3. **目录结构建议**

```plaintext
calibration_storage_lib/
  ├── eeprom/
  │   ├── include/
  │   │   └── calibration_storage_interface.h
  │   ├── src/
  │   │   └── i2c_eeprom_calibration_storage.cpp
  │   └── Makefile  # 生成 libcalibration_storage_eeprom.a
  ├── preference/
  │   ├── include/
  │   │   └── calibration_storage_interface.h
  │   ├── src/
  │   │   └── preference_calibration_storage.cpp
  │   └── Makefile  # 生成 libcalibration_storage_preference.a
```

4. **ESPHome固件集成方式**
   - 在编译 ESPHome 固件时，根据实际需求只链接一个库（如 `libcalibration_storage_eeprom.a` 或 `libcalibration_storage_preference.a`）。
   - 这样固件中只会包含所需实现，未用到的实现完全不会被编译进固件，进一步精简和隔离。
   - 公共接口头文件统一，便于主工程无缝切换实现。

5. **集成说明**
   - 在 README.md 中补充如何选择和链接不同存储实现的说明，便于主工程开发者根据实际需求集成。 