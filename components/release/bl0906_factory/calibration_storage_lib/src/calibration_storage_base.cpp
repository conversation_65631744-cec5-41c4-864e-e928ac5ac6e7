#include "../include/calibration_storage_base.h"
#include "../include/calibration_storage_interface.h"
#include <string>

namespace esphome {
namespace bl0906_factory {

bool CalibrationStorageBase::load_instance(uint32_t instance_id, std::vector<esphome::bl0906_factory::CalibrationEntry>& entries) {
    if (!validate_instance_id(instance_id)) {
        log_data_validation_error("instance_id", instance_id, MAX_VALID_INSTANCE_ID);
        return false;
    }
    uint8_t buffer[512];
    size_t buffer_size = sizeof(buffer);
    if (!read_raw_data(instance_id, buffer, buffer_size)) {
        log_instance_operation("读取", instance_id, false, "原始数据读取失败");
        return false;
    }
    if (!deserialize_entries(buffer, buffer_size, entries)) {
        log_instance_operation("反序列化", instance_id, false, "数据格式错误");
        return false;
    }
    log_instance_operation("读取", instance_id, true, (std::string("成功读取 ") + std::to_string(entries.size()) + " 个条目").c_str());
    return true;
}

bool CalibrationStorageBase::validate_instance_id(uint32_t instance_id) const {
    return instance_id >= MIN_VALID_INSTANCE_ID && instance_id <= MAX_VALID_INSTANCE_ID;
}

bool CalibrationStorageBase::deserialize_entries(const uint8_t* buffer, size_t buffer_size, std::vector<esphome::bl0906_factory::CalibrationEntry>& entries) const {
    if (buffer_size < sizeof(uint16_t)) return false;
    entries.clear();
    uint16_t potential_count;
    memcpy(&potential_count, buffer, sizeof(uint16_t));
    size_t expected_preferences_size = sizeof(uint16_t) + potential_count * ENTRY_SERIALIZED_SIZE;
    bool is_preferences_format = (
        potential_count > 0 && potential_count <= get_max_entries_per_instance() &&
        buffer_size >= expected_preferences_size && buffer_size <= expected_preferences_size + 16
    );
    if (is_preferences_format) {
        const uint8_t* data_ptr = buffer + sizeof(uint16_t);
        for (uint16_t i = 0; i < potential_count; i++) {
            esphome::bl0906_factory::CalibrationEntry entry;
            entry.register_addr = data_ptr[i * ENTRY_SERIALIZED_SIZE];
            entry.value = static_cast<int16_t>((data_ptr[i * ENTRY_SERIALIZED_SIZE + 1] << 8) | data_ptr[i * ENTRY_SERIALIZED_SIZE + 2]);
            if (entry.register_addr != 0) entries.push_back(entry);
        }
    } else if (buffer_size > 4) {
        const uint8_t* data_ptr = buffer + 4;
        size_t max_entries = (buffer_size - 4) / ENTRY_SERIALIZED_SIZE;
        for (size_t i = 0; i < max_entries; i++) {
            uint8_t reg_addr = data_ptr[i * ENTRY_SERIALIZED_SIZE];
            if (reg_addr == 0) break;
            esphome::bl0906_factory::CalibrationEntry entry;
            entry.register_addr = reg_addr;
            entry.value = static_cast<int16_t>((data_ptr[i * ENTRY_SERIALIZED_SIZE + 1] << 8) | (data_ptr[i * ENTRY_SERIALIZED_SIZE + 2]));
            entries.push_back(entry);
        }
    } else {
        return false;
    }
    return true;
}

void CalibrationStorageBase::log_instance_operation(const char* operation, uint32_t instance_id, bool success, const char* details) const {
    // 这里只做简单输出，实际可对接主工程日志
}
void CalibrationStorageBase::log_data_validation_error(const char* field, uint32_t value, uint32_t max_value) const {
    // 这里只做简单输出，实际可对接主工程日志
}

}  // namespace bl0906_factory
}  // namespace esphome 