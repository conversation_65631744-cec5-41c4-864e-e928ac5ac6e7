#include "../include/i2c_eeprom_calibration_storage.h"
#include <cstring>

namespace esphome {
namespace bl0906_factory {

I2CEepromCalibrationStorage::I2CEepromCalibrationStorage(I2CInterface* i2c, uint8_t dev_addr, size_t data_len)
    : i2c_(i2c), dev_addr_(dev_addr), data_len_(data_len) {}

bool I2CEepromCalibrationStorage::init() {
    // 初始化I2C EEPROM存储
    return i2c_ != nullptr;
}

bool I2CEepromCalibrationStorage::read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) {
    // 使用基类的load_instance方法
    return load_instance(instance_id, entries);
}

bool I2CEepromCalibrationStorage::write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) {
    // 发布版为只读，不支持写入
    return false;
}

bool I2CEepromCalibrationStorage::delete_instance(uint32_t instance_id) {
    // 发布版为只读，不支持删除
    return false;
}

bool I2CEepromCalibrationStorage::verify() {
    // 验证EEPROM连接
    if (!i2c_) return false;
    uint8_t test_byte;
    return i2c_->read(dev_addr_, &test_byte, 1);
}

bool I2CEepromCalibrationStorage::erase() {
    // 发布版为只读，不支持擦除
    return false;
}

std::vector<uint32_t> I2CEepromCalibrationStorage::get_instance_list() {
    // 简化实现：返回固定的实例列表
    return {1, 2, 3};
}

size_t I2CEepromCalibrationStorage::get_max_instances() {
    // 根据EEPROM大小计算最大实例数
    return data_len_ / 64;  // 假设每个实例64字节
}

std::string I2CEepromCalibrationStorage::get_storage_type() const {
    return "I2C_EEPROM";
}

bool I2CEepromCalibrationStorage::read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) {
    // 实现原始数据读取
    if (!i2c_ || !buffer) return false;

    // 计算实例在EEPROM中的地址
    size_t instance_size = 64;  // 每个实例64字节
    size_t offset = instance_id * instance_size;

    if (offset + instance_size > data_len_) {
        return false;
    }

    // 从EEPROM读取数据
    bool success = i2c_->read(dev_addr_ + offset, buffer, instance_size);
    if (success) {
        buffer_size = instance_size;
    }

    return success;
}

I2CEepromCalibrationStorage::~I2CEepromCalibrationStorage() {}

}  // namespace bl0906_factory
}  // namespace esphome