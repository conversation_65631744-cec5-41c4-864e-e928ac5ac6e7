#include "../include/preference_calibration_storage.h"
#include <cstring>

namespace esphome {
namespace bl0906_factory {

PreferenceCalibrationStorage::PreferenceCalibrationStorage(PreferenceInterface* pref, const char* key, size_t data_len)
    : pref_(pref), key_(key), data_len_(data_len) {}

bool PreferenceCalibrationStorage::init() {
    return pref_ != nullptr && key_ != nullptr;
}

bool PreferenceCalibrationStorage::read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) {
    return load_instance(instance_id, entries);
}

bool PreferenceCalibrationStorage::write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) {
    return false; // 只读模式
}

bool PreferenceCalibrationStorage::delete_instance(uint32_t instance_id) {
    return false; // 只读模式
}

bool PreferenceCalibrationStorage::verify() {
    return pref_ != nullptr && key_ != nullptr;
}

bool PreferenceCalibrationStorage::erase() {
    return false; // 只读模式
}

std::vector<uint32_t> PreferenceCalibrationStorage::get_instance_list() {
    return {1}; // 简化实现：只有一个实例
}

size_t PreferenceCalibrationStorage::get_max_instances() {
    return 1; // Preference存储通常只有一个实例
}

std::string PreferenceCalibrationStorage::get_storage_type() const {
    return "PREFERENCE";
}

bool PreferenceCalibrationStorage::read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) {
    if (!pref_ || !key_ || !buffer || instance_id != 1) return false;

    bool success = pref_->read(key_, buffer, data_len_);
    if (success) {
        buffer_size = data_len_;
    }

    return success;
}

PreferenceCalibrationStorage::~PreferenceCalibrationStorage() {}

}  // namespace bl0906_factory
}  // namespace esphome