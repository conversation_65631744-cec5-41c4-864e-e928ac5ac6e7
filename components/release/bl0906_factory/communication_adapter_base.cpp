#include "communication_adapter_base.h"
#include "bl0906_log_interface.h"
#include "bl0906_hal_interface.h"

// 包含C接口以避免代码重复
extern "C" {
#include "bl0906_comm_common_api.h"
}

namespace bl0906_core {

static const char *const TAG = "comm_adapter_base";

// 获取C接口实例（避免代码重复）
static const bl0906_comm_common_api_t* get_c_api() {
    static const bl0906_comm_common_api_t* api = bl0906_get_common_api();
    return api;
}

// ========== 通用接口实现 ==========

std::string CommunicationAdapterBase::get_last_error() const {
  return last_error_message_;
}

void CommunicationAdapterBase::reset_error_state() {
  last_error_ = CommunicationError::SUCCESS;
  last_error_message_.clear();
}

CommunicationError CommunicationAdapterBase::get_last_error_code() const {
  return last_error_;
}

size_t CommunicationAdapterBase::get_success_count() const {
  return stats_.success_count;
}

size_t CommunicationAdapterBase::get_error_count() const {
  return stats_.error_count;
}

CommunicationStats CommunicationAdapterBase::get_statistics() const {
  return stats_;
}

void CommunicationAdapterBase::reset_statistics() {
  stats_ = CommunicationStats{};
}

// ========== 保护的通用方法 ==========

void CommunicationAdapterBase::set_error(CommunicationError error, const std::string& message) {
  last_error_ = error;
  last_error_message_ = message;
  
  // 更新统计信息
  update_statistics(false, error);
  
  // 记录错误日志
  BL0906_LOGE(TAG, "%s适配器错误: %s", get_adapter_type().c_str(), message.c_str());
}

void CommunicationAdapterBase::update_statistics(bool success, CommunicationError error) {
  if (success) {
    stats_.success_count++;
  } else {
    stats_.error_count++;
    stats_.last_error = error;
    stats_.last_error_timestamp = 0;  // 简化实现：不记录时间戳
    
    // 根据错误类型更新对应计数器
    switch (error) {
      case CommunicationError::TIMEOUT:
        stats_.timeout_count++;
        break;
      case CommunicationError::CHECKSUM_ERROR:
        stats_.checksum_error_count++;
        break;
      case CommunicationError::DEVICE_NOT_AVAILABLE:
        stats_.device_not_available_count++;
        break;
      case CommunicationError::HARDWARE_ERROR:
        stats_.hardware_error_count++;
        break;
      case CommunicationError::INVALID_RESPONSE:
        stats_.invalid_response_count++;
        break;
      default:
        break;
    }
  }
}

bool CommunicationAdapterBase::check_initialized() const {
  if (!initialized_) {
    BL0906_LOGW(TAG, "%s适配器未初始化", get_adapter_type().c_str());
    return false;
  }
  return true;
}

bool CommunicationAdapterBase::verify_register_value(int16_t expected_value, int32_t actual_value, uint8_t register_address) {
  // 简单的数值比较验证
  if (static_cast<int32_t>(expected_value) != actual_value) {
    BL0906_LOGW(TAG, "%s适配器寄存器 0x%02X 验证失败: 期望=%d, 实际=%d",
                       get_adapter_type().c_str(), register_address, expected_value, actual_value);
    return false;
  }
  return true;
}

void CommunicationAdapterBase::log_operation_start(const std::string& operation_name, uint8_t register_address, int32_t value) {
  if (value != 0) {
    BL0906_LOGD(TAG, "%s %s寄存器 0x%02X, 值=%d",
                     operation_name.c_str(), get_adapter_type().c_str(), register_address, value);
  } else {
    BL0906_LOGD(TAG, "%s %s寄存器 0x%02X",
                     operation_name.c_str(), get_adapter_type().c_str(), register_address);
  }
}

void CommunicationAdapterBase::log_operation_result(const std::string& operation_name, uint8_t register_address, bool success, int32_t value) {
  if (success) {
    if (value != 0) {
      BL0906_LOGI(TAG, "%s %s寄存器 0x%02X 成功, 值=%d",
                      operation_name.c_str(), get_adapter_type().c_str(), register_address, value);
    } else {
      BL0906_LOGI(TAG, "%s %s寄存器 0x%02X 成功",
                      operation_name.c_str(), get_adapter_type().c_str(), register_address);
    }
  } else {
    BL0906_LOGE(TAG, "%s %s寄存器 0x%02X 失败: %s",
                     operation_name.c_str(), get_adapter_type().c_str(), register_address, last_error_message_.c_str());
  }
}

// ========== 通用数据处理方法实现（发布版本，统一UART和SPI逻辑）==========

int32_t CommunicationAdapterBase::parse_register_response(uint8_t address, uint8_t data_h, uint8_t data_m, uint8_t data_l) {
  // 根据寄存器类型选择解析方式
  if (is_16bit_register(address)) {
    // 16位寄存器：只使用中低字节
    return convert_to_signed_16bit(data_m, data_l);
  } else if (is_unsigned_register(address)) {
    // 24位无符号寄存器
    return static_cast<int32_t>(convert_to_unsigned_24bit(data_h, data_m, data_l));
  } else {
    // 24位有符号寄存器
    return convert_to_signed_24bit(data_h, data_m, data_l);
  }
}

bool CommunicationAdapterBase::prepare_register_write_data(uint8_t address, int16_t value, uint8_t& data_h, uint8_t& data_m, uint8_t& data_l) {
  // 校准寄存器写入数据准备
  if (is_16bit_register(address)) {
    // 16位寄存器：高字节为0
    data_h = 0x00;
    data_m = (value >> 8) & 0xFF;
    data_l = value & 0xFF;
  } else {
    // 24位寄存器：扩展符号位
    if (value < 0) {
      // 负数：扩展符号位
      data_h = 0xFF;
      data_m = (value >> 8) & 0xFF;
      data_l = value & 0xFF;
    } else {
      // 正数：高字节为0
      data_h = 0x00;
      data_m = (value >> 8) & 0xFF;
      data_l = value & 0xFF;
    }
  }
  return true;
}

bool CommunicationAdapterBase::verify_write_operation(uint8_t address, int16_t expected_value) {
  // 读回寄存器值进行验证
  bool read_success = false;
  int32_t actual_value = read_register(address, &read_success);
  
  if (!read_success) {
    set_error(CommunicationError::INVALID_RESPONSE, "写入验证读取失败");
    return false;
  }
  
  // 验证结果（考虑16位和24位寄存器的差异）
  if (is_16bit_register(address)) {
    // 16位寄存器：只比较低16位
    int16_t actual_16bit = static_cast<int16_t>(actual_value & 0xFFFF);
    if (actual_16bit != expected_value) {
      set_error(CommunicationError::HARDWARE_ERROR, 
                "写入验证失败，期望值=" + std::to_string(expected_value) + 
                "，实际值=" + std::to_string(actual_16bit));
      return false;
    }
  } else {
    // 24位寄存器：扩展期望值到24位后比较
    int32_t expected_24bit = (expected_value < 0) ? (expected_value | 0xFF0000) : expected_value;
    if (actual_value != expected_24bit) {
      set_error(CommunicationError::HARDWARE_ERROR, 
                "写入验证失败，期望值=" + std::to_string(expected_24bit) + 
                "，实际值=" + std::to_string(actual_value));
      return false;
    }
  }
  
  return true;
}

bool CommunicationAdapterBase::is_16bit_register(uint8_t address) {
  // 使用C接口实现，避免代码重复
  const bl0906_comm_common_api_t* api = get_c_api();
  return api ? api->is_16bit_register(address) : false;
}

bool CommunicationAdapterBase::is_unsigned_register(uint8_t address) {
  // 使用C接口实现，避免代码重复
  const bl0906_comm_common_api_t* api = get_c_api();
  return api ? api->is_unsigned_register(address) : false;
}

int32_t CommunicationAdapterBase::convert_to_signed_24bit(uint8_t data_h, uint8_t data_m, uint8_t data_l) {
  // 使用C接口实现，避免代码重复
  const bl0906_comm_common_api_t* api = get_c_api();
  return api ? api->convert_to_signed_24bit(data_h, data_m, data_l) : 0;
}

uint32_t CommunicationAdapterBase::convert_to_unsigned_24bit(uint8_t data_h, uint8_t data_m, uint8_t data_l) {
  // 使用C接口实现，避免代码重复
  const bl0906_comm_common_api_t* api = get_c_api();
  return api ? api->convert_to_unsigned_24bit(data_h, data_m, data_l) : 0;
}

int16_t CommunicationAdapterBase::convert_to_signed_16bit(uint8_t data_m, uint8_t data_l) {
  // 使用C接口实现，避免代码重复
  const bl0906_comm_common_api_t* api = get_c_api();
  return api ? api->convert_to_signed_16bit(data_m, data_l) : 0;
}

} // namespace bl0906_core 