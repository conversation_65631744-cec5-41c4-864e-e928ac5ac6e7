#!/bin/bash
# BL0906 Factory 恢复发布版脚本
# 用途：从生产版配置恢复到发布版配置

echo "=== 恢复 BL0906 Factory 发布版配置 ==="

# 检查是否存在备份文件
if [ ! -f "__init_release_backup.py" ]; then
    echo "错误：未找到发布版备份文件 __init_release_backup.py"
    echo "可能的原因："
    echo "  1. 未运行过生产版构建脚本"
    echo "  2. 备份文件已被删除"
    echo ""
    echo "解决方案："
    echo "  如果确定当前为生产版配置，可以手动恢复："
    echo "  git checkout __init__.py"
    exit 1
fi

# 恢复发布版配置
echo "✓ 恢复发布版配置..."
cp __init_release_backup.py __init__.py

# 清理备份文件
echo "✓ 清理备份文件..."
rm __init_release_backup.py

# 验证恢复结果
if grep -q "AUTO_LOAD.*sensor.*number" __init__.py; then
    echo "错误：恢复失败，当前配置仍包含 number 组件"
    echo "请手动检查 __init__.py 文件"
    exit 1
fi

if ! grep -q "发布版电能计量组件" __init__.py; then
    echo "警告：恢复的配置可能不是标准发布版"
    echo "请检查 __init__.py 文件头部注释"
fi

echo "✓ 发布版配置恢复完成"

# 设置发布版环境变量
export BUILD_TYPE=release
export CPPFLAGS="-DBL0906_PRODUCTION_BUILD -DBL0906_READONLY_CALIBRATION -DBL0906_FAULT_TOLERANT"

echo ""
echo "=== 发布版特性确认 ==="
echo "✓ 移除 number 组件（校准调试功能）"
echo "✓ 容错运行模式"
echo "✓ 只读校准数据"
echo "✓ 简化配置"

echo ""
echo "=== 恢复完成 ==="
echo "当前配置已恢复为发布版，适用于最终用户部署"
echo "" 