#pragma once

#include "esphome/core/component.h"
#include "esphome/core/hal.h"
#include "esphome/core/log.h"
#include "esphome/components/sensor/sensor.h"
#include "esphome/components/i2c/i2c.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/spi/spi.h"

// 预编译库接口
#include "bl0906_core_api.h"

// 通信适配器接口（发布版：只需要接口定义）
#include "communication_adapter_interface.h"
// 注意：具体的适配器实现（uart/spi）已编译到静态库中
// 注意：EEPROM存储功能已编译到静态库中
#include <memory>

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "bl0906_factory";

// 芯片型号枚举（映射到预编译库）
enum class ChipModel {
  BL0906 = 0,
  BL0910 = 1
};

// 电压采样模式枚举（映射到预编译库）
enum class VoltageSamplingMode {
  TRANSFORMER = 0,
  RESISTOR_DIVIDER = 1
};

// 频率适配模式枚举（从生产版同步）
enum class FreqAdaptMode {
  OFF = 0,
  AUTO = 1,
  HZ60 = 2
};

// 传感器类型枚举（与生产版保持一致）
enum class SensorType {
  SENSOR_VOLTAGE = 0,
  SENSOR_FREQUENCY = 1,
  SENSOR_TEMPERATURE = 2,
  SENSOR_CURRENT = 3,
  SENSOR_POWER = 4,
  SENSOR_ENERGY = 5,
  SENSOR_POWER_SUM = 6,
  SENSOR_ENERGY_SUM = 7,
  SENSOR_TOTAL_ENERGY = 8
};

// EEPROM类型枚举
enum class EEPROMType {
  EEPROM_24C02 = 0,
  EEPROM_24C04 = 1,
  EEPROM_24C08 = 2,
  EEPROM_24C16 = 3
};

// 前向声明
class BL0906Wrapper;

/**
 * @brief 薄包装层主组件类（发布版）
 * 
 * 这个类作为ESPHome组件和预编译库之间的适配层，提供：
 * - 预编译库的初始化和管理
 * - 芯片参数的运行时配置
 * - 通信适配器的统一接口
 * - 传感器数据的读取和发布
 * - 电量持久化存储
 * - 频率自动适配
 */
class BL0906Wrapper : public PollingComponent {
 public:
  // 构造函数
  BL0906Wrapper() = default;

  // ESPHome组件生命周期
  void setup() override;
  void loop() override;
  void update() override;
  void dump_config() override;
  float get_setup_priority() const override { return setup_priority::DATA; }

  // 芯片配置（从生产版同步）
  void set_chip_model(ChipModel model) { chip_model_ = model; }
  ChipModel get_chip_model() const { return chip_model_; }
  
  // 通信适配器接口（从生产版同步）
  void set_communication_adapter(bl0906_core::CommunicationAdapterInterface* adapter);
  bl0906_core::CommunicationAdapterInterface* get_communication_adapter() const;

  // 发布版：通信适配器工厂方法（适配器实现在预编译库中）
  void create_uart_adapter(uart::UARTComponent* uart_component);
  void create_spi_adapter(spi::SPIComponent* spi_component, GPIOPin* cs_pin);
  
  // 传感器注册（简化版）
  void set_sensor(SensorType sensor_type, sensor::Sensor *sensor, int channel = 0);
  
  // 电量持久化配置（从生产版同步）
  void set_energy_persistence_enabled(bool enabled) { energy_persistence_enabled_ = enabled; }
  bool get_energy_persistence_enabled() const { return energy_persistence_enabled_; }
  
  // 频率适配配置（从生产版同步）
  void set_freq_adapt_mode(FreqAdaptMode mode) { freq_adapt_mode_ = mode; }
  FreqAdaptMode get_freq_adapt_mode() const { return freq_adapt_mode_; }
  
  // 电压采样模式配置
  void set_voltage_sampling_mode(VoltageSamplingMode mode) { voltage_sampling_mode_ = mode; }
  VoltageSamplingMode get_voltage_sampling_mode() const { return voltage_sampling_mode_; }
  
  // 实例ID配置（从生产版同步）
  void set_instance_id(uint32_t id) { instance_id_ = id; }
  uint32_t get_instance_id() const { return instance_id_; }
  
  // 容错模式配置（发布版特有）
  void set_fault_tolerant_mode(bool enabled) { fault_tolerant_mode_ = enabled; }
  bool get_fault_tolerant_mode() const { return fault_tolerant_mode_; }
  
  // I2C EEPROM存储配置
  void set_eeprom_storage(EEPROMType eeprom_type, uint8_t i2c_address);
  void set_i2c_parent(i2c::I2CBus *parent) { i2c_parent_ = parent; }
  bool is_eeprom_storage_enabled() const { return eeprom_storage_enabled_; }
  
  // 工具函数
  bool validate_register_address(uint8_t address);
  
  // 错误处理
  std::string get_last_error() const { return last_error_; }

 protected:
  // 预编译库回调函数（静态）
  static bl0906_result_t read_register_callback(uint8_t address, int32_t *value);
  static bl0906_result_t write_register_callback(uint8_t address, int16_t value);
  static bl0906_result_t send_raw_command_callback(const uint8_t *data, size_t length);
  
  // 实际的通信实现
  bl0906_result_t read_register_impl(uint8_t address, int32_t *value);
  bl0906_result_t write_register_impl(uint8_t address, int16_t value);
  bl0906_result_t send_raw_command_impl(const uint8_t *data, size_t length);

 private:
  // 芯片配置
  ChipModel chip_model_ = ChipModel::BL0906;
  VoltageSamplingMode voltage_sampling_mode_ = VoltageSamplingMode::TRANSFORMER;
  FreqAdaptMode freq_adapt_mode_ = FreqAdaptMode::OFF;
  
  // 通信适配器（从生产版同步）
  // 使用自定义删除器确保通过预编译库销毁适配器
  struct AdapterDeleter {
    void operator()(bl0906_core::CommunicationAdapterInterface* adapter) const;
  };
  std::unique_ptr<bl0906_core::CommunicationAdapterInterface, AdapterDeleter> comm_adapter_;
  
  // 功能开关
  bool energy_persistence_enabled_ = true;
  bool fault_tolerant_mode_ = true;  // 发布版默认启用容错模式
  bool freq_adapted_ = false;
  
  // 实例ID（从生产版同步）
  uint32_t instance_id_ = 0;
  
  // I2C EEPROM存储配置
  bool eeprom_storage_enabled_ = true;  // 发布版默认启用EEPROM存储
  EEPROMType eeprom_type_ = EEPROMType::EEPROM_24C08;  // 默认24C08
  uint8_t i2c_address_ = 0x50;  // 默认I2C地址
  i2c::I2CBus *i2c_parent_ = nullptr;
  
  // 传感器映射（简化版）
  sensor::Sensor *voltage_sensor_ = nullptr;
  sensor::Sensor *frequency_sensor_ = nullptr;
  sensor::Sensor *temperature_sensor_ = nullptr;
  sensor::Sensor *power_sum_sensor_ = nullptr;
  sensor::Sensor *energy_sum_sensor_ = nullptr;
  sensor::Sensor *current_sensors_[10] = {nullptr};
  sensor::Sensor *power_sensors_[10] = {nullptr};
  sensor::Sensor *energy_sensors_[10] = {nullptr};
  sensor::Sensor *total_energy_sensors_[10] = {nullptr};
  
  // 状态管理
  bool initialized_ = false;
  std::string last_error_;
  
  // 静态实例指针（用于回调）
  static BL0906Wrapper *instance_;
  
  // 内部辅助函数
  bool initialize_precompiled_library();
  bool perform_frequency_adaptation();
  void update_sensors();
  void set_error(const std::string &error);
  
  // 数据转换
  bl0906_chip_model_t map_chip_model(ChipModel model);
  bl0906_voltage_sampling_mode_t map_voltage_sampling_mode(VoltageSamplingMode mode);
  bl0906_comm_type_t determine_communication_type();
};

}  // namespace bl0906_factory
}  // namespace esphome 