#include "bl0906_hal_interface.h"
#include <stdlib.h>
#include <unistd.h>
#include <time.h>

// 静态变量
static bl0906_hal_callbacks_t g_hal_callbacks = {0};
static bool g_hal_initialized = false;

// 默认延时实现（使用nanosleep）
void bl0906_hal_delay_ms_default(uint32_t ms) {
    struct timespec ts;
    ts.tv_sec = ms / 1000;
    ts.tv_nsec = (ms % 1000) * 1000000;
    nanosleep(&ts, NULL);
}

void bl0906_hal_delay_us_default(uint32_t us) {
    struct timespec ts;
    ts.tv_sec = us / 1000000;
    ts.tv_nsec = (us % 1000000) * 1000;
    nanosleep(&ts, NULL);
}

// 默认内存管理实现
void* bl0906_hal_malloc_default(size_t size) {
    return malloc(size);
}

void bl0906_hal_free_default(void* ptr) {
    free(ptr);
}

// 初始化HAL
bl0906_hal_result_t bl0906_hal_init(const bl0906_hal_callbacks_t* callbacks) {
    if (!callbacks) {
        return BL0906_HAL_INVALID_PARAM;
    }
    
    g_hal_callbacks = *callbacks;
    
    // 设置默认实现（如果未提供）
    if (!g_hal_callbacks.delay_ms) {
        g_hal_callbacks.delay_ms = bl0906_hal_delay_ms_default;
    }
    if (!g_hal_callbacks.delay_us) {
        g_hal_callbacks.delay_us = bl0906_hal_delay_us_default;
    }
    if (!g_hal_callbacks.malloc_func) {
        g_hal_callbacks.malloc_func = bl0906_hal_malloc_default;
    }
    if (!g_hal_callbacks.free_func) {
        g_hal_callbacks.free_func = bl0906_hal_free_default;
    }
    
    g_hal_initialized = true;
    return BL0906_HAL_OK;
}

// 延时函数
void bl0906_hal_delay_ms(uint32_t ms) {
    if (g_hal_initialized && g_hal_callbacks.delay_ms) {
        g_hal_callbacks.delay_ms(ms);
    } else {
        bl0906_hal_delay_ms_default(ms);
    }
}

void bl0906_hal_delay_us(uint32_t us) {
    if (g_hal_initialized && g_hal_callbacks.delay_us) {
        g_hal_callbacks.delay_us(us);
    } else {
        bl0906_hal_delay_us_default(us);
    }
}

// I2C操作函数
bl0906_hal_result_t bl0906_hal_i2c_read(uint8_t device_addr, uint8_t reg_addr, uint8_t* data, size_t length) {
    if (!g_hal_initialized || !g_hal_callbacks.i2c_read) {
        return BL0906_HAL_ERROR;
    }
    return g_hal_callbacks.i2c_read(device_addr, reg_addr, data, length);
}

bl0906_hal_result_t bl0906_hal_i2c_write(uint8_t device_addr, uint8_t reg_addr, const uint8_t* data, size_t length) {
    if (!g_hal_initialized || !g_hal_callbacks.i2c_write) {
        return BL0906_HAL_ERROR;
    }
    return g_hal_callbacks.i2c_write(device_addr, reg_addr, data, length);
}

bl0906_hal_result_t bl0906_hal_i2c_read_bytes(uint8_t device_addr, uint8_t* data, size_t length) {
    if (!g_hal_initialized || !g_hal_callbacks.i2c_read_bytes) {
        return BL0906_HAL_ERROR;
    }
    return g_hal_callbacks.i2c_read_bytes(device_addr, data, length);
}

bl0906_hal_result_t bl0906_hal_i2c_write_bytes(uint8_t device_addr, const uint8_t* data, size_t length) {
    if (!g_hal_initialized || !g_hal_callbacks.i2c_write_bytes) {
        return BL0906_HAL_ERROR;
    }
    return g_hal_callbacks.i2c_write_bytes(device_addr, data, length);
}

// 内存管理函数
void* bl0906_hal_malloc(size_t size) {
    if (g_hal_initialized && g_hal_callbacks.malloc_func) {
        return g_hal_callbacks.malloc_func(size);
    } else {
        return bl0906_hal_malloc_default(size);
    }
}

void bl0906_hal_free(void* ptr) {
    if (g_hal_initialized && g_hal_callbacks.free_func) {
        g_hal_callbacks.free_func(ptr);
    } else {
        bl0906_hal_free_default(ptr);
    }
} 