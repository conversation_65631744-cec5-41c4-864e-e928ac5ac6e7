#!/bin/bash
# BL0906 Factory 生产版构建脚本
# 用途：构建包含完整校准调试功能的生产版本

echo "=== BL0906 Factory 生产版构建 ==="

# 检查当前目录
if [ ! -f "__init__.py" ] || [ ! -f "__init_production.py" ]; then
    echo "错误：未找到必要的配置文件"
    echo "  需要: __init__.py (发布版)"
    echo "  需要: __init_production.py (生产版)"
    exit 1
fi

# 备份当前发布版配置
echo "✓ 备份发布版配置..."
cp __init__.py __init_release_backup.py

# 切换到生产版配置
echo "✓ 切换到生产版配置..."
cp __init_production.py __init__.py

# 设置构建环境变量
export BUILD_TYPE=production
export CPPFLAGS="-DBL0906_DEVELOPMENT_BUILD"

echo "✓ 设置生产版编译标志："
echo "  - BL0906_DEVELOPMENT_BUILD: 生产版标识"
echo "  - 包含完整的 number 组件支持"
echo "  - 包含校准调试功能"

# 验证生产版配置
if ! grep -q "number" __init__.py; then
    echo "错误：生产版配置应包含 number 组件"
    echo "恢复发布版配置..."
    cp __init_release_backup.py __init__.py
    rm __init_release_backup.py
    exit 1
fi

if ! grep -q "AUTO_LOAD.*sensor.*number" __init__.py; then
    echo "错误：生产版配置应在 AUTO_LOAD 中包含 number 组件"
    echo "恢复发布版配置..."
    cp __init_release_backup.py __init__.py
    rm __init_release_backup.py
    exit 1
fi

echo "✓ 生产版配置验证通过"

# 检查必要文件
required_files=(
    "bl0906_factory.cpp"
    "bl0906_factory.h"
    "sensor.py"
    "number.py"
    "bl0906_number.cpp"
    "bl0906_number.h"
    "config_mappings.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误：缺少必要文件 $file"
        echo "恢复发布版配置..."
        cp __init_release_backup.py __init__.py
        rm __init_release_backup.py
        exit 1
    fi
done

echo "✓ 必要文件检查完成"

# 构建信息
echo ""
echo "=== 生产版特性 ==="
echo "✓ 包含 number 组件（校准调试功能）"
echo "✓ 完整的校准参数调整支持"
echo "✓ 初始校准值配置"
echo "✓ 校准模式开关"
echo "✓ 开发调试友好"
echo "✓ 生产制造过程支持"

echo ""
echo "=== 重要提醒 ==="
echo "⚠️  当前已切换到生产版配置"
echo "⚠️  构建完成后请运行以下命令恢复发布版："
echo "    ./restore_release.sh"
echo "    或手动执行: cp __init_release_backup.py __init__.py"

echo ""
echo "=== 生产版构建完成 ==="
echo "当前配置为生产版，适用于开发和生产制造"
echo "" 