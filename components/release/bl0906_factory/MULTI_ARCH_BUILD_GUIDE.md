# BL0906 Factory 多架构构建指南

## 概述

BL0906 Factory 预编译库现在支持多种ESP32系列架构的编译。本指南将详细说明如何为不同的ESP32芯片编译静态库。

## 支持的架构

| 架构 | 芯片型号 | 工具链 | 指令集 | 特点 |
|------|----------|--------|--------|------|
| esp32 | ESP32 | xtensa-esp32-elf | Xtensa LX6 | 双核，经典版本 |
| esp32s2 | ESP32-S2 | xtensa-esp32s2-elf | Xtensa LX7 | 单核，USB支持 |
| esp32s3 | ESP32-S3 | xtensa-esp32s3-elf | Xtensa LX7 | 双核，AI加速 |
| esp32c3 | ESP32-C3 | riscv32-esp-elf | RISC-V | 单核，低功耗 |
| esp32c6 | ESP32-C6 | riscv32-esp-elf | RISC-V | WiFi 6支持 |
| esp32h2 | ESP32-H2 | riscv32-esp-elf | RISC-V | 802.15.4支持 |

## 快速开始

### 基本用法

```bash
# 为ESP32编译（默认）
./build_precompiled_lib.sh esp32

# 为ESP32-C3编译
./build_precompiled_lib.sh esp32c3

# 为ESP32-S3编译，显示详细输出
./build_precompiled_lib.sh esp32s3 --verbose

# 显示帮助信息
./build_precompiled_lib.sh --help
```

### 输出文件

每个架构会生成对应的库文件：

```
release/
├── libbl0906_core_esp32.a      # ESP32版本
├── libbl0906_core_esp32c3.a    # ESP32-C3版本
├── libbl0906_core_esp32s3.a    # ESP32-S3版本
├── bl0906_core_api.h           # 通用头文件
├── VERSION                     # 版本信息
└── README.md                   # 说明文档
```

## 环境要求

### ESP-IDF工具链

确保已安装对应架构的ESP-IDF工具链：

```bash
# 检查ESP-IDF环境
echo $IDF_PATH

# 检查工具链是否可用
xtensa-esp32-elf-gcc --version      # ESP32
xtensa-esp32s3-elf-gcc --version    # ESP32-S3
riscv32-esp-elf-gcc --version       # ESP32-C3/C6/H2
```

### 自动架构检测

脚本支持从环境变量自动检测架构：

```bash
# 设置IDF_TARGET环境变量
export IDF_TARGET=esp32c3
./build_precompiled_lib.sh          # 自动使用esp32c3

# 命令行参数优先级更高
./build_precompiled_lib.sh esp32s3  # 使用esp32s3，忽略IDF_TARGET
```

## 详细使用说明

### 命令行选项

```
使用方法: ./build_precompiled_lib.sh [架构] [选项]

支持的架构:
  esp32       ESP32 (Xtensa LX6) - 默认
  esp32s2     ESP32-S2 (Xtensa LX7)
  esp32s3     ESP32-S3 (Xtensa LX7)
  esp32c3     ESP32-C3 (RISC-V)
  esp32c6     ESP32-C6 (RISC-V)
  esp32h2     ESP32-H2 (RISC-V)

选项:
  --skip-cleanup    跳过清理步骤
  --verbose         详细输出
  --help            显示帮助信息
```

### 编译过程

1. **架构检测**: 根据参数或环境变量确定目标架构
2. **工具链配置**: 自动配置对应的编译器和链接器
3. **源码编译**: 使用架构特定的编译选项编译源码
4. **库文件生成**: 创建架构特定的静态库文件
5. **文档生成**: 生成包含架构信息的说明文档

### 构建目录

每个架构使用独立的构建目录：

```
build_esp32/        # ESP32构建目录
build_esp32c3/      # ESP32-C3构建目录
build_esp32s3/      # ESP32-S3构建目录
```

## 架构特定配置

### Xtensa架构 (ESP32/S2/S3)

- 编译标志: `-mlongcalls`
- 工具链前缀: `xtensa-esp32*-elf-`
- 特点: 成熟稳定，性能优异

### RISC-V架构 (ESP32-C3/C6/H2)

- ESP32-C3: `-march=rv32imc -mabi=ilp32`
- ESP32-C6/H2: `-march=rv32imac -mabi=ilp32`
- 工具链前缀: `riscv32-esp-elf-`
- 特点: 开源指令集，功耗优化

## 集成到ESPHome

### 1. 选择正确的库文件

根据您的ESP32芯片型号选择对应的库文件：

```yaml
# ESP32项目
esphome:
  name: my-project
  platform: ESP32
  board: esp32dev

# ESP32-C3项目  
esphome:
  name: my-project
  platform: ESP32
  board: esp32-c3-devkitm-1
```

### 2. 复制库文件

将对应架构的库文件复制到组件目录：

```bash
# 对于ESP32项目
cp release/libbl0906_core_esp32.a components/bl0906_factory/

# 对于ESP32-C3项目
cp release/libbl0906_core_esp32c3.a components/bl0906_factory/
```

### 3. 更新组件配置

确保组件链接正确的库文件。

## 测试验证

### 运行测试脚本

```bash
# 运行多架构测试
./test_build.sh

# 测试特定架构（需要安装对应工具链）
./build_precompiled_lib.sh esp32c3 --verbose
```

### 验证库文件

```bash
# 检查库文件架构
file release/libbl0906_core_esp32c3.a

# 查看符号表
nm release/libbl0906_core_esp32c3.a | grep -E "^[0-9a-f]+ [TtWw] "

# 检查库文件大小
ls -lh release/libbl0906_core_*.a
```

## 故障排除

### 常见问题

1. **工具链未找到**
   ```
   错误: 未找到 riscv32-esp-elf-gcc 编译器
   解决: 安装ESP-IDF并设置正确的PATH
   ```

2. **权限问题**
   ```bash
   chmod +x build_precompiled_lib.sh
   ```

3. **构建失败**
   ```bash
   # 清理后重试
   rm -rf build_*
   ./build_precompiled_lib.sh esp32c3 --verbose
   ```

### 调试技巧

- 使用 `--verbose` 选项查看详细输出
- 检查 `VERSION` 文件确认架构信息
- 使用 `file` 命令验证库文件架构

## 最佳实践

1. **批量编译**: 为所有目标架构编译库文件
2. **版本管理**: 使用不同目录管理不同架构的库文件
3. **自动化**: 集成到CI/CD流水线中
4. **测试**: 在实际硬件上测试每个架构版本

## 示例脚本

### 批量编译所有架构

```bash
#!/bin/bash
architectures=("esp32" "esp32s2" "esp32s3" "esp32c3" "esp32c6" "esp32h2")

for arch in "${architectures[@]}"; do
    echo "编译 $arch..."
    if ./build_precompiled_lib.sh "$arch"; then
        echo "✓ $arch 编译成功"
    else
        echo "✗ $arch 编译失败"
    fi
done
```

### 自动部署脚本

```bash
#!/bin/bash
# 根据项目配置自动选择正确的库文件

if grep -q "board: esp32-c3" esphome_config.yaml; then
    cp release/libbl0906_core_esp32c3.a components/bl0906_factory/
elif grep -q "board: esp32-s3" esphome_config.yaml; then
    cp release/libbl0906_core_esp32s3.a components/bl0906_factory/
else
    cp release/libbl0906_core_esp32.a components/bl0906_factory/
fi
```

## 更新日志

- **v2.0.0**: 添加多架构支持，支持ESP32系列所有主要芯片
- **v1.0.0**: 初始版本，仅支持ESP32

## 贡献指南

如需添加新架构支持：

1. 在 `detect_target_architecture()` 函数中添加新架构配置
2. 更新帮助文档和README
3. 添加对应的测试用例
4. 更新架构兼容性表格

## 联系支持

如有问题请提交Issue或联系维护者。 