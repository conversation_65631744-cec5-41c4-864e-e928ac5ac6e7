#include "bl0906_wrapper.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"

namespace esphome {
namespace bl0906_factory {

// 静态实例指针
BL0906Wrapper* BL0906Wrapper::instance_ = nullptr;

// 适配器删除器实现
void BL0906Wrapper::AdapterDeleter::operator()(bl0906_core::CommunicationAdapterInterface* adapter) const {
  if (adapter) {
    bl0906_core_destroy_adapter(adapter);
  }
}

void BL0906Wrapper::setup() {
  ESP_LOGD(TAG, "薄包装层初始化开始...");
  
  // 设置静态实例指针
  instance_ = this;
  
  // 检查通信适配器
  if (!comm_adapter_) {
    ESP_LOGE(TAG, "通信适配器未设置");
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：通信适配器未设置，但继续运行");
    } else {
      this->mark_failed();
      return;
    }
  }
  
  // 初始化通信适配器
  if (comm_adapter_ && !comm_adapter_->initialize()) {
    ESP_LOGE(TAG, "通信适配器初始化失败: %s", comm_adapter_->get_last_error().c_str());
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：通信适配器初始化失败，但继续运行");
    } else {
      this->mark_failed();
      return;
    }
  }
  
  // 初始化预编译库
  if (!initialize_precompiled_library()) {
    ESP_LOGE(TAG, "预编译库初始化失败");
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：预编译库初始化失败，但继续运行");
    } else {
      this->mark_failed();
      return;
    }
  }
  
  // 执行频率适配
  if (freq_adapt_mode_ != FreqAdaptMode::OFF && !freq_adapted_) {
    if (perform_frequency_adaptation()) {
      freq_adapted_ = true;
      ESP_LOGI(TAG, "频率适配完成");
    } else {
      ESP_LOGW(TAG, "频率适配失败，使用默认50Hz模式");
    }
  }
  
  // 启用电量持久化
  if (energy_persistence_enabled_) {
    bl0906_result_t result = bl0906_core_set_energy_persistence(true);
    if (result != BL0906_SUCCESS) {
      ESP_LOGW(TAG, "电量持久化启用失败: %s", bl0906_core_get_error_string(result));
    } else {
      ESP_LOGI(TAG, "电量持久化已启用");
    }
  }
  
  initialized_ = true;
  ESP_LOGI(TAG, "薄包装层初始化完成");
}

void BL0906Wrapper::loop() {
  if (!initialized_) {
    return;
  }
  
  // 更新传感器数据
  update_sensors();
}

void BL0906Wrapper::update() {
  if (!initialized_) {
    return;
  }
  
  // 定期更新传感器数据
  update_sensors();
}

void BL0906Wrapper::dump_config() {
  ESP_LOGCONFIG(TAG, "BL0906 Factory (Release Version):");
  ESP_LOGCONFIG(TAG, "  Chip Model: %s", bl0906_core_get_chip_name());
  ESP_LOGCONFIG(TAG, "  Channel Count: %d", bl0906_core_get_channel_count());
  ESP_LOGCONFIG(TAG, "  Energy Persistence: %s", energy_persistence_enabled_ ? "Enabled" : "Disabled");
  ESP_LOGCONFIG(TAG, "  Fault Tolerant Mode: %s", fault_tolerant_mode_ ? "Enabled" : "Disabled");
  ESP_LOGCONFIG(TAG, "  Frequency Adaptation: %s", 
                freq_adapt_mode_ == FreqAdaptMode::OFF ? "Off" :
                freq_adapt_mode_ == FreqAdaptMode::AUTO ? "Auto" : "60Hz");
  ESP_LOGCONFIG(TAG, "  Instance ID: 0x%08X", instance_id_);
  
  if (comm_adapter_) {
    ESP_LOGCONFIG(TAG, "  Communication: %s", comm_adapter_->get_adapter_type().c_str());
  } else {
    ESP_LOGCONFIG(TAG, "  Communication: Not configured");
  }
}

void BL0906Wrapper::set_communication_adapter(bl0906_core::CommunicationAdapterInterface* adapter) {
  comm_adapter_.reset(adapter);
  ESP_LOGI(TAG, "设置通信适配器: %s", 
           comm_adapter_ ? comm_adapter_->get_adapter_type().c_str() : "null");
}

bl0906_core::CommunicationAdapterInterface* BL0906Wrapper::get_communication_adapter() const {
  return comm_adapter_.get();
}

void BL0906Wrapper::create_uart_adapter(uart::UARTComponent* uart_component) {
  if (!uart_component) {
    ESP_LOGE(TAG, "UART组件为空，无法创建UART适配器");
    return;
  }

  // 发布版：通过预编译库的工厂函数创建UART适配器
  ESP_LOGI(TAG, "通过预编译库创建UART通信适配器");

  // 调用预编译库中的工厂函数（返回void*，需要转换）
  void* adapter_ptr = bl0906_core_create_uart_adapter(uart_component);
  if (adapter_ptr) {
    bl0906_core::CommunicationAdapterInterface* adapter =
        static_cast<bl0906_core::CommunicationAdapterInterface*>(adapter_ptr);
    comm_adapter_.reset(adapter);
    ESP_LOGI(TAG, "UART适配器创建成功: %s", adapter->get_adapter_type().c_str());
  } else {
    ESP_LOGE(TAG, "UART适配器创建失败");
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：UART适配器创建失败，但继续运行");
    }
  }
}

void BL0906Wrapper::create_spi_adapter(spi::SPIComponent* spi_component, GPIOPin* cs_pin) {
  if (!spi_component || !cs_pin) {
    ESP_LOGE(TAG, "SPI组件或CS引脚为空，无法创建SPI适配器");
    return;
  }

  // 发布版：通过预编译库的工厂函数创建SPI适配器
  ESP_LOGI(TAG, "通过预编译库创建SPI通信适配器");

  // 调用预编译库中的工厂函数（返回void*，需要转换）
  void* adapter_ptr = bl0906_core_create_spi_adapter(spi_component, cs_pin);
  if (adapter_ptr) {
    bl0906_core::CommunicationAdapterInterface* adapter =
        static_cast<bl0906_core::CommunicationAdapterInterface*>(adapter_ptr);
    comm_adapter_.reset(adapter);
    ESP_LOGI(TAG, "SPI适配器创建成功: %s", adapter->get_adapter_type().c_str());
  } else {
    ESP_LOGE(TAG, "SPI适配器创建失败");
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：SPI适配器创建失败，但继续运行");
    }
  }
}

void BL0906Wrapper::set_sensor(SensorType sensor_type, sensor::Sensor *sensor, int channel) {
  if (!sensor) {
    return;
  }
  
  // 传感器类型映射（与生产版保持一致）
  switch (sensor_type) {
    case SensorType::SENSOR_VOLTAGE:
      voltage_sensor_ = sensor;
      break;
    case SensorType::SENSOR_FREQUENCY:
      frequency_sensor_ = sensor;
      break;
    case SensorType::SENSOR_TEMPERATURE:
      temperature_sensor_ = sensor;
      break;
    case SensorType::SENSOR_POWER_SUM:
      power_sum_sensor_ = sensor;
      break;
    case SensorType::SENSOR_ENERGY_SUM:
      energy_sum_sensor_ = sensor;
      break;
    case SensorType::SENSOR_CURRENT:
      if (channel >= 0 && channel < 10) {
        current_sensors_[channel] = sensor;
      }
      break;
    case SensorType::SENSOR_POWER:
      if (channel >= 0 && channel < 10) {
        power_sensors_[channel] = sensor;
      }
      break;
    case SensorType::SENSOR_ENERGY:
      if (channel >= 0 && channel < 10) {
        energy_sensors_[channel] = sensor;
      }
      break;
    case SensorType::SENSOR_TOTAL_ENERGY:
      if (channel >= 0 && channel < 10) {
        total_energy_sensors_[channel] = sensor;
      }
      break;
    default:
      ESP_LOGW(TAG, "未知的传感器类型: %d", static_cast<int>(sensor_type));
      break;
  }
}

bool BL0906Wrapper::validate_register_address(uint8_t address) {
  // 简化的寄存器地址验证
  return address != 0x00;
}

bool BL0906Wrapper::initialize_precompiled_library() {
  if (!comm_adapter_) {
    set_error("通信适配器未设置");
    return false;
  }
  
  // 设置通信回调
  bl0906_comm_callbacks_t callbacks = {
    .read_register = read_register_callback,
    .write_register = write_register_callback,
    .send_raw_command = send_raw_command_callback
  };
  
  // 确定通信类型
  bl0906_comm_type_t comm_type = determine_communication_type();
  
  // 映射芯片型号
  bl0906_chip_model_t chip_model = map_chip_model(chip_model_);
  
  // 初始化预编译库
  bl0906_result_t result = bl0906_core_init(chip_model, comm_type, &callbacks);
  if (result != BL0906_SUCCESS) {
    set_error("预编译库初始化失败: " + std::string(bl0906_core_get_error_string(result)));
    return false;
  }
  
  // 初始化I2C EEPROM存储（如果启用）
  if (eeprom_storage_enabled_) {
    bl0906_eeprom_type_t eeprom_type;
    switch (eeprom_type_) {
      case EEPROMType::EEPROM_24C02:
        eeprom_type = BL0906_EEPROM_24C02;
        break;
      case EEPROMType::EEPROM_24C04:
        eeprom_type = BL0906_EEPROM_24C04;
        break;
      case EEPROMType::EEPROM_24C08:
        eeprom_type = BL0906_EEPROM_24C08;
        break;
      case EEPROMType::EEPROM_24C16:
        eeprom_type = BL0906_EEPROM_24C16;
        break;
      default:
        eeprom_type = BL0906_EEPROM_24C08;
        break;
    }
    
    result = bl0906_core_init_eeprom_storage(eeprom_type, i2c_address_);
    if (result == BL0906_SUCCESS) {
      ESP_LOGI(TAG, "I2C EEPROM存储初始化成功");
      
      // 尝试从EEPROM加载校准数据
      if (instance_id_ != 0) {
        bl0906_calibration_entry_t entries[50];  // 最多50个校准条目
        size_t actual_count = 0;
        
        result = bl0906_core_load_calibration_from_eeprom(instance_id_, entries, 50, &actual_count);
        if (result == BL0906_SUCCESS && actual_count > 0) {
          ESP_LOGI(TAG, "从EEPROM加载了%d个校准条目", actual_count);
          
          // ESP32-C3版本的库中校准数据会自动应用，无需手动调用apply函数
          ESP_LOGI(TAG, "校准数据已自动应用");
        } else {
          ESP_LOGD(TAG, "EEPROM中没有找到实例0x%08X的校准数据", instance_id_);
        }
      }
    } else {
      ESP_LOGW(TAG, "I2C EEPROM存储初始化失败: %s", bl0906_core_get_error_string(result));
    }
  }
  
  return true;
}

bool BL0906Wrapper::perform_frequency_adaptation() {
  if (freq_adapt_mode_ == FreqAdaptMode::OFF) {
    return true;
  }
  
  bl0906_result_t result;
  
  if (freq_adapt_mode_ == FreqAdaptMode::AUTO) {
    // 自动检测频率
    result = bl0906_core_auto_detect_frequency();
    if (result == BL0906_SUCCESS) {
      ESP_LOGI(TAG, "自动频率检测完成");
      return true;
    } else {
      ESP_LOGW(TAG, "自动频率检测失败: %s", bl0906_core_get_error_string(result));
      return false;
    }
  } else if (freq_adapt_mode_ == FreqAdaptMode::HZ60) {
    // 强制60Hz模式
    result = bl0906_core_set_frequency_mode(true);
    if (result == BL0906_SUCCESS) {
      ESP_LOGI(TAG, "强制60Hz模式设置完成");
      return true;
    } else {
      ESP_LOGW(TAG, "60Hz模式设置失败: %s", bl0906_core_get_error_string(result));
      return false;
    }
  }
  
  return false;
}

void BL0906Wrapper::update_sensors() {
  if (!initialized_) {
    return;
  }
  
  // 读取传感器数据
  bl0906_sensor_data_t data;
  bl0906_result_t result = bl0906_core_read_sensor_data(&data);
  
  if (result != BL0906_SUCCESS) {
    if (!fault_tolerant_mode_) {
      ESP_LOGE(TAG, "读取传感器数据失败: %s", bl0906_core_get_error_string(result));
    }
    return;
  }
  
  // 发布全局传感器数据
  if (voltage_sensor_) {
    voltage_sensor_->publish_state(data.voltage);
  }
  if (frequency_sensor_) {
    frequency_sensor_->publish_state(data.frequency);
  }
  if (temperature_sensor_) {
    temperature_sensor_->publish_state(data.temperature);
  }
  if (power_sum_sensor_) {
    power_sum_sensor_->publish_state(data.power_sum);
  }
  if (energy_sum_sensor_) {
    energy_sum_sensor_->publish_state(data.energy_sum);
  }
  
  // 发布通道传感器数据
  int channel_count = bl0906_core_get_channel_count();
  for (int i = 0; i < channel_count && i < 10; i++) {
    if (current_sensors_[i]) {
      current_sensors_[i]->publish_state(data.channels[i].current);
    }
    if (power_sensors_[i]) {
      power_sensors_[i]->publish_state(data.channels[i].power);
    }
    if (energy_sensors_[i]) {
      energy_sensors_[i]->publish_state(data.channels[i].energy);
    }
    if (total_energy_sensors_[i]) {
      // 总电量传感器使用持久化数据
      float total_energy;
      if (bl0906_core_get_persistent_energy(i, &total_energy) == BL0906_SUCCESS) {
        total_energy_sensors_[i]->publish_state(total_energy);
      }
    }
  }
}

void BL0906Wrapper::set_error(const std::string &error) {
  last_error_ = error;
  ESP_LOGE(TAG, "%s", error.c_str());
}

bl0906_chip_model_t BL0906Wrapper::map_chip_model(ChipModel model) {
  switch (model) {
    case ChipModel::BL0906:
      return BL0906_CHIP_BL0906;
    case ChipModel::BL0910:
      return BL0906_CHIP_BL0910;
    default:
      return BL0906_CHIP_BL0906;
  }
}

bl0906_voltage_sampling_mode_t BL0906Wrapper::map_voltage_sampling_mode(VoltageSamplingMode mode) {
  switch (mode) {
    case VoltageSamplingMode::TRANSFORMER:
      return BL0906_VOLTAGE_SAMPLING_TRANSFORMER;
    case VoltageSamplingMode::RESISTOR_DIVIDER:
      return BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER;
    default:
      return BL0906_VOLTAGE_SAMPLING_TRANSFORMER;
  }
}

bl0906_comm_type_t BL0906Wrapper::determine_communication_type() {
  if (!comm_adapter_) {
    return BL0906_COMM_UART;  // 默认UART
  }
  
  std::string adapter_type = comm_adapter_->get_adapter_type();
  if (adapter_type == "SPI") {
    return BL0906_COMM_SPI;
  } else {
    return BL0906_COMM_UART;
  }
}

// 静态回调函数实现
bl0906_result_t BL0906Wrapper::read_register_callback(uint8_t address, int32_t *value) {
  if (!instance_) {
    return BL0906_ERROR_NOT_INITIALIZED;
  }
  
  return instance_->read_register_impl(address, value);
}

bl0906_result_t BL0906Wrapper::write_register_callback(uint8_t address, int16_t value) {
  if (!instance_) {
    return BL0906_ERROR_NOT_INITIALIZED;
  }
  
  return instance_->write_register_impl(address, value);
}

bl0906_result_t BL0906Wrapper::send_raw_command_callback(const uint8_t *data, size_t length) {
  if (!instance_) {
    return BL0906_ERROR_NOT_INITIALIZED;
  }
  
  return instance_->send_raw_command_impl(data, length);
}

// 实际通信实现
bl0906_result_t BL0906Wrapper::read_register_impl(uint8_t address, int32_t *value) {
  if (!comm_adapter_) {
    return BL0906_ERROR_COMMUNICATION;
  }
  
  bool success = false;
  *value = comm_adapter_->read_register(address, &success);
  
  return success ? BL0906_SUCCESS : BL0906_ERROR_COMMUNICATION;
}

bl0906_result_t BL0906Wrapper::write_register_impl(uint8_t address, int16_t value) {
  if (!comm_adapter_) {
    return BL0906_ERROR_COMMUNICATION;
  }
  
  bool success = comm_adapter_->write_register(address, value);
  
  return success ? BL0906_SUCCESS : BL0906_ERROR_COMMUNICATION;
}

bl0906_result_t BL0906Wrapper::send_raw_command_impl(const uint8_t *data, size_t length) {
  if (!comm_adapter_) {
    return BL0906_ERROR_COMMUNICATION;
  }
  
  bool success = comm_adapter_->send_raw_command(data, length);
  
  return success ? BL0906_SUCCESS : BL0906_ERROR_COMMUNICATION;
}

// I2C EEPROM存储方法实现
void BL0906Wrapper::set_eeprom_storage(EEPROMType eeprom_type, uint8_t i2c_address) {
  eeprom_storage_enabled_ = true;
  eeprom_type_ = eeprom_type;
  i2c_address_ = i2c_address;
  
  ESP_LOGI(TAG, "配置I2C EEPROM存储: 型号=%d, 地址=0x%02X", 
           static_cast<int>(eeprom_type), i2c_address);
}

}  // namespace bl0906_factory
}  // namespace esphome 