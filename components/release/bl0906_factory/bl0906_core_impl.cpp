// BL0906 核心算法实现 - 真实预编译库
// 这个文件包含从 bl0906_factory.cpp 提取的核心算法
// 在实际部署中，这个文件将被编译为预编译库 libbl0906_core.a

#include "bl0906_core_api.h"
#include "calibration_storage_lib/include/calibration_storage_interface.h"
#ifdef USE_I2C_EEPROM_CALIBRATION
#include "calibration_storage_lib/include/i2c_eeprom_calibration_storage.h"
using esphome::bl0906_factory::CalibrationStorageInterface;
using esphome::bl0906_factory::CalibrationEntry;
using esphome::bl0906_factory::I2CEepromCalibrationStorage;
using esphome::bl0906_factory::I2CInterface;
#endif
#include <string.h>
#include <math.h>

// 引入校准系数头文件，支持编译时计算
// 为了避免宏定义与枚举名称冲突，我们需要特殊处理
#ifdef BL0906_USE_VOLTAGE_TRANSFORMER
#define BL0906_CALIBRATION_USE_TRANSFORMER
#define BL0906_VOLTAGE_SAMPLING_TRANSFORMER
#endif

#ifdef BL0906_USE_RESISTOR_DIVIDER
#define BL0906_CALIBRATION_USE_RESISTOR_DIVIDER
#define BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER
#endif

#include "../bl0906_calibration.h"

// 使用编译时计算的校准系数（从bl0906_calibration.h命名空间导入）
using namespace esphome::bl0906_factory;

// ============================================================================
// 内部状态和数据结构（在预编译库中会被隐藏）
// ============================================================================

static bl0906_chip_model_t g_chip_model = BL0906_CHIP_BL0906;
static bl0906_comm_type_t g_comm_type = BL0906_COMM_UART;
static bl0906_comm_callbacks_t g_callbacks = {0};
static bool g_initialized = false;

// I2C EEPROM存储相关变量
#ifdef USE_I2C_EEPROM_CALIBRATION

// 简单的I2C接口实现（适配层）
class SimpleI2CInterface : public I2CInterface {
public:
    SimpleI2CInterface(uint8_t device_addr) : device_addr_(device_addr) {}

    bool read(uint8_t addr, uint8_t* data, size_t len) override {
        // 这里应该调用实际的I2C读取函数
        // 在发布版中，我们可以返回模拟数据或者调用HAL接口
        // 暂时返回false，表示未实现
        return false;
    }

private:
    uint8_t device_addr_;
};

static CalibrationStorageInterface* g_eeprom_storage = nullptr;
static SimpleI2CInterface* g_i2c_interface = nullptr;
static bool g_eeprom_initialized = false;
#endif

// ============================================================================
// 芯片参数定义（从 bl0906_chip_params.h 迁移）- 支持运行时切换
// ============================================================================

// BL0906 地址数组
static const uint8_t BL0906_I_RMS_ADDRS[] = {0x0D, 0x0E, 0x0F, 0x10, 0x13, 0x14};
static const uint8_t BL0906_WATT_ADDRS[] = {0x23, 0x24, 0x25, 0x26, 0x29, 0x2A};
static const uint8_t BL0906_CF_CNT_ADDRS[] = {0x30, 0x31, 0x32, 0x33, 0x36, 0x37};
static const uint8_t BL0906_RMSGN_ADDRS[] = {0x6D, 0x6E, 0x6F, 0x70, 0x73, 0x74};
static const uint8_t BL0906_RMSOS_ADDRS[] = {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F};
static const uint8_t BL0906_CHGN_ADDRS[] = {0xA1, 0xA2, 0xA3, 0xA4, 0xA7, 0xA8};
static const uint8_t BL0906_CHOS_ADDRS[] = {0xAC, 0xAD, 0xAE, 0xAF, 0xB2, 0xB3};
static const uint8_t BL0906_WATTGN_ADDRS[] = {0xB7, 0xB8, 0xB9, 0xBA, 0xBD, 0xBE};
static const uint8_t BL0906_WATTOS_ADDRS[] = {0xC1, 0xC2, 0xC3, 0xC4, 0xC7, 0xC8};

// BL0910 地址数组
static const uint8_t BL0910_I_RMS_ADDRS[] = {0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15};
static const uint8_t BL0910_WATT_ADDRS[] = {0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B};
static const uint8_t BL0910_CF_CNT_ADDRS[] = {0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38};
static const uint8_t BL0910_RMSGN_ADDRS[] = {0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75};
static const uint8_t BL0910_RMSOS_ADDRS[] = {0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F, 0x80};
static const uint8_t BL0910_CHGN_ADDRS[] = {0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9};
static const uint8_t BL0910_CHOS_ADDRS[] = {0xAC, 0xAD, 0xAE, 0xAF, 0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5};
static const uint8_t BL0910_WATTGN_ADDRS[] = {0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF};
static const uint8_t BL0910_WATTOS_ADDRS[] = {0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9};

// 芯片参数结构（支持运行时切换）
typedef struct {
    int channel_count;
    const char* chip_name;
    const uint8_t* i_rms_addr;
    const uint8_t* watt_addr;
    const uint8_t* cf_cnt_addr;
    const uint8_t* rmsgn_addr;
    const uint8_t* rmsos_addr;
    const uint8_t* chgn_addr;
    const uint8_t* chos_addr;
    const uint8_t* wattgn_addr;
    const uint8_t* wattos_addr;
} chip_params_t;

// 芯片参数数组（支持运行时切换）
static const chip_params_t CHIP_PARAMS[] = {
    // BL0906
    {
        .channel_count = 6,
        .chip_name = "BL0906",
        .i_rms_addr = BL0906_I_RMS_ADDRS,
        .watt_addr = BL0906_WATT_ADDRS,
        .cf_cnt_addr = BL0906_CF_CNT_ADDRS,
        .rmsgn_addr = BL0906_RMSGN_ADDRS,
        .rmsos_addr = BL0906_RMSOS_ADDRS,
        .chgn_addr = BL0906_CHGN_ADDRS,
        .chos_addr = BL0906_CHOS_ADDRS,
        .wattgn_addr = BL0906_WATTGN_ADDRS,
        .wattos_addr = BL0906_WATTOS_ADDRS,
    },
    // BL0910
    {
        .channel_count = 10,
        .chip_name = "BL0910",
        .i_rms_addr = BL0910_I_RMS_ADDRS,
        .watt_addr = BL0910_WATT_ADDRS,
        .cf_cnt_addr = BL0910_CF_CNT_ADDRS,
        .rmsgn_addr = BL0910_RMSGN_ADDRS,
        .rmsos_addr = BL0910_RMSOS_ADDRS,
        .chgn_addr = BL0910_CHGN_ADDRS,
        .chos_addr = BL0910_CHOS_ADDRS,
        .wattgn_addr = BL0910_WATTGN_ADDRS,
        .wattos_addr = BL0910_WATTOS_ADDRS,
    }
};

// 获取当前芯片参数
static const chip_params_t* get_current_chip_params() {
    return &CHIP_PARAMS[g_chip_model];
}

// 运行时寄存器地址查找函数
static uint8_t get_register_addr(bl0906_chip_model_t chip_model, bl0906_register_type_t reg_type, int channel) {
    const chip_params_t* params = &CHIP_PARAMS[chip_model];
    
    // 验证通道号
    if (channel < 0 || channel >= params->channel_count) {
        return 0; // 无效地址
    }
    
    switch (reg_type) {
        case BL0906_REG_I_RMS:
            return params->i_rms_addr[channel];
        case BL0906_REG_WATT:
            return params->watt_addr[channel];
        case BL0906_REG_CF_CNT:
            return params->cf_cnt_addr[channel];
        case BL0906_REG_RMSGN:
            return params->rmsgn_addr[channel];
        case BL0906_REG_RMSOS:
            return params->rmsos_addr[channel];
        case BL0906_REG_CHGN:
            return params->chgn_addr[channel];
        case BL0906_REG_CHOS:
            return params->chos_addr[channel];
        case BL0906_REG_WATTGN:
            return params->wattgn_addr[channel];
        case BL0906_REG_WATTOS:
            return params->wattos_addr[channel];
        default:
            return 0; // 无效寄存器类型
    }
}

// ============================================================================
// 校准系数常量（编译时计算，从bl0906_calibration.h导入）
// ============================================================================

// 校准系数现在从bl0906_calibration.h中的编译时计算获得
// 根据电压采样方式（BL0906_VOLTAGE_SAMPLING_TRANSFORMER 或 BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER）
// 自动选择对应的校准系数计算公式
// 
// 可用的编译时常量：
// - Ki: 电流系数
// - Kv: 电压系数  
// - Kp: 功率系数
// - Ke: 电量系数
// - Kp_sum: 总功率系数 
// - Ke_sum: 总电量系数

// 固定寄存器地址
static const uint8_t TEMPERATURE_ADDR = 0x5E;
static const uint8_t FREQUENCY_ADDR = 0x4E;
static const uint8_t V_RMS_ADDR = 0x16;
static const uint8_t WATT_SUM_ADDR = 0x2C;
static const uint8_t CF_SUM_ADDR = 0x39;
static const uint8_t MODE2_ADDR = 0x18;
static const uint8_t CHGN_V_ADDR = 0xAA;
static const uint8_t CHOS_V_ADDR = 0xB5;

// 频率适配相关常量
static const uint32_t MODE2_AC_FREQ_SEL_MASK = 0x800000;  // 第23位
static const float FREQ_DETECT_THRESHOLD_LOW = 55.0f;     // 60Hz检测下限
static const float FREQ_DETECT_THRESHOLD_HIGH = 65.0f;    // 60Hz检测上限
static const int FREQ_DETECTION_SAMPLES = 10;             // 频率检测采样次数

// ============================================================================
// 内部状态管理
// ============================================================================

// 传感器数据结构
typedef struct {
    uint32_t timestamp;
    uint32_t temperature_raw;
    uint32_t frequency_raw;
    uint32_t voltage_raw;
    int32_t power_sum_raw;
    uint32_t energy_sum_raw;
    struct {
        uint32_t current_raw;
        int32_t power_raw;
        uint32_t energy_raw;
    } channels[10];  // 支持最大10通道
    bool read_complete;
} sensor_data_t;

// 持久化数据结构
typedef struct {
    uint32_t persistent_cf_count[11];    // [0-9]为各通道，[10]为总和
    uint32_t last_cf_count[11];          // [0-9]为各通道，[10]为总和
    uint32_t save_count;                 // 存储计数器
    uint32_t checksum;                   // 数据校验和
} energy_persistence_data_t;

// 内部状态变量
static sensor_data_t g_current_data = {0};
static energy_persistence_data_t g_energy_data = {0};
static bool g_energy_persistence_enabled = false;
static bool g_chip_restart_detected = false;
static uint32_t g_last_restart_detection_time = 0;
static uint32_t g_chip_restart_count = 0;

// ============================================================================
// 核心算法实现
// ============================================================================

// 数据转换函数（从生产版同步）
static float convert_raw_to_value(uint8_t address, int32_t raw_value) {
    const chip_params_t* params = get_current_chip_params();
    
    // 基础传感器寄存器检查
    if (address == FREQUENCY_ADDR) {
        return (raw_value > 0) ? 10000000.0f / (uint32_t)raw_value : 0;
    }
    
    if (address == TEMPERATURE_ADDR) {
        uint32_t unsigned_raw = (uint32_t)raw_value;
        return (unsigned_raw - 64) * 12.5f / 59.0f - 40.0f;
    }
    
    if (address == V_RMS_ADDR) {
        return (uint32_t)raw_value / Kv;
    }
    
    if (address == WATT_SUM_ADDR) {
        return raw_value / Kp_sum;
    }
    
    if (address == CF_SUM_ADDR) {
        return (uint32_t)raw_value / Ke_sum;
    }
    
    // 动态检查通道寄存器
    for (int i = 0; i < params->channel_count; i++) {
        if (address == get_register_addr(g_chip_model, BL0906_REG_I_RMS, i)) {
            return (uint32_t)raw_value / Ki;
        }
        if (address == get_register_addr(g_chip_model, BL0906_REG_WATT, i)) {
            return raw_value / Kp;
        }
        if (address == get_register_addr(g_chip_model, BL0906_REG_CF_CNT, i)) {
            return (uint32_t)raw_value / Ke;
        }
    }
    
    // MODE2寄存器直接返回原始值
    if (address == MODE2_ADDR) {
        return (uint32_t)raw_value;
    }
    
    return raw_value;  // 未知寄存器直接返回原始值
}

// 芯片重启检测（从生产版同步）
static void detect_chip_restart(const sensor_data_t* data) {
    if (!g_energy_persistence_enabled) {
        return;
    }
    
    const chip_params_t* params = get_current_chip_params();
    
    // 添加超时机制，防止重启检测永远不恢复（5分钟超时）
    static const uint32_t RESTART_DETECTION_TIMEOUT = 300000;
    if (g_chip_restart_detected && 
        (bl0906_core_get_millis() - g_last_restart_detection_time) > RESTART_DETECTION_TIMEOUT) {
        g_chip_restart_detected = false;
    }
    
    bool all_channels_low = true;
    
    // 检查各通道的CF_count
    for (int i = 0; i < params->channel_count; i++) {
        uint32_t hardware_cf_count = data->channels[i].energy_raw;
        
        if (hardware_cf_count >= 50) {
            all_channels_low = false;
            break;
        }
    }
    
    // 如果之前已检测到重启，检查是否可以重新启用检测
    if (g_chip_restart_detected) {
        if (!all_channels_low) {
            g_chip_restart_detected = false;
        }
        return;
    }
    
    // 只有所有通道的CF_count都小于50时才确定芯片重启
    if (all_channels_low && !g_chip_restart_detected) {
        g_chip_restart_detected = true;
        g_last_restart_detection_time = bl0906_core_get_millis();
        g_chip_restart_count++;
        
        // 重新记录各通道的CF_count值
        for (int i = 0; i < params->channel_count; i++) {
            g_energy_data.last_cf_count[i] = data->channels[i].energy_raw;
        }
        g_energy_data.last_cf_count[params->channel_count] = data->energy_sum_raw;
    }
}

// 持久化存储处理（从生产版同步）
static void process_energy_persistence(const sensor_data_t* data) {
    if (!g_energy_persistence_enabled) {
        return;
    }
    
    const chip_params_t* params = get_current_chip_params();
    
    // 处理各通道
    for (int i = 0; i < params->channel_count; i++) {
        uint32_t current_count = data->channels[i].energy_raw;
        
        if (current_count >= g_energy_data.last_cf_count[i]) {
            uint32_t increment = current_count - g_energy_data.last_cf_count[i];
            g_energy_data.persistent_cf_count[i] += increment;
        } else {
            g_energy_data.persistent_cf_count[i] += current_count;
        }
        
        g_energy_data.last_cf_count[i] = current_count;
    }
    
    // 处理总和
    uint32_t current_sum_count = data->energy_sum_raw;
    const int sum_index = params->channel_count;
    
    if (current_sum_count >= g_energy_data.last_cf_count[sum_index]) {
        uint32_t increment = current_sum_count - g_energy_data.last_cf_count[sum_index];
        g_energy_data.persistent_cf_count[sum_index] += increment;
    } else {
        g_energy_data.persistent_cf_count[sum_index] += current_sum_count;
    }
    
    g_energy_data.last_cf_count[sum_index] = current_sum_count;
}

// 频率检测（从生产版同步）
static float detect_grid_frequency() {
    float freq_sum = 0.0f;
    int valid_samples = 0;
    
    for (int i = 0; i < FREQ_DETECTION_SAMPLES; i++) {
        int32_t freq_raw;
        bl0906_result_t result = g_callbacks.read_register(FREQUENCY_ADDR, &freq_raw);
        
        if (result == BL0906_SUCCESS && freq_raw > 0) {
            float freq = convert_raw_to_value(FREQUENCY_ADDR, freq_raw);
            if (freq > 30.0f && freq < 80.0f) {
                freq_sum += freq;
                valid_samples++;
            }
        }
        
        // 采样间隔
        bl0906_core_delay_ms(100);
    }
    
    if (valid_samples > 0) {
        return freq_sum / valid_samples;
    }
    
    return 0.0f;
}

// 设置AC频率模式（从生产版同步）
static bool set_ac_frequency_mode(bool is_60hz) {
    // 读取当前MODE2寄存器值
    int32_t current_mode2;
    bl0906_result_t result = g_callbacks.read_register(MODE2_ADDR, &current_mode2);
    if (result != BL0906_SUCCESS) {
        return false;
    }
    
    // 修改AC_FREQ_SEL位
    uint32_t new_mode2 = (uint32_t)current_mode2;
    if (is_60hz) {
        new_mode2 |= MODE2_AC_FREQ_SEL_MASK;
    } else {
        new_mode2 &= ~MODE2_AC_FREQ_SEL_MASK;
    }
    
    // 如果值没有变化，直接返回成功
    if (new_mode2 == (uint32_t)current_mode2) {
        return true;
    }
    
    // 写入新值（需要24位写入）
    return bl0906_core_write_register_24bit(MODE2_ADDR, new_mode2) == BL0906_SUCCESS;
}

// ============================================================================
// 预编译库API实现
// ============================================================================

bl0906_result_t bl0906_core_init(bl0906_chip_model_t chip_model, bl0906_comm_type_t comm_type, 
                                 const bl0906_comm_callbacks_t* callbacks) {
    if (!callbacks || !callbacks->read_register || !callbacks->write_register) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    g_chip_model = chip_model;
    g_comm_type = comm_type;
    g_callbacks = *callbacks;
    g_initialized = true;
    
    // 初始化内部状态
    memset(&g_current_data, 0, sizeof(g_current_data));
    memset(&g_energy_data, 0, sizeof(g_energy_data));
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_read_sensor_data(bl0906_sensor_data_t* data) {
    if (!g_initialized || !data) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    const chip_params_t* params = get_current_chip_params();
    
    // 读取基础传感器数据
    int32_t temp_raw, freq_raw, volt_raw;
    if (g_callbacks.read_register(TEMPERATURE_ADDR, &temp_raw) != BL0906_SUCCESS ||
        g_callbacks.read_register(FREQUENCY_ADDR, &freq_raw) != BL0906_SUCCESS ||
        g_callbacks.read_register(V_RMS_ADDR, &volt_raw) != BL0906_SUCCESS) {
        return BL0906_ERROR_COMMUNICATION;
    }
    
    g_current_data.temperature_raw = (uint32_t)temp_raw;
    g_current_data.frequency_raw = (uint32_t)freq_raw;
    g_current_data.voltage_raw = (uint32_t)volt_raw;
    
    // 读取通道数据
    for (int i = 0; i < params->channel_count; i++) {
        int32_t current_raw, power_raw, energy_raw;
        
        if (g_callbacks.read_register(get_register_addr(g_chip_model, BL0906_REG_I_RMS, i), &current_raw) != BL0906_SUCCESS ||
            g_callbacks.read_register(get_register_addr(g_chip_model, BL0906_REG_WATT, i), &power_raw) != BL0906_SUCCESS ||
            g_callbacks.read_register(get_register_addr(g_chip_model, BL0906_REG_CF_CNT, i), &energy_raw) != BL0906_SUCCESS) {
            return BL0906_ERROR_COMMUNICATION;
        }
        
        g_current_data.channels[i].current_raw = (uint32_t)current_raw;
        g_current_data.channels[i].power_raw = power_raw;
        g_current_data.channels[i].energy_raw = (uint32_t)energy_raw;
    }
    
    // 读取总和数据
    int32_t power_sum_raw, energy_sum_raw;
    if (g_callbacks.read_register(WATT_SUM_ADDR, &power_sum_raw) != BL0906_SUCCESS ||
        g_callbacks.read_register(CF_SUM_ADDR, &energy_sum_raw) != BL0906_SUCCESS) {
        return BL0906_ERROR_COMMUNICATION;
    }
    
    g_current_data.power_sum_raw = power_sum_raw;
    g_current_data.energy_sum_raw = (uint32_t)energy_sum_raw;
    g_current_data.read_complete = true;
    g_current_data.timestamp = bl0906_core_get_millis();
    
    // 处理芯片重启检测和持久化
    detect_chip_restart(&g_current_data);
    process_energy_persistence(&g_current_data);
    
    // 填充输出数据
    data->timestamp = g_current_data.timestamp;
    data->temperature = convert_raw_to_value(TEMPERATURE_ADDR, g_current_data.temperature_raw);
    data->frequency = convert_raw_to_value(FREQUENCY_ADDR, g_current_data.frequency_raw);
    data->voltage = convert_raw_to_value(V_RMS_ADDR, g_current_data.voltage_raw);
    data->power_sum = convert_raw_to_value(WATT_SUM_ADDR, g_current_data.power_sum_raw);
    data->energy_sum = convert_raw_to_value(CF_SUM_ADDR, g_current_data.energy_sum_raw);
    
    for (int i = 0; i < params->channel_count; i++) {
        data->channels[i].current = convert_raw_to_value(get_register_addr(g_chip_model, BL0906_REG_I_RMS, i), g_current_data.channels[i].current_raw);
        data->channels[i].power = convert_raw_to_value(get_register_addr(g_chip_model, BL0906_REG_WATT, i), g_current_data.channels[i].power_raw);
        data->channels[i].energy = g_energy_persistence_enabled ? 
            g_energy_data.persistent_cf_count[i] / Ke : 
            convert_raw_to_value(get_register_addr(g_chip_model, BL0906_REG_CF_CNT, i), g_current_data.channels[i].energy_raw);
    }
    
    data->channel_count = params->channel_count;
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_get_register_address(bl0906_register_type_t reg_type, int channel, uint8_t* address) {
    if (!g_initialized || !address) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    uint8_t addr = get_register_addr(g_chip_model, reg_type, channel);
    if (addr == 0) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    *address = addr;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_set_energy_persistence(bool enabled) {
    if (!g_initialized) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    g_energy_persistence_enabled = enabled;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_auto_detect_frequency() {
    if (!g_initialized) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    float detected_freq = detect_grid_frequency();
    if (detected_freq > 0) {
        bool is_60hz = (detected_freq >= FREQ_DETECT_THRESHOLD_LOW && 
                       detected_freq <= FREQ_DETECT_THRESHOLD_HIGH);
        
        if (set_ac_frequency_mode(is_60hz)) {
            return BL0906_SUCCESS;
        }
    }
    
    return BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_write_register_24bit(uint8_t address, uint32_t value) {
    if (!g_initialized) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    // 确保值在24位范围内
    if (value > 0xFFFFFF) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    // 构造24位写入命令
    uint8_t write_cmd[6];
    
    if (g_comm_type == BL0906_COMM_UART) {
        write_cmd[0] = 0xCA;  // UART写命令
        write_cmd[1] = address;
        write_cmd[2] = value & 0xFF;
        write_cmd[3] = (value >> 8) & 0xFF;
        write_cmd[4] = (value >> 16) & 0xFF;
        write_cmd[5] = (write_cmd[0] + write_cmd[1] + write_cmd[2] + write_cmd[3] + write_cmd[4]) ^ 0xFF;
    } else {
        write_cmd[0] = 0x81;  // SPI写命令
        write_cmd[1] = address;
        write_cmd[2] = (value >> 16) & 0xFF;
        write_cmd[3] = (value >> 8) & 0xFF;
        write_cmd[4] = value & 0xFF;
        write_cmd[5] = (write_cmd[0] + write_cmd[1] + write_cmd[2] + write_cmd[3] + write_cmd[4]) ^ 0xFF;
    }
    
    return g_callbacks.send_raw_command(write_cmd, 6);
}

// 函数已在下方定义

const char* bl0906_core_get_error_string(bl0906_result_t result) {
    switch (result) {
        case BL0906_SUCCESS:
            return "Success";
        case BL0906_ERROR_NOT_INITIALIZED:
            return "Not initialized";
        case BL0906_ERROR_INVALID_PARAMETER:
            return "Invalid parameter";
        case BL0906_ERROR_COMMUNICATION:
            return "Communication error";
        case BL0906_ERROR_OPERATION_FAILED:
            return "Operation failed";
        default:
            return "Unknown error";
    }
}

const char* bl0906_core_get_chip_name(void) {
    if (!g_initialized) {
        return "Unknown";
    }
    
    const chip_params_t* params = get_current_chip_params();
    return params->chip_name;
}

int bl0906_core_get_channel_count(void) {
    if (!g_initialized) {
        return 0;
    }
    
    const chip_params_t* params = get_current_chip_params();
    return params->channel_count;
}

bl0906_result_t bl0906_core_set_frequency_mode(bool is_60hz) {
    if (!g_initialized) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    return set_ac_frequency_mode(is_60hz) ? BL0906_SUCCESS : BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_get_persistent_energy(int channel, float* total_energy) {
    if (!g_initialized || !total_energy) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    const chip_params_t* params = get_current_chip_params();
    if (channel < 0 || channel >= params->channel_count) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    if (!g_energy_persistence_enabled) {
        return BL0906_ERROR_OPERATION_FAILED;
    }
    
    *total_energy = g_energy_data.persistent_cf_count[channel] / Ke;
    return BL0906_SUCCESS;
}

// ============================================================================
// 弱符号函数实现（需要由用户实现）
// ============================================================================

__attribute__((weak)) uint32_t bl0906_core_get_millis() {
    // 默认实现，用户应该提供实际的毫秒时间戳
    static uint32_t counter = 0;
    return ++counter;
}

__attribute__((weak)) void bl0906_core_delay_ms(uint32_t ms) {
    // 默认实现，用户应该提供实际的延时函数
    (void)ms;
}

// ============================================================================
// I2C EEPROM存储API实现
// ============================================================================

#ifdef USE_I2C_EEPROM_CALIBRATION

bl0906_result_t bl0906_core_init_eeprom_storage(bl0906_eeprom_type_t eeprom_type, uint8_t i2c_address) {
    // 清理之前的实例
    if (g_eeprom_storage != nullptr) {
        delete g_eeprom_storage;
        g_eeprom_storage = nullptr;
    }
    if (g_i2c_interface != nullptr) {
        delete g_i2c_interface;
        g_i2c_interface = nullptr;
    }

    // 根据EEPROM类型确定数据长度
    size_t data_len;
    switch (eeprom_type) {
        case BL0906_EEPROM_24C02:
            data_len = 256;  // 24C02: 256 bytes
            break;
        case BL0906_EEPROM_24C04:
            data_len = 512;  // 24C04: 512 bytes
            break;
        case BL0906_EEPROM_24C08:
            data_len = 1024; // 24C08: 1024 bytes
            break;
        case BL0906_EEPROM_24C16:
            data_len = 2048; // 24C16: 2048 bytes
            break;
        default:
            return BL0906_ERROR_INVALID_PARAMETER;
    }

    // 创建I2C接口实例
    g_i2c_interface = new SimpleI2CInterface(i2c_address);
    if (g_i2c_interface == nullptr) {
        return BL0906_ERROR_OPERATION_FAILED;
    }

    // 创建EEPROM存储实例
    g_eeprom_storage = new I2CEepromCalibrationStorage(g_i2c_interface, i2c_address, data_len);
    
    if (g_eeprom_storage == nullptr) {
        return BL0906_ERROR_OPERATION_FAILED;
    }
    
    if (!g_eeprom_storage->init()) {
        delete g_eeprom_storage;
        g_eeprom_storage = nullptr;
        delete g_i2c_interface;
        g_i2c_interface = nullptr;
        return BL0906_ERROR_OPERATION_FAILED;
    }
    
    g_eeprom_initialized = true;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_calculate_calibration_coefficients(bl0906_voltage_sampling_mode_t sampling_mode,
                                                              const bl0906_reference_params_t* ref_params,
                                                              bl0906_calibration_coefficients_t* coefficients) {
    if (!ref_params || !coefficients) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }

    // 从参考参数中提取值
    float Vref = ref_params->Vref;
    int Gain_V = ref_params->Gain_V;
    int Gain_I = ref_params->Gain_I;
    float RL = ref_params->RL;
    float Rt = ref_params->Rt;

    // 根据电压采样模式计算校准系数
    if (sampling_mode == (bl0906_voltage_sampling_mode_t)0) {  // BL0906_VOLTAGE_SAMPLING_TRANSFORMER
        // 电压互感器采样方式参数
        float Rf = 100000.0f;      // 分压上拉电阻 (Ω)
        int R46 = 100;             // 电压采样电阻 (Ω)

        // 校准系数计算
        coefficients->Ki = (12875 * Gain_I * (RL + RL) * 1000 / Rt) / Vref;
        coefficients->Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf);
        coefficients->Kp = 2.3847e-7 * coefficients->Ki * coefficients->Kv;
        coefficients->Ke = (3600000.0f * 16 * coefficients->Kp) / (4194304.0f * 0.032768f * 16);

    } else if (sampling_mode == (bl0906_voltage_sampling_mode_t)1) {  // BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER
        // 电压分压电阻采样方式参数
        float Rf = 1500.0f;        // 分压上拉电阻 (kΩ)
        float Rv = 1.0f;           // 分压下拉电阻 (kΩ)

        // 校准系数计算
        coefficients->Ki = 12875.0f * Gain_I * 2 * RL * 1000 / Rt / Vref;
        coefficients->Kv = 13162.0f * Rv * 1000 * Gain_V / (Vref * (Rf + Rv));
        coefficients->Kp = (40.4125 * Gain_V * Gain_I * RL * 2 * 1000 / Rt) * Rv * 1000/(Vref * Vref * (Rf + Rv));
        coefficients->Ke = (3600000.0f * 16 * coefficients->Kp) / (4194304.0f * 0.032768f * 16);

    } else {
        return BL0906_ERROR_INVALID_PARAMETER;
    }

    // 通用校准系数（两种模式都使用）
    coefficients->Kp_sum = coefficients->Kp / 16;
    coefficients->Ke_sum = coefficients->Ke / 16;
    coefficients->FREF = 1 / 10000000.0f;     // 频率转换
    coefficients->TREF = 59 - 40 / 12.5f;     // 温度转换

    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_get_default_reference_params(bl0906_voltage_sampling_mode_t sampling_mode,
                                                        bl0906_reference_params_t* ref_params) {
    if (!ref_params) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }

    // 设置默认参考参数
    ref_params->Vref = 1.097f;           // 内部参考电压 (V)
    ref_params->Gain_V = 1;              // 电压通道增益 (1, 2, 8, 16)
    ref_params->Gain_I = 1;              // 电流通道增益 (1, 2, 8, 16)
    ref_params->RL = 5.1f;               // 互感器副边负载电阻 (Ω)
    ref_params->Rt = 2000.0f;            // 互感器变比，如 2000:1

    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_load_calibration_from_eeprom(uint32_t instance_id,
                                                        bl0906_calibration_entry_t* entries,
                                                        size_t max_entries,
                                                        size_t* actual_count) {
    if (!g_eeprom_initialized || g_eeprom_storage == nullptr) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    if (entries == nullptr || actual_count == nullptr) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    std::vector<CalibrationEntry> storage_entries;
    if (!g_eeprom_storage->read_instance(instance_id, storage_entries)) {
        return BL0906_ERROR_OPERATION_FAILED;
    }
    
    size_t count = std::min(storage_entries.size(), max_entries);
    for (size_t i = 0; i < count; i++) {
        entries[i].register_addr = storage_entries[i].register_addr;
        entries[i].value = storage_entries[i].value;
    }
    
    *actual_count = count;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_save_calibration_to_eeprom(uint32_t instance_id,
                                                      const bl0906_calibration_entry_t* entries,
                                                      size_t entry_count) {
    if (!g_eeprom_initialized || g_eeprom_storage == nullptr) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    if (entries == nullptr) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    std::vector<CalibrationEntry> storage_entries;
    for (size_t i = 0; i < entry_count; i++) {
        CalibrationEntry entry;
        entry.register_addr = entries[i].register_addr;
        entry.value = entries[i].value;
        storage_entries.push_back(entry);
    }
    
    if (!g_eeprom_storage->write_instance(instance_id, storage_entries)) {
        return BL0906_ERROR_OPERATION_FAILED;
    }
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_get_eeprom_instance_list(uint32_t* instance_ids,
                                                    size_t max_instances,
                                                    size_t* actual_count) {
    if (!g_eeprom_initialized || g_eeprom_storage == nullptr) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    if (instance_ids == nullptr || actual_count == nullptr) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    std::vector<uint32_t> storage_instances = g_eeprom_storage->get_instance_list();
    size_t count = std::min(storage_instances.size(), max_instances);
    
    for (size_t i = 0; i < count; i++) {
        instance_ids[i] = storage_instances[i];
    }
    
    *actual_count = count;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_clear_eeprom_storage(void) {
    if (!g_eeprom_initialized || g_eeprom_storage == nullptr) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    if (!g_eeprom_storage->erase()) {
        return BL0906_ERROR_OPERATION_FAILED;
    }
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_get_eeprom_storage_status(size_t* max_instances, size_t* used_instances) {
    if (!g_eeprom_initialized || g_eeprom_storage == nullptr) {
        return BL0906_ERROR_NOT_INITIALIZED;
    }
    
    if (max_instances == nullptr || used_instances == nullptr) {
        return BL0906_ERROR_INVALID_PARAMETER;
    }
    
    *max_instances = g_eeprom_storage->get_max_instances();
    *used_instances = g_eeprom_storage->get_instance_list().size();
    
    return BL0906_SUCCESS;
}

#else  // USE_I2C_EEPROM_CALIBRATION

// 当未启用I2C EEPROM校准存储时，提供空实现
bl0906_result_t bl0906_core_init_eeprom_storage(bl0906_eeprom_type_t eeprom_type, uint8_t i2c_address) {
    (void)eeprom_type;
    (void)i2c_address;
    return BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_load_calibration_from_eeprom(uint32_t instance_id,
                                                        bl0906_calibration_entry_t* entries,
                                                        size_t max_entries,
                                                        size_t* actual_count) {
    (void)instance_id;
    (void)entries;
    (void)max_entries;
    (void)actual_count;
    return BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_save_calibration_to_eeprom(uint32_t instance_id,
                                                      const bl0906_calibration_entry_t* entries,
                                                      size_t entry_count) {
    (void)instance_id;
    (void)entries;
    (void)entry_count;
    return BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_get_eeprom_instance_list(uint32_t* instance_ids,
                                                    size_t max_instances,
                                                    size_t* actual_count) {
    (void)instance_ids;
    (void)max_instances;
    (void)actual_count;
    return BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_clear_eeprom_storage(void) {
    return BL0906_ERROR_OPERATION_FAILED;
}

bl0906_result_t bl0906_core_get_eeprom_storage_status(size_t* max_instances, size_t* used_instances) {
    (void)max_instances;
    (void)used_instances;
    return BL0906_ERROR_OPERATION_FAILED;
}

#endif  // USE_I2C_EEPROM_CALIBRATION

// ============================================================================
// 通信适配器工厂函数实现（发布版专用）
// ============================================================================

#ifdef __cplusplus

// 包含通信适配器相关头文件
#include "communication_adapter_interface.h"
#include "communication_adapter_base.h"

// 前向声明ESPHome类型（避免包含ESPHome头文件）
namespace esphome {
namespace uart {
    class UARTComponent;
}
namespace spi {
    class SPIComponent;
}
class GPIOPin;
}

namespace bl0906_core {

// UART适配器的具体实现类
class UartAdapterImpl : public CommunicationAdapterBase {
public:
    UartAdapterImpl(esphome::uart::UARTComponent* uart_component)
        : uart_component_(uart_component) {}

    bool initialize() override {
        if (!uart_component_) {
            set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "UART组件为空");
            return false;
        }
        return true;
    }

    int32_t read_register(uint8_t address, bool* success = nullptr) override {
        // 简化实现：返回模拟数据
        if (success) *success = true;
        return 0x123456;  // 模拟数据
    }

    bool write_register(uint8_t address, int16_t value) override {
        // 简化实现：总是返回成功
        (void)address;
        (void)value;
        return true;
    }

    bool send_raw_command(const uint8_t* command, size_t length) override {
        // 简化实现：总是返回成功
        (void)command;
        (void)length;
        return true;
    }

    bool is_available() override {
        return uart_component_ != nullptr;
    }

    bool is_connected() override {
        return uart_component_ != nullptr;
    }

    void flush_buffer() override {
        // 简化实现：无操作
    }

    std::string get_adapter_type() const override {
        return "UART";
    }

    std::string get_status_info() const override {
        return uart_component_ ? "UART适配器已连接" : "UART适配器未连接";
    }

    bool self_test() override {
        return uart_component_ != nullptr;
    }

private:
    esphome::uart::UARTComponent* uart_component_;
};

// SPI适配器的具体实现类
class SpiAdapterImpl : public CommunicationAdapterBase {
public:
    SpiAdapterImpl(esphome::spi::SPIComponent* spi_component, esphome::GPIOPin* cs_pin)
        : spi_component_(spi_component), cs_pin_(cs_pin) {}

    bool initialize() override {
        if (!spi_component_ || !cs_pin_) {
            set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "SPI组件或CS引脚为空");
            return false;
        }
        return true;
    }

    int32_t read_register(uint8_t address, bool* success = nullptr) override {
        // 简化实现：返回模拟数据
        if (success) *success = true;
        return 0x789ABC;  // 模拟数据
    }

    bool write_register(uint8_t address, int16_t value) override {
        // 简化实现：总是返回成功
        (void)address;
        (void)value;
        return true;
    }

    bool send_raw_command(const uint8_t* command, size_t length) override {
        // 简化实现：总是返回成功
        (void)command;
        (void)length;
        return true;
    }

    bool is_available() override {
        return spi_component_ != nullptr && cs_pin_ != nullptr;
    }

    bool is_connected() override {
        return spi_component_ != nullptr && cs_pin_ != nullptr;
    }

    void flush_buffer() override {
        // 简化实现：无操作
    }

    std::string get_adapter_type() const override {
        return "SPI";
    }

    std::string get_status_info() const override {
        if (spi_component_ && cs_pin_) {
            return "SPI适配器已连接";
        } else {
            return "SPI适配器未连接";
        }
    }

    bool self_test() override {
        return spi_component_ != nullptr && cs_pin_ != nullptr;
    }

private:
    esphome::spi::SPIComponent* spi_component_;
    esphome::GPIOPin* cs_pin_;
};

} // namespace bl0906_core

// C接口的工厂函数实现
extern "C" {

void* bl0906_core_create_uart_adapter(void* uart_component) {
    if (!uart_component) {
        return nullptr;
    }

    auto* adapter = new bl0906_core::UartAdapterImpl(
        static_cast<esphome::uart::UARTComponent*>(uart_component)
    );

    if (adapter && adapter->initialize()) {
        return adapter;
    } else {
        delete adapter;
        return nullptr;
    }
}

void* bl0906_core_create_spi_adapter(void* spi_component, void* cs_pin) {
    if (!spi_component || !cs_pin) {
        return nullptr;
    }

    auto* adapter = new bl0906_core::SpiAdapterImpl(
        static_cast<esphome::spi::SPIComponent*>(spi_component),
        static_cast<esphome::GPIOPin*>(cs_pin)
    );

    if (adapter && adapter->initialize()) {
        return adapter;
    } else {
        delete adapter;
        return nullptr;
    }
}

void bl0906_core_destroy_adapter(void* adapter) {
    if (adapter) {
        delete static_cast<bl0906_core::CommunicationAdapterInterface*>(adapter);
    }
}

} // extern "C"

#endif // __cplusplus