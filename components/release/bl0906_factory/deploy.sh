#!/bin/bash
# BL0906 Factory 通用部署脚本
# 用途：根据参数选择构建发布版或生产版

VERSION_TYPE=${1:-release}

echo "=== BL0906 Factory 自动化部署 ==="
echo "版本类型: $VERSION_TYPE"
echo ""

case $VERSION_TYPE in
    "release")
        echo "构建发布版..."
        ./build_release.sh
        if [ $? -eq 0 ]; then
            echo ""
            echo "🎉 发布版构建成功！"
            echo ""
            echo "=== 发布版使用说明 ==="
            echo "发布版特点："
            echo "  ✓ 用户友好的简化配置"
            echo "  ✓ 移除校准调试功能"
            echo "  ✓ 容错运行模式"
            echo "  ✓ 核心算法保护"
            echo "  ✓ 运行时芯片型号支持"
            echo "  ✓ 电量持久化存储"
            echo "  ✓ 频率自动适配"
            echo "  ✓ I2C EEPROM校准存储（默认启用）"
            echo ""
            echo "适用场景："
            echo "  - 最终用户部署"
            echo "  - 产品发布"
            echo "  - 生产环境使用"
            echo ""
            echo "配置示例："
            echo "  bl0906_factory:"
            echo "    chip_model: bl0906"
            echo "    communication: uart"
            echo "    instance_id: 0x12345678"
            echo "    uart_id: uart_bus"
            echo "    energy_persistence: true"
            echo "    freq_adapt: auto"
            echo "    fault_tolerant: true"
            echo "    # I2C EEPROM存储配置"
            echo "    eeprom_type: 24c08"
            echo "    i2c_address: 0x50"
            echo "    i2c_id: i2c_bus"
            echo ""
            echo "传感器配置："
            echo "  sensor:"
            echo "    - platform: bl0906_factory"
            echo "      bl0906_factory_id: my_bl0906"
            echo "      voltage:"
            echo "        name: \"电压\""
            echo "      current_1:"
            echo "        name: \"通道1电流\""
            echo "      power_1:"
            echo "        name: \"通道1功率\""
            echo "      energy_1:"
            echo "        name: \"通道1电量\""
        else
            echo "❌ 发布版构建失败"
            exit 1
        fi
        ;;
    "production")
        echo "构建生产版..."
        ./build_production.sh
        if [ $? -eq 0 ]; then
            echo ""
            echo "🎉 生产版构建成功！"
            echo ""
            echo "=== 生产版使用说明 ==="
            echo "生产版特点："
            echo "  ✓ 完整的校准调试功能"
            echo "  ✓ number 组件支持"
            echo "  ✓ 初始校准值配置"
            echo "  ✓ 开发调试友好"
            echo "  ✓ 运行时芯片型号支持"
            echo "  ✓ 电量持久化存储"
            echo "  ✓ 电量统计功能"
            echo "  ✓ 频率自动适配"
            echo ""
            echo "适用场景："
            echo "  - 开发调试"
            echo "  - 生产制造过程"
            echo "  - 校准测试"
            echo ""
            echo "⚠️  重要提醒："
            echo "  构建完成后请及时恢复发布版配置："
            echo "  ./restore_release.sh"
            echo ""
            echo "配置示例："
            echo "  bl0906_factory:"
            echo "    chip_model: bl0906"
            echo "    communication: uart"
            echo "    instance_id: 0x12345678"
            echo "    uart_id: uart_bus"
            echo "    calibration_mode: true"
            echo "    calibration:"
            echo "      storage_type: preferences"
            echo "      enabled: true"
            echo "    energy_statistics: true"
            echo "    time_id: sntp_time"
        else
            echo "❌ 生产版构建失败"
            exit 1
        fi
        ;;
    "help"|"-h"|"--help")
        echo "使用方法："
        echo "  $0 [release|production]"
        echo ""
        echo "参数说明："
        echo "  release     构建发布版（默认）"
        echo "  production  构建生产版"
        echo "  help        显示帮助信息"
        echo ""
        echo "版本对比："
        echo "  发布版："
        echo "    - 面向最终用户"
        echo "    - 简化配置"
        echo "    - 容错模式"
        echo "    - 核心算法保护"
        echo "    - 无校准功能"
        echo ""
        echo "  生产版："
        echo "    - 面向开发者"
        echo "    - 完整功能"
        echo "    - 校准调试"
        echo "    - 统计功能"
        echo "    - 开发友好"
        ;;
    *)
        echo "❌ 未知的版本类型: $VERSION_TYPE"
        echo "支持的版本类型: release, production"
        echo "使用 '$0 help' 查看详细帮助"
        exit 1
        ;;
esac

echo ""
echo "=== 部署完成 ===" 