#pragma once

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// 结果状态枚举
typedef enum {
    BL0906_HAL_OK = 0,
    BL0906_HAL_ERROR = 1,
    BL0906_HAL_TIMEOUT = 2,
    BL0906_HAL_INVALID_PARAM = 3
} bl0906_hal_result_t;

// 延时函数类型
typedef void (*bl0906_delay_ms_func_t)(uint32_t ms);
typedef void (*bl0906_delay_us_func_t)(uint32_t us);

// I2C操作函数类型
typedef bl0906_hal_result_t (*bl0906_i2c_read_func_t)(uint8_t device_addr, uint8_t reg_addr, uint8_t* data, size_t length);
typedef bl0906_hal_result_t (*bl0906_i2c_write_func_t)(uint8_t device_addr, uint8_t reg_addr, const uint8_t* data, size_t length);
typedef bl0906_hal_result_t (*bl0906_i2c_read_bytes_func_t)(uint8_t device_addr, uint8_t* data, size_t length);
typedef bl0906_hal_result_t (*bl0906_i2c_write_bytes_func_t)(uint8_t device_addr, const uint8_t* data, size_t length);

// 内存操作函数类型
typedef void* (*bl0906_malloc_func_t)(size_t size);
typedef void (*bl0906_free_func_t)(void* ptr);

// HAL回调函数结构
typedef struct {
    // 延时函数
    bl0906_delay_ms_func_t delay_ms;
    bl0906_delay_us_func_t delay_us;
    
    // I2C操作函数
    bl0906_i2c_read_func_t i2c_read;
    bl0906_i2c_write_func_t i2c_write;
    bl0906_i2c_read_bytes_func_t i2c_read_bytes;
    bl0906_i2c_write_bytes_func_t i2c_write_bytes;
    
    // 内存管理函数
    bl0906_malloc_func_t malloc_func;
    bl0906_free_func_t free_func;
} bl0906_hal_callbacks_t;

// 默认HAL实现
void bl0906_hal_delay_ms_default(uint32_t ms);
void bl0906_hal_delay_us_default(uint32_t us);
void* bl0906_hal_malloc_default(size_t size);
void bl0906_hal_free_default(void* ptr);

// HAL接口函数
bl0906_hal_result_t bl0906_hal_init(const bl0906_hal_callbacks_t* callbacks);
void bl0906_hal_delay_ms(uint32_t ms);
void bl0906_hal_delay_us(uint32_t us);
bl0906_hal_result_t bl0906_hal_i2c_read(uint8_t device_addr, uint8_t reg_addr, uint8_t* data, size_t length);
bl0906_hal_result_t bl0906_hal_i2c_write(uint8_t device_addr, uint8_t reg_addr, const uint8_t* data, size_t length);
bl0906_hal_result_t bl0906_hal_i2c_read_bytes(uint8_t device_addr, uint8_t* data, size_t length);
bl0906_hal_result_t bl0906_hal_i2c_write_bytes(uint8_t device_addr, const uint8_t* data, size_t length);
void* bl0906_hal_malloc(size_t size);
void bl0906_hal_free(void* ptr);

#ifdef __cplusplus
}

// C++ 命名空间兼容
namespace bl0906_core {

// C++ HAL类
class HAL {
public:
    static bl0906_hal_result_t init(const bl0906_hal_callbacks_t* callbacks) {
        return bl0906_hal_init(callbacks);
    }
    
    static void delay_ms(uint32_t ms) {
        bl0906_hal_delay_ms(ms);
    }
    
    static void delay_us(uint32_t us) {
        bl0906_hal_delay_us(us);
    }
    
    static bl0906_hal_result_t i2c_read(uint8_t device_addr, uint8_t reg_addr, uint8_t* data, size_t length) {
        return bl0906_hal_i2c_read(device_addr, reg_addr, data, length);
    }
    
    static bl0906_hal_result_t i2c_write(uint8_t device_addr, uint8_t reg_addr, const uint8_t* data, size_t length) {
        return bl0906_hal_i2c_write(device_addr, reg_addr, data, length);
    }
};

}  // namespace bl0906_core

#endif 