#!/bin/bash
# BL0906 Factory 发布版构建脚本
# 用途：构建用户友好的发布版本，支持多架构预编译库

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}=== BL0906 Factory 发布版构建 ===${NC}"
}

print_step() {
    echo -e "${YELLOW}[步骤] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 支持的架构列表
SUPPORTED_ARCHS=("esp32" "esp32s2" "esp32s3" "esp32c3" "esp32c6" "esp32h2")

# 检查当前目录
check_environment() {
    print_step "检查构建环境..."

    if [ ! -f "__init__.py" ]; then
        print_error "未找到 __init__.py 文件，请在 bl0906_factory 组件目录中运行此脚本"
        exit 1
    fi

    if [ ! -d "calibration_storage_lib" ]; then
        print_error "未找到 calibration_storage_lib 目录"
        exit 1
    fi

    if [ ! -d "communication" ]; then
        print_error "未找到 communication 目录"
        exit 1
    fi

    print_success "环境检查通过"
}

# 检查ESP-IDF工具链是否安装
check_esp_idf_toolchain() {
    print_step "检查ESP-IDF工具链..."

    # 检查ESP-IDF环境变量
    if [ -z "$IDF_PATH" ]; then
        print_error "未找到ESP-IDF环境变量 IDF_PATH"
        print_info "请按以下步骤安装ESP-IDF:"
        print_info "1. 克隆ESP-IDF: git clone --recursive https://github.com/espressif/esp-idf.git"
        print_info "2. 进入目录: cd esp-idf"
        print_info "3. 安装工具: ./install.sh"
        print_info "4. 设置环境: . ./export.sh"
        print_info "5. 或者运行: get_idf"
        return 1
    fi

    # 检查工具链是否在PATH中
    local missing_tools=()

    if ! command -v xtensa-esp32-elf-gcc >/dev/null 2>&1; then
        missing_tools+=("xtensa-esp32-elf-gcc")
    fi

    if ! command -v riscv32-esp-elf-gcc >/dev/null 2>&1; then
        missing_tools+=("riscv32-esp-elf-gcc")
    fi

    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "缺少ESP32工具链: ${missing_tools[*]}"
        print_info "请运行以下命令设置ESP-IDF环境:"
        print_info "  source \$IDF_PATH/export.sh"
        print_info "或者运行: get_idf"
        return 1
    fi

    print_success "ESP-IDF工具链检查通过"
    print_info "IDF_PATH: $IDF_PATH"
    return 0
}

# 获取架构对应的编译器配置
get_compiler_config() {
    local arch=$1

    case $arch in
        "esp32")
            echo "xtensa-esp32-elf-gcc xtensa-esp32-elf-g++ xtensa-esp32-elf-ar -mlongcalls"
            ;;
        "esp32s2")
            echo "xtensa-esp32s2-elf-gcc xtensa-esp32s2-elf-g++ xtensa-esp32s2-elf-ar -mlongcalls"
            ;;
        "esp32s3")
            echo "xtensa-esp32s3-elf-gcc xtensa-esp32s3-elf-g++ xtensa-esp32s3-elf-ar -mlongcalls"
            ;;
        "esp32c3")
            echo "riscv32-esp-elf-gcc riscv32-esp-elf-g++ riscv32-esp-elf-ar -march=rv32imc -mabi=ilp32"
            ;;
        "esp32c6")
            echo "riscv32-esp-elf-gcc riscv32-esp-elf-g++ riscv32-esp-elf-ar -march=rv32imac -mabi=ilp32"
            ;;
        "esp32h2")
            echo "riscv32-esp-elf-gcc riscv32-esp-elf-g++ riscv32-esp-elf-ar -march=rv32imac -mabi=ilp32"
            ;;
        *)
            print_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 构建calibration_storage_lib
build_calibration_storage_lib() {
    local arch=$1
    print_step "构建 calibration_storage_lib ($arch)..."

    cd calibration_storage_lib

    # 获取编译器配置
    local compiler_config=($(get_compiler_config $arch))
    local CC="${compiler_config[0]}"
    local CXX="${compiler_config[1]}"
    local AR="${compiler_config[2]}"
    local ARCH_FLAGS="${compiler_config[3]} ${compiler_config[4]:-}"

    # 清理之前的构建
    make clean > /dev/null 2>&1 || true

    # 使用交叉编译器构建
    make CC="$CXX" \
         CFLAGS="-Iinclude -Wall -Os -std=c++17 -fno-rtti -fno-exceptions $ARCH_FLAGS" \
         AR="$AR" \
         TARGET="libcalibration_storage_${arch}.a"

    if [ $? -eq 0 ]; then
        print_success "calibration_storage_lib ($arch) 构建成功"
        # 复制到release目录
        cp "libcalibration_storage_${arch}.a" "../release/"
    else
        print_error "calibration_storage_lib ($arch) 构建失败"
        cd ..
        return 1
    fi

    cd ..
}

# 构建communication库
build_communication_lib() {
    local arch=$1
    print_step "构建 communication 库 ($arch)..."

    cd communication

    # 获取编译器配置
    local compiler_config=($(get_compiler_config $arch))
    local CC="${compiler_config[0]}"
    local AR="${compiler_config[2]}"
    local ARCH_FLAGS="${compiler_config[3]} ${compiler_config[4]:-}"

    # 清理之前的构建
    make clean > /dev/null 2>&1 || true

    # 创建架构特定的库目录
    mkdir -p "libs_${arch}"

    # 使用交叉编译器构建
    make CC="$CC" \
         AR="$AR" \
         CFLAGS="-Os -ffunction-sections -fdata-sections -Wall -Wextra -std=c99 $ARCH_FLAGS" \
         LIBS_DIR="libs_${arch}" \
         COMMON_LIB="libs_${arch}/libbl0906_comm_common_${arch}.a" \
         UART_LIB="libs_${arch}/libbl0906_uart_comm_${arch}.a" \
         SPI_LIB="libs_${arch}/libbl0906_spi_comm_${arch}.a"

    if [ $? -eq 0 ]; then
        print_success "communication 库 ($arch) 构建成功"
        # 复制到release目录
        cp "libs_${arch}"/*.a "../release/"
    else
        print_error "communication 库 ($arch) 构建失败"
        cd ..
        return 1
    fi

    cd ..
}

# 构建核心预编译库
build_core_library() {
    local arch=$1
    print_step "构建核心预编译库 ($arch)..."

    if [ -f "build_precompiled_lib.sh" ]; then
        chmod +x build_precompiled_lib.sh
        ./build_precompiled_lib.sh $arch

        if [ $? -eq 0 ]; then
            print_success "核心预编译库 ($arch) 构建成功"
        else
            print_error "核心预编译库 ($arch) 构建失败"
            return 1
        fi
    else
        print_error "未找到 build_precompiled_lib.sh"
        return 1
    fi
}

# 验证发布版配置
validate_release_config() {
    print_step "验证发布版配置..."

    # 检查必要文件
    local required_files=(
        "bl0906_wrapper.cpp"
        "bl0906_wrapper.h"
        "sensor.py"
        "__init__.py"
    )

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "缺少必要文件: $file"
            exit 1
        fi
    done

    # 验证发布版配置 - 检查AUTO_LOAD数组中是否包含"number"
    if grep "AUTO_LOAD" __init__.py | grep -o '\[.*\]' | grep -q '"number"'; then
        print_error "发布版不应包含 number 组件"
        exit 1
    fi

    print_success "发布版配置验证通过"
}

# 设置构建环境
setup_build_environment() {
    print_step "设置构建环境..."

    export BUILD_TYPE=release
    export CPPFLAGS="-DBL0906_PRODUCTION_BUILD -DBL0906_READONLY_CALIBRATION -DBL0906_FAULT_TOLERANT"

    print_info "发布版编译标志："
    print_info "  - BL0906_PRODUCTION_BUILD: 发布版标识"
    print_info "  - BL0906_READONLY_CALIBRATION: 只读校准模式"
    print_info "  - BL0906_FAULT_TOLERANT: 容错运行模式"

    print_success "构建环境设置完成"
}

# 显示使用帮助
show_usage() {
    echo "用法: $0 [选项] [架构...]"
    echo ""
    echo "选项:"
    echo "  --all              构建所有支持的架构"
    echo "  --skip-sublibs     跳过子库构建，只构建核心库"
    echo "  --skip-toolchain   跳过工具链检查（仅用于测试）"
    echo "  --clean            构建前清理所有文件"
    echo "  --verbose          显示详细输出"
    echo "  --help             显示此帮助信息"
    echo ""
    echo "支持的架构:"
    for arch in "${SUPPORTED_ARCHS[@]}"; do
        echo "  $arch"
    done
    echo ""
    echo "示例:"
    echo "  $0                          # 构建默认配置（ESP32-C3, SPI, EEPROM）"
    echo "  $0 --all                    # 构建所有架构"
    echo "  $0 esp32c3 esp32s3          # 构建指定架构"
    echo "  $0 --skip-sublibs esp32c3   # 只构建核心库"
    echo "  $0 --clean --all            # 清理后构建所有架构"
    echo ""
    echo "环境变量配置:"
    echo "  COMMUNICATION_MODE=spi      # 通信模式（默认：spi）"
    echo "  VOLTAGE_SAMPLING_MODE=transformer  # 电压采样模式（默认：transformer）"
    echo ""
    echo "ESP-IDF环境设置:"
    echo "  source \$IDF_PATH/export.sh  # 设置ESP-IDF环境"
    echo "  get_idf                     # 使用别名设置环境"
}

# 清理构建文件
clean_build_files() {
    print_step "清理构建文件..."

    # 清理calibration_storage_lib
    if [ -d "calibration_storage_lib" ]; then
        cd calibration_storage_lib
        make clean > /dev/null 2>&1 || true
        rm -f libcalibration_storage_*.a
        cd ..
    fi

    # 清理communication
    if [ -d "communication" ]; then
        cd communication
        make clean > /dev/null 2>&1 || true
        rm -rf libs_*
        cd ..
    fi

    # 清理release目录
    if [ -d "release" ]; then
        rm -f release/libcalibration_storage_*.a
        rm -f release/libbl0906_comm_*_*.a
        rm -f release/libbl0906_core_*.a
    fi

    print_success "清理完成"
}

# 显示构建结果
show_build_results() {
    print_step "构建结果汇总..."

    echo ""
    print_info "=== 发布版特性 ==="
    print_info "✓ 移除 number 组件（校准调试功能）"
    print_info "✓ 容错运行模式（校准数据缺失时继续工作）"
    print_info "✓ 只读校准数据（从存储读取，不可修改）"
    print_info "✓ 简化配置（用户友好）"
    print_info "✓ 算法保护（核心功能通过预编译库保护）"
    print_info "✓ 多架构支持（ESP32系列全覆盖）"

    echo ""
    print_info "=== 生成的库文件 ==="
    if [ -d "release" ]; then
        ls -la release/*.a 2>/dev/null | while read line; do
            print_info "$line"
        done
    fi

    echo ""
    print_success "发布版构建完成！"
    print_info "当前配置为发布版，适用于最终用户部署"
    print_info "如需切换到生产版，请运行: ./build_production.sh"
}

# 主函数
main() {
    print_header

    # 解析命令行参数
    local build_all=false
    local skip_sublibs=false
    local skip_toolchain=false
    local clean_first=false
    local verbose=false
    local target_archs=()

    while [[ $# -gt 0 ]]; do
        case $1 in
            --all)
                build_all=true
                shift
                ;;
            --skip-sublibs)
                skip_sublibs=true
                shift
                ;;
            --skip-toolchain)
                skip_toolchain=true
                shift
                ;;
            --clean)
                clean_first=true
                shift
                ;;
            --verbose)
                verbose=true
                set -x  # 启用详细输出
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            esp32|esp32s2|esp32s3|esp32c3|esp32c6|esp32h2)
                target_archs+=("$1")
                shift
                ;;
            -*)
                print_error "未知选项: $1"
                show_usage
                exit 1
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # 确定要构建的架构
    if [ "$build_all" = true ]; then
        target_archs=("${SUPPORTED_ARCHS[@]}")
    elif [ ${#target_archs[@]} -eq 0 ]; then
        # 默认构建ESP32-C3
        target_archs=("esp32c3")
        print_info "未指定架构，默认构建: esp32c3"
    fi

    # 执行构建步骤
    check_environment
    validate_release_config
    setup_build_environment

    # 检查ESP-IDF工具链（除非跳过）
    if [ "$skip_toolchain" = false ]; then
        if ! check_esp_idf_toolchain; then
            print_error "ESP-IDF工具链检查失败，无法进行交叉编译"
            print_info ""
            print_info "快速解决方案："
            print_info "1. 如果已安装ESP-IDF，运行: source \$IDF_PATH/export.sh"
            print_info "2. 如果使用get_idf别名，运行: get_idf"
            print_info "3. 如果未安装，请先安装ESP-IDF开发环境"
            print_info "4. 或者使用 --skip-toolchain 选项跳过检查（仅用于测试）"
            exit 1
        fi
    else
        print_info "跳过ESP-IDF工具链检查（测试模式）"
    fi

    if [ "$clean_first" = true ]; then
        clean_build_files
    fi

    # 确保release目录存在
    mkdir -p release

    # 为每个架构构建库
    for arch in "${target_archs[@]}"; do
        print_step "开始构建架构: $arch"

        if [ "$skip_sublibs" = false ]; then
            # 构建子库
            build_calibration_storage_lib "$arch" || exit 1
            build_communication_lib "$arch" || exit 1
        fi

        # 构建核心库
        build_core_library "$arch" || exit 1

        print_success "架构 $arch 构建完成"
        echo ""
    done

    show_build_results
}

# 错误处理
trap 'print_error "构建过程中发生错误，退出码: $?"' ERR

# 运行主函数
main "$@"