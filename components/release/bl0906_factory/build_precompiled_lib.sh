#!/bin/bash

# BL0906 Factory 预编译库构建脚本
# 此脚本将核心算法编译为预编译库，保护关键IP
# 支持多架构编译：ESP32, ESP32-S2, ESP32-S3, ESP32-C3

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${YELLOW}[步骤] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 项目配置
PROJECT_NAME="bl0906_factory"
VERSION="2.0.0"

# 全局变量（将在架构检测后设置）
TARGET_ARCH=""
CC=""
CXX=""
AR=""
STRIP=""
ARCH_FLAGS=""
ARCH_NAME=""
CORE_LIB_NAME=""
CFLAGS=""
CXXFLAGS=""

# 目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
BUILD_DIR="$PROJECT_DIR/build"
RELEASE_DIR="$PROJECT_DIR/release"
TEMP_DIR="$PROJECT_DIR/temp"

# 源文件配置（修正路径）
CORE_SOURCES=(
    "bl0906_core_impl.cpp"
    "bl0906_log_interface.cpp"
    "bl0906_hal_interface.cpp"
    "communication_adapter_base.cpp"
    "calibration_storage_lib/src/calibration_storage_base.cpp"
    "calibration_storage_lib/src/i2c_eeprom_calibration_storage.cpp"
    "communication/common/bl0906_comm_common_impl.c"
)

# 条件编译的通信源文件
UART_COMM_SOURCES=(
    "communication/uart/bl0906_uart_impl.c"
)

SPI_COMM_SOURCES=(
    "communication/spi/bl0906_spi_impl.c"
)

HEADER_FILES=(
    "bl0906_core_api.h"
    "bl0906_log_interface.h"
    "bl0906_hal_interface.h"
    # "bl0906_calibration.h"  # 移除：包含敏感的校准算法，应通过API调用
    "bl0906_wrapper.h"
    "calibration_storage_lib/include/calibration_storage_interface.h"
    "calibration_storage_lib/include/calibration_storage_base.h"
    "calibration_storage_lib/include/i2c_eeprom_calibration_storage.h"
    "communication/common/bl0906_comm_common_api.h"
    "communication/uart/bl0906_uart_api.h"
    "communication/spi/bl0906_spi_api.h"
)

# 组件运行所需的其他文件
COMPONENT_FILES=(
    "bl0906_wrapper.cpp"
    "__init__.py"
    "sensor.py"
    # "communication_adapter_base.cpp" # 已移至CORE_SOURCES，编译到静态库中
)

# 基础编译选项（重新启用EEPROM功能）
BASE_CFLAGS="-Os -g0 -DNDEBUG -DUSE_I2C_EEPROM_CALIBRATION -ffunction-sections -fdata-sections -fstrict-aliasing"
INCLUDES="-I. -I$PROJECT_DIR -I$PROJECT_DIR/communication/common -I$PROJECT_DIR/communication/uart -I$PROJECT_DIR/communication/spi -I$PROJECT_DIR/calibration_storage_lib/include"

# 通信模式配置（默认使用SPI）
COMMUNICATION_MODE="${COMMUNICATION_MODE:-spi}"
case "$COMMUNICATION_MODE" in
    "uart")
        COMM_FLAGS="-DUSE_UART_COMMUNICATION"
        print_info "使用UART通信模式"
        ;;
    "spi")
        COMM_FLAGS="-DUSE_SPI_COMMUNICATION"
        print_info "使用SPI通信模式"
        ;;
    "both")
        COMM_FLAGS="-DUSE_UART_COMMUNICATION -DUSE_SPI_COMMUNICATION"
        print_info "同时支持UART和SPI通信模式"
        ;;
    *)
        print_error "不支持的通信模式: $COMMUNICATION_MODE"
        print_info "支持的模式: uart, spi, both"
        exit 1
        ;;
esac

# 电压采样方式配置（默认使用电压互感器方式）
VOLTAGE_SAMPLING_MODE="${VOLTAGE_SAMPLING_MODE:-transformer}"
case "$VOLTAGE_SAMPLING_MODE" in
    "transformer")
        VOLTAGE_SAMPLING_FLAGS="-DBL0906_USE_VOLTAGE_TRANSFORMER"
        ;;
    "resistor_divider")
        VOLTAGE_SAMPLING_FLAGS="-DBL0906_USE_RESISTOR_DIVIDER"
        ;;
    *)
        print_error "不支持的电压采样方式: $VOLTAGE_SAMPLING_MODE"
        echo "支持的方式: transformer, resistor_divider"
        exit 1
        ;;
esac

# 函数定义
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  BL0906 Factory 预编译库构建工具 v$VERSION${NC}"
    echo -e "${BLUE}  支持多架构编译${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

print_step() {
    echo -e "${GREEN}[步骤]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[信息]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

show_usage() {
    echo "使用方法: $0 [架构] [选项]"
    echo ""
    echo "支持的架构:"
    echo "  esp32       ESP32 (Xtensa LX6) - 默认"
    echo "  esp32s2     ESP32-S2 (Xtensa LX7)"
    echo "  esp32s3     ESP32-S3 (Xtensa LX7)"
    echo "  esp32c3     ESP32-C3 (RISC-V)"
    echo "  esp32c6     ESP32-C6 (RISC-V)"
    echo "  esp32h2     ESP32-H2 (RISC-V)"
    echo ""
    echo "选项:"
    echo "  --skip-cleanup    跳过清理步骤"
    echo "  --verbose         详细输出"
    echo "  --help            显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 使用默认配置（ESP32-C3, SPI, EEPROM）"
    echo "  $0 esp32          # 为ESP32编译"
    echo "  $0 esp32c3        # 为ESP32-C3编译"
    echo "  $0 esp32s3 --verbose  # 为ESP32-S3编译（详细输出）"
    echo "  COMMUNICATION_MODE=uart $0 esp32c3  # 使用UART通信模式"
    echo "  VOLTAGE_SAMPLING_MODE=resistor_divider $0 esp32  # 使用电阻分压采样方式"
    echo ""
    echo "环境变量:"
    echo "  IDF_TARGET               ESP-IDF目标架构（如果未指定命令行参数）"
    echo "  VOLTAGE_SAMPLING_MODE    电压采样方式："
    echo "                           - transformer (默认): 电压互感器采样"
    echo "                           - resistor_divider: 电阻分压采样"
}

detect_target_architecture() {
    local specified_arch="$1"
    
    # 优先使用命令行参数
    if [ -n "$specified_arch" ]; then
        TARGET_ARCH="$specified_arch"
    # 其次使用环境变量
    elif [ -n "$IDF_TARGET" ]; then
        TARGET_ARCH="$IDF_TARGET"
        print_info "从环境变量IDF_TARGET检测到架构: $TARGET_ARCH"
    # 最后使用默认值
    else
        TARGET_ARCH="esp32c3"
        print_info "使用默认架构: $TARGET_ARCH"
    fi
    
    print_step "配置目标架构: $TARGET_ARCH"
    
    # 根据架构配置编译器和选项
    case $TARGET_ARCH in
        "esp32")
            CC="xtensa-esp32-elf-gcc"
            CXX="xtensa-esp32-elf-g++"
            AR="xtensa-esp32-elf-ar"
            STRIP="xtensa-esp32-elf-strip"
            ARCH_FLAGS="-mlongcalls"
            ARCH_NAME="ESP32 (Xtensa LX6)"
            ;;
        "esp32s2")
            CC="xtensa-esp32s2-elf-gcc"
            CXX="xtensa-esp32s2-elf-g++"
            AR="xtensa-esp32s2-elf-ar"
            STRIP="xtensa-esp32s2-elf-strip"
            ARCH_FLAGS="-mlongcalls"
            ARCH_NAME="ESP32-S2 (Xtensa LX7)"
            ;;
        "esp32s3")
            CC="xtensa-esp32s3-elf-gcc"
            CXX="xtensa-esp32s3-elf-g++"
            AR="xtensa-esp32s3-elf-ar"
            STRIP="xtensa-esp32s3-elf-strip"
            ARCH_FLAGS="-mlongcalls"
            ARCH_NAME="ESP32-S3 (Xtensa LX7)"
            ;;
        "esp32c3")
            CC="riscv32-esp-elf-gcc"
            CXX="riscv32-esp-elf-g++"
            AR="riscv32-esp-elf-ar"
            STRIP="riscv32-esp-elf-strip"
            ARCH_FLAGS="-march=rv32imc -mabi=ilp32"
            ARCH_NAME="ESP32-C3 (RISC-V)"
            ;;
        "esp32c6")
            CC="riscv32-esp-elf-gcc"
            CXX="riscv32-esp-elf-g++"
            AR="riscv32-esp-elf-ar"
            STRIP="riscv32-esp-elf-strip"
            ARCH_FLAGS="-march=rv32imac -mabi=ilp32"
            ARCH_NAME="ESP32-C6 (RISC-V)"
            ;;
        "esp32h2")
            CC="riscv32-esp-elf-gcc"
            CXX="riscv32-esp-elf-g++"
            AR="riscv32-esp-elf-ar"
            STRIP="riscv32-esp-elf-strip"
            ARCH_FLAGS="-march=rv32imac -mabi=ilp32"
            ARCH_NAME="ESP32-H2 (RISC-V)"
            ;;
        *)
            print_error "不支持的目标架构: $TARGET_ARCH"
            echo ""
            echo "支持的架构："
            echo "  esp32, esp32s2, esp32s3, esp32c3, esp32c6, esp32h2"
            exit 1
            ;;
    esac
    
    # 设置架构特定的库文件名和编译选项
    CORE_LIB_NAME="libbl0906_core_${TARGET_ARCH}.a"
    CFLAGS="$BASE_CFLAGS $ARCH_FLAGS $VOLTAGE_SAMPLING_FLAGS $COMM_FLAGS"
    CXXFLAGS="$CFLAGS -std=c++17 -fno-rtti -fno-exceptions"
    
    print_success "架构配置完成"
    print_info "目标架构: $ARCH_NAME"
    print_info "编译器: $CXX"
    print_info "库文件名: $CORE_LIB_NAME"
    print_info "架构标志: $ARCH_FLAGS"
    print_info "电压采样方式: $VOLTAGE_SAMPLING_MODE"
    print_info "电压采样标志: $VOLTAGE_SAMPLING_FLAGS"
    echo
}

check_dependencies() {
    print_step "检查构建依赖..."
    
    # 检查编译器
    if ! command -v $CC &> /dev/null; then
        print_error "未找到 $CC 编译器"
        print_info "请安装 ESP-IDF 开发环境并确保工具链路径正确"
        print_info "对于 $TARGET_ARCH，需要安装对应的工具链"
        exit 1
    fi
    
    if ! command -v $CXX &> /dev/null; then
        print_error "未找到 $CXX 编译器"
        print_info "请安装 ESP-IDF 开发环境并确保工具链路径正确"
        exit 1
    fi
    
    if ! command -v $AR &> /dev/null; then
        print_error "未找到 $AR 归档工具"
        print_info "请安装 ESP-IDF 开发环境并确保工具链路径正确"
        exit 1
    fi
    
    # 显示编译器版本信息
    print_info "编译器版本信息:"
    $CXX --version | head -1 | sed 's/^/  /'
    
    print_success "所有依赖检查通过"
}

prepare_directories() {
    print_step "准备构建目录..."
    
    # 创建架构特定的构建目录
    BUILD_DIR="$PROJECT_DIR/build_${TARGET_ARCH}"
    
    # 清理旧的构建目录
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    # 创建目录
    mkdir -p "$BUILD_DIR"
    mkdir -p "$RELEASE_DIR"
    mkdir -p "$TEMP_DIR"
    
    print_info "构建目录: $BUILD_DIR"
    print_success "目录准备完成"
}

validate_sources() {
    print_step "验证源文件..."
    
    for source in "${CORE_SOURCES[@]}"; do
        if [ ! -f "$PROJECT_DIR/$source" ]; then
            print_error "源文件不存在: $source"
            exit 1
        fi
        print_info "✓ $source"
    done
    
    for header in "${HEADER_FILES[@]}"; do
        if [ ! -f "$PROJECT_DIR/$header" ]; then
            print_error "头文件不存在: $header"
            exit 1
        fi
        print_info "✓ $header"
    done
    
    print_success "源文件验证完成"
}

compile_core_library() {
    print_step "编译核心库..."

    cd "$BUILD_DIR"

    # 编译核心源文件
    OBJECT_FILES=()
    for source in "${CORE_SOURCES[@]}"; do
        source_path="$PROJECT_DIR/$source"
        # 只使用文件名，不包含路径
        filename=$(basename "$source")
        object_file="${filename%.*}.o"

        print_info "编译 $source..."

        # 根据文件扩展名选择编译器
        if [[ "$source" == *.cpp ]]; then
            $CXX $CXXFLAGS $INCLUDES -c "$source_path" -o "$object_file"
        elif [[ "$source" == *.c ]]; then
            $CC $CFLAGS $INCLUDES -c "$source_path" -o "$object_file"
        else
            print_error "不支持的源文件类型: $source"
            exit 1
        fi

        if [ $? -ne 0 ]; then
            print_error "编译失败: $source"
            exit 1
        fi

        OBJECT_FILES+=("$object_file")
        print_info "✓ $source -> $object_file"
    done

    # 根据通信模式添加对应的通信模块
    case "$COMMUNICATION_MODE" in
        "uart")
            print_info "编译UART通信模块..."
            for source in "${UART_COMM_SOURCES[@]}"; do
                source_path="$PROJECT_DIR/$source"
                filename=$(basename "$source")
                object_file="${filename%.*}.o"

                print_info "编译 $source..."
                $CC $CFLAGS $INCLUDES -c "$source_path" -o "$object_file"

                if [ $? -ne 0 ]; then
                    print_error "编译失败: $source"
                    exit 1
                fi

                OBJECT_FILES+=("$object_file")
                print_info "✓ $source -> $object_file"
            done
            ;;
        "spi")
            print_info "编译SPI通信模块..."
            for source in "${SPI_COMM_SOURCES[@]}"; do
                source_path="$PROJECT_DIR/$source"
                filename=$(basename "$source")
                object_file="${filename%.*}.o"

                print_info "编译 $source..."
                $CC $CFLAGS $INCLUDES -c "$source_path" -o "$object_file"

                if [ $? -ne 0 ]; then
                    print_error "编译失败: $source"
                    exit 1
                fi

                OBJECT_FILES+=("$object_file")
                print_info "✓ $source -> $object_file"
            done
            ;;
        "both")
            print_info "编译UART和SPI通信模块..."
            for source in "${UART_COMM_SOURCES[@]}" "${SPI_COMM_SOURCES[@]}"; do
                source_path="$PROJECT_DIR/$source"
                filename=$(basename "$source")
                object_file="${filename%.*}.o"

                print_info "编译 $source..."
                $CC $CFLAGS $INCLUDES -c "$source_path" -o "$object_file"

                if [ $? -ne 0 ]; then
                    print_error "编译失败: $source"
                    exit 1
                fi

                OBJECT_FILES+=("$object_file")
                print_info "✓ $source -> $object_file"
            done
            ;;
    esac
    
    # 创建静态库
    print_info "创建静态库 $CORE_LIB_NAME..."
    $AR rcs "$CORE_LIB_NAME" "${OBJECT_FILES[@]}"
    
    if [ $? -ne 0 ]; then
        print_error "创建静态库失败"
        exit 1
    fi
    
    # 优化库文件
    print_info "优化库文件..."
    $STRIP --strip-unneeded "$CORE_LIB_NAME"
    
    print_success "核心库编译完成"
}

generate_version_info() {
    print_step "生成版本信息..."
    
    local version_file="$BUILD_DIR/VERSION"
    local build_date=$(date '+%Y-%m-%d %H:%M:%S')
    local build_hash=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    cat > "$version_file" << EOF
BL0906 Factory 预编译库版本信息
========================================
版本: $VERSION
构建日期: $build_date
Git提交: $build_hash
编译器: $CXX
目标架构: $ARCH_NAME
电压采样方式: $VOLTAGE_SAMPLING_MODE

核心功能:
- BL0906/BL0910 双芯片支持
- 运行时芯片型号切换
- 芯片参数管理 (从 bl0906_chip_params.h 集成)
- 校准系数计算 (从 bl0906_calibration.h 编译时计算)
- 寄存器地址映射算法
- 数据转换算法
- 写保护解除算法
- 频率模式设置
- 双电压采样模式支持 (互感器/电阻分压)

预编译保护:
- 寄存器地址映射表
- 校准系数计算公式
- 数据转换算法
- 写保护解除时序
- 芯片差异处理逻辑

构建配置:
- 优化级别: -Os (size优化)
- 调试信息: 已移除
- 符号表: 已剥离
- 异常处理: 已禁用
- RTTI: 已禁用
EOF
    
    print_success "版本信息生成完成"
}

create_release_package() {
    print_step "创建发布包..."

    # 复制核心库文件
    print_info "复制核心库文件: $BUILD_DIR/$CORE_LIB_NAME"
    cp "$BUILD_DIR/$CORE_LIB_NAME" "$RELEASE_DIR/"

    # 复制头文件
    print_info "复制头文件..."
    for header in "${HEADER_FILES[@]}"; do
        print_info "  复制: $header"
        if [ -f "$PROJECT_DIR/$header" ]; then
            cp "$PROJECT_DIR/$header" "$RELEASE_DIR/"
        else
            print_error "头文件不存在: $PROJECT_DIR/$header"
            return 1
        fi
    done

    # 复制组件文件
    print_info "复制组件文件..."
    for component_file in "${COMPONENT_FILES[@]}"; do
        print_info "  复制: $component_file"
        if [ -f "$PROJECT_DIR/$component_file" ]; then
            cp "$PROJECT_DIR/$component_file" "$RELEASE_DIR/"
        else
            print_error "组件文件不存在: $PROJECT_DIR/$component_file"
            return 1
        fi
    done

    # 复制版本信息
    print_info "复制版本信息文件"
    cp "$BUILD_DIR/VERSION" "$RELEASE_DIR/"
    
    # 生成架构特定的发布说明
    print_info "生成README.md文件"
    {
        echo "# BL0906 Factory 预编译库 - $ARCH_NAME"
        echo ""
        echo "## 概述"
        echo ""
        echo "这是BL0906 Factory组件的预编译核心库，包含了关键的算法实现和IP保护。"
        echo "本版本专为 **$ARCH_NAME** 架构编译。"
        echo ""
        echo "## 架构信息"
        echo ""
        echo "- **目标架构**: $ARCH_NAME"
        echo "- **编译器**: $CXX"
        echo "- **架构标志**: $ARCH_FLAGS"
        echo "- **库文件名**: $CORE_LIB_NAME"
        echo ""
        echo "## 文件说明"
        echo ""
        echo "### 预编译库文件 (5个)"
        echo "- \`$CORE_LIB_NAME\` - 核心算法库（$ARCH_NAME 架构）"
        echo "- \`libcalibration_storage_$TARGET_ARCH.a\` - 校准数据存储库"
        echo "- \`libbl0906_comm_common_$TARGET_ARCH.a\` - 通用通信库"
        echo "- \`libbl0906_uart_comm_$TARGET_ARCH.a\` - UART协议库"
        echo "- \`libbl0906_spi_comm_$TARGET_ARCH.a\` - SPI协议库"
        echo ""
        echo "### C/C++接口文件 (14个)"
        echo "- \`bl0906_core_api.h\` - 核心C接口（包含校准数据结构）"
        echo "- \`bl0906_wrapper.h/.cpp\` - ESPHome包装层"
        echo "- \`bl0906_*_api.h\` - 通信接口头文件"
        echo "- \`calibration_storage_*.h\` - 校准存储接口"
        echo "- \`communication_adapter_*.h/.cpp\` - 通信适配器"
        echo ""
        echo "### Python组件文件 (2个)"
        echo "- \`__init__.py\` - ESPHome组件定义"
        echo "- \`sensor.py\` - 传感器实现"
        echo ""
        echo "### 文档文件 (2个)"
        echo "- \`VERSION\` - 版本信息文件"
        echo "- \`README.md\` - 说明文档"
        echo ""
        echo "## 构建信息"
        echo ""
        echo "- **构建日期**: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "- **目标架构**: $ARCH_NAME"
        echo "- **库文件名**: $CORE_LIB_NAME"
    } > "$RELEASE_DIR/README.md"

    
    print_success "发布包创建完成"
}

show_library_info() {
    print_step "库文件信息..."
    
    local lib_path="$RELEASE_DIR/$CORE_LIB_NAME"
    local lib_size=$(du -h "$lib_path" | cut -f1)
    local symbol_count=$(nm "$lib_path" 2>/dev/null | wc -l || echo "N/A")
    
    echo
    echo -e "${YELLOW}目标架构:${NC} $ARCH_NAME"
    echo -e "${YELLOW}库文件路径:${NC} $lib_path"
    echo -e "${YELLOW}库文件大小:${NC} $lib_size"
    echo -e "${YELLOW}符号数量:${NC} $symbol_count"
    echo -e "${YELLOW}编译器:${NC} $CXX"
    echo -e "${YELLOW}架构标志:${NC} $ARCH_FLAGS"
    echo
    
    # 显示架构信息
    if command -v file &> /dev/null; then
        print_info "库文件架构信息:"
        file "$lib_path" | sed 's/^/  /'
        echo
    fi
    
    # 显示导出的符号
    print_info "导出的公共符号:"
    if command -v nm &> /dev/null; then
        nm "$lib_path" 2>/dev/null | grep -E "^[0-9a-f]+ [TtWw] " | head -20 | while read line; do
            symbol=$(echo "$line" | awk '{print $3}')
            echo -e "  ${GREEN}•${NC} $symbol"
        done
        
        local total_symbols=$(nm "$lib_path" 2>/dev/null | grep -E "^[0-9a-f]+ [TtWw] " | wc -l)
        if [ "$total_symbols" -gt 20 ]; then
            echo -e "  ${YELLOW}... 还有 $((total_symbols - 20)) 个符号${NC}"
        fi
    else
        echo -e "  ${YELLOW}nm 工具不可用，无法显示符号信息${NC}"
    fi
    echo
}

cleanup() {
    print_step "清理临时文件..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    print_success "清理完成"
}

main() {
    print_header
    
    # 检查命令行参数
    SKIP_CLEANUP=false
    VERBOSE=false
    SPECIFIED_ARCH=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            esp32|esp32s2|esp32s3|esp32c3|esp32c6|esp32h2)
                # 架构参数
                if [ -n "$SPECIFIED_ARCH" ]; then
                    print_error "只能指定一个架构，已指定: $SPECIFIED_ARCH"
                    exit 1
                fi
                SPECIFIED_ARCH="$1"
                shift
                ;;
            -*)
                print_error "未知选项: $1"
                show_usage
                exit 1
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 检测和配置目标架构
    detect_target_architecture "$SPECIFIED_ARCH"
    
    # 执行构建步骤
    check_dependencies
    prepare_directories
    validate_sources
    compile_core_library
    generate_version_info
    create_release_package
    show_library_info
    
    if [ "$SKIP_CLEANUP" = false ]; then
        cleanup
    fi
    
    echo
    print_success "预编译库构建完成!"
    echo -e "${GREEN}目标架构:${NC} $ARCH_NAME"
    echo -e "${GREEN}发布文件位于:${NC} $RELEASE_DIR"
    echo -e "${GREEN}核心库文件:${NC} $RELEASE_DIR/$CORE_LIB_NAME"
    echo
    print_info "下一步操作:"
    echo "1. 将发布文件集成到ESPHome组件"
    echo "2. 更新薄包装层以使用预编译库"
    echo "3. 测试组件功能"
    echo "4. 部署到生产环境"
    echo
    print_info "其他架构编译:"
    echo "  $0 esp32c3     # 编译ESP32-C3版本"
    echo "  $0 esp32s3     # 编译ESP32-S3版本"
    echo
}

# 捕获退出信号进行清理
trap cleanup EXIT

# 运行主函数
main "$@" 