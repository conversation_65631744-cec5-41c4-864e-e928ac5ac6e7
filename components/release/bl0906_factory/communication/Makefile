# BL0906 通用通信库 Makefile

# 编译器配置
CC = gcc
AR = ar
CFLAGS = -Os -ffunction-sections -fdata-sections -Wall -Wextra -std=c99 -g
INCLUDES = -Icommon

# 目录配置
COMMON_DIR = common
UART_DIR = uart
SPI_DIR = spi
BUILD_DIR = build
LIBS_DIR = libs
TEST_DIR = .

# 源文件
COMMON_SOURCES = $(COMMON_DIR)/bl0906_comm_common_impl.c
UART_SOURCES = $(UART_DIR)/bl0906_uart_impl.c
SPI_SOURCES = $(SPI_DIR)/bl0906_spi_impl.c

COMMON_OBJECTS = $(BUILD_DIR)/bl0906_comm_common_impl.o
UART_OBJECTS = $(BUILD_DIR)/bl0906_uart_impl.o
SPI_OBJECTS = $(BUILD_DIR)/bl0906_spi_impl.o

COMMON_LIB = $(LIBS_DIR)/libbl0906_comm_common.a
UART_LIB = $(LIBS_DIR)/libbl0906_uart_comm.a
SPI_LIB = $(LIBS_DIR)/libbl0906_spi_comm.a

# 测试程序
TEST_COMMON_SOURCE = test_common_lib.c
TEST_PROTOCOL_SOURCE = test_protocol_libs.c
TEST_COMMON_EXECUTABLE = $(BUILD_DIR)/test_common_lib
TEST_PROTOCOL_EXECUTABLE = $(BUILD_DIR)/test_protocol_libs

# 默认目标（只构建库，不包含测试）
all: $(COMMON_LIB) $(UART_LIB) $(SPI_LIB)

# 包含测试的完整目标
all-with-tests: $(COMMON_LIB) $(UART_LIB) $(SPI_LIB) test-common test-protocol

# 创建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(LIBS_DIR):
	mkdir -p $(LIBS_DIR)

# 编译通用库
$(COMMON_OBJECTS): $(COMMON_SOURCES) | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(COMMON_LIB): $(COMMON_OBJECTS) | $(LIBS_DIR)
	$(AR) rcs $@ $^
	@echo "通用库构建完成: $@"

# 编译UART协议库
$(UART_OBJECTS): $(UART_SOURCES) $(COMMON_LIB) | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -I$(UART_DIR) -c $< -o $@

$(UART_LIB): $(UART_OBJECTS) | $(LIBS_DIR)
	$(AR) rcs $@ $^
	@echo "UART协议库构建完成: $@"

# 编译SPI协议库
$(SPI_OBJECTS): $(SPI_SOURCES) $(COMMON_LIB) | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -I$(SPI_DIR) -c $< -o $@

$(SPI_LIB): $(SPI_OBJECTS) | $(LIBS_DIR)
	$(AR) rcs $@ $^
	@echo "SPI协议库构建完成: $@"

# 编译通用库测试程序
$(TEST_COMMON_EXECUTABLE): $(TEST_COMMON_SOURCE) $(COMMON_LIB) | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) $< -L$(LIBS_DIR) -lbl0906_comm_common -o $@
	@echo "通用库测试程序构建完成: $@"

# 编译协议库测试程序
$(TEST_PROTOCOL_EXECUTABLE): $(TEST_PROTOCOL_SOURCE) $(COMMON_LIB) $(UART_LIB) $(SPI_LIB) | $(BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) $< -L$(LIBS_DIR) -lbl0906_uart_comm -lbl0906_spi_comm -lbl0906_comm_common -o $@
	@echo "协议库测试程序构建完成: $@"

# 运行通用库测试
test-common: $(TEST_COMMON_EXECUTABLE)
	@echo "运行通用库测试..."
	@$(TEST_COMMON_EXECUTABLE)

# 运行协议库测试
test-protocol: $(TEST_PROTOCOL_EXECUTABLE)
	@echo "运行协议库测试..."
	@$(TEST_PROTOCOL_EXECUTABLE)

# 运行所有测试
test: test-common test-protocol

# 清理
clean:
	rm -rf $(BUILD_DIR) $(LIBS_DIR)
	@echo "清理完成"

# 显示库信息
info: $(COMMON_LIB)
	@echo "=== 库信息 ==="
	@ls -lh $(COMMON_LIB)
	@echo "=== 符号表 ==="
	@nm $(COMMON_LIB) 2>/dev/null | grep " T " || echo "无可用符号信息"

# 重新构建
rebuild: clean all

# 安装头文件和库文件到指定目录
install: $(COMMON_LIB)
	@if [ -z "$(DESTDIR)" ]; then \
		echo "请指定安装目录: make install DESTDIR=/path/to/install"; \
		exit 1; \
	fi
	mkdir -p $(DESTDIR)/include $(DESTDIR)/lib
	cp $(COMMON_DIR)/bl0906_comm_common_api.h $(DESTDIR)/include/
	cp $(COMMON_LIB) $(DESTDIR)/lib/
	@echo "安装完成到: $(DESTDIR)"

# 创建发布包
package: $(COMMON_LIB)
	@echo "创建发布包..."
	mkdir -p bl0906_comm_common_release
	cp $(COMMON_LIB) bl0906_comm_common_release/
	cp $(COMMON_DIR)/bl0906_comm_common_api.h bl0906_comm_common_release/
	cp $(LIBS_DIR)/VERSION_COMMON bl0906_comm_common_release/ 2>/dev/null || true
	tar -czf bl0906_comm_common_release.tar.gz bl0906_comm_common_release/
	rm -rf bl0906_comm_common_release/
	@echo "发布包已创建: bl0906_comm_common_release.tar.gz"

# 帮助信息
help:
	@echo "BL0906 通用通信库构建系统"
	@echo ""
	@echo "可用目标:"
	@echo "  all      - 构建库和测试程序"
	@echo "  test     - 构建并运行测试程序"
	@echo "  clean    - 清理构建文件"
	@echo "  info     - 显示库信息"
	@echo "  rebuild  - 重新构建"
	@echo "  install  - 安装到指定目录 (需要 DESTDIR=path)"
	@echo "  package  - 创建发布包"
	@echo "  help     - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make test                    # 构建并测试"
	@echo "  make install DESTDIR=/usr    # 安装到 /usr"
	@echo "  make clean && make all       # 重新构建"

.PHONY: all test clean info rebuild install package help 