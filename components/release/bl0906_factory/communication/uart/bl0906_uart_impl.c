#include "bl0906_uart_api.h"

// ============================================================================
// UART协议实现（从 uart_communication_adapter.cpp 提取）
// ============================================================================

// UART校验和计算实现
static uint8_t calculate_checksum_impl(
    uint8_t address, 
    const bl0906_register_packet_t* packet) {
    
    if (!packet) {
        return 0;
    }
    
    // UART校验和算法：地址 + 数据字节求和后取反
    uint8_t sum = address + packet->data_l + packet->data_m + packet->data_h;
    return ~sum;  // 取反
}

// UART响应验证实现
static bl0906_comm_result_t verify_response_impl(
    uint8_t address, 
    const uint8_t* response_data, 
    size_t response_len) {
    
    if (!response_data || response_len != BL0906_UART_RESPONSE_LEN) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 提取响应数据
    uint8_t data_l = response_data[0];    // 低字节
    uint8_t data_m = response_data[1];    // 中字节  
    uint8_t data_h = response_data[2];    // 高字节
    uint8_t received_checksum = response_data[3];  // 校验和
    
    // 计算期望的校验和
    bl0906_register_packet_t packet = {data_h, data_m, data_l, 0};
    uint8_t expected_checksum = calculate_checksum_impl(address, &packet);
    
    if (received_checksum != expected_checksum) {
        return BL0906_COMM_ERROR_DATA_CONVERSION;
    }
    
    return BL0906_COMM_SUCCESS;
}

// UART读取命令准备实现
static bl0906_comm_result_t prepare_read_command_impl(
    uint8_t address, 
    uint8_t* command_buffer, 
    size_t* command_len) {
    
    if (!command_buffer || !command_len) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // UART读取命令格式：[READ_CMD] [ADDRESS]
    command_buffer[0] = BL0906_UART_READ_CMD;
    command_buffer[1] = address;
    *command_len = 2;
    
    return BL0906_COMM_SUCCESS;
}

// UART写入命令准备实现
static bl0906_comm_result_t prepare_write_command_impl(
    uint8_t address, 
    int16_t value, 
    uint8_t* command_buffer, 
    size_t* command_len) {
    
    if (!command_buffer || !command_len) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 获取通用API来准备数据
    const bl0906_comm_common_api_t* common_api = bl0906_get_common_api();
    bl0906_register_packet_t packet;
    
    bl0906_comm_result_t result = common_api->prepare_write_data(address, value, &packet);
    if (result != BL0906_COMM_SUCCESS) {
        return result;
    }
    
    // 计算校验和
    uint8_t checksum = calculate_checksum_impl(address, &packet);
    
    // UART写入命令格式：[WRITE_CMD] [ADDRESS] [DATA_L] [DATA_M] [DATA_H] [CHECKSUM]
    command_buffer[0] = BL0906_UART_WRITE_CMD;
    command_buffer[1] = address;
    command_buffer[2] = packet.data_l;
    command_buffer[3] = packet.data_m;
    command_buffer[4] = packet.data_h;
    command_buffer[5] = checksum;
    *command_len = BL0906_UART_WRITE_CMD_LEN;
    
    return BL0906_COMM_SUCCESS;
}

// UART响应数据解析实现
static bl0906_comm_result_t parse_response_data_impl(
    uint8_t address,
    const uint8_t* response_data,
    size_t response_len,
    bl0906_register_packet_t* packet) {
    
    if (!response_data || !packet || response_len != BL0906_UART_RESPONSE_LEN) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 先验证响应
    bl0906_comm_result_t result = verify_response_impl(address, response_data, response_len);
    if (result != BL0906_COMM_SUCCESS) {
        return result;
    }
    
    // 提取数据到packet结构
    packet->data_l = response_data[0];    // 低字节
    packet->data_m = response_data[1];    // 中字节  
    packet->data_h = response_data[2];    // 高字节
    packet->checksum = response_data[3];  // 校验和
    
    return BL0906_COMM_SUCCESS;
}

// 协议信息
const char* bl0906_uart_get_protocol_info(void) {
    return "BL0906 UART Protocol v1.0 - 读取命令:0x35, 写入命令:0xCA, 校验和:地址+数据取反";
}

// ============================================================================
// API实例定义
// ============================================================================

static const bl0906_uart_api_t g_uart_api = {
    .calculate_checksum = calculate_checksum_impl,
    .verify_response = verify_response_impl,
    .prepare_read_command = prepare_read_command_impl,
    .prepare_write_command = prepare_write_command_impl,
    .parse_response_data = parse_response_data_impl
};

const bl0906_uart_api_t* bl0906_get_uart_api(void) {
    return &g_uart_api;
} 