#pragma once

#include "../common/bl0906_comm_common_api.h"

#ifdef __cplusplus
extern "C" {
#endif

// UART协议常量
#define BL0906_UART_READ_CMD    0x35
#define BL0906_UART_WRITE_CMD   0xCA

// UART响应数据长度
#define BL0906_UART_RESPONSE_LEN    4   // 3字节数据 + 1字节校验和
#define BL0906_UART_WRITE_CMD_LEN   6   // 写命令总长度

// UART协议处理API结构
typedef struct {
    // 校验和计算：UART协议的校验和算法
    uint8_t (*calculate_checksum)(
        uint8_t address, 
        const bl0906_register_packet_t* packet
    );
    
    // 响应验证：验证UART响应数据的完整性
    bl0906_comm_result_t (*verify_response)(
        uint8_t address, 
        const uint8_t* response_data, 
        size_t response_len
    );
    
    // 读取命令准备：准备UART读取命令
    bl0906_comm_result_t (*prepare_read_command)(
        uint8_t address, 
        uint8_t* command_buffer, 
        size_t* command_len
    );
    
    // 写入命令准备：准备UART写入命令
    bl0906_comm_result_t (*prepare_write_command)(
        uint8_t address, 
        int16_t value, 
        uint8_t* command_buffer, 
        size_t* command_len
    );
    
    // 解析响应数据：从UART响应中提取寄存器数据
    bl0906_comm_result_t (*parse_response_data)(
        uint8_t address,
        const uint8_t* response_data,
        size_t response_len,
        bl0906_register_packet_t* packet
    );
    
} bl0906_uart_api_t;

// 获取UART API实例
const bl0906_uart_api_t* bl0906_get_uart_api(void);

// 工具函数：获取UART协议描述
const char* bl0906_uart_get_protocol_info(void);

#ifdef __cplusplus
}
#endif 