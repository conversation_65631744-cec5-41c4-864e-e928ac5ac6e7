#include "bl0906_spi_api.h"

// ============================================================================
// SPI协议实现（从 spi_communication_adapter.cpp 提取）
// ============================================================================

// SPI校验和计算实现
static uint8_t calculate_checksum_impl(
    uint8_t cmd, 
    uint8_t address, 
    const bl0906_register_packet_t* packet) {
    
    if (!packet) {
        return 0;
    }
    
    // SPI校验和算法：命令 + 地址 + 数据字节求和后异或0xFF
    uint8_t sum = cmd + address + packet->data_h + packet->data_m + packet->data_l;
    return sum ^ 0xFF;  // 异或0xFF（等价于取反）
}

// SPI响应验证实现
static bl0906_comm_result_t verify_response_impl(
    uint8_t address, 
    const uint8_t* response_data, 
    size_t response_len) {
    
    if (!response_data || response_len != BL0906_SPI_TRANSFER_LEN) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // BL0906 SPI响应格式：[DATA_H] [DATA_M] [DATA_L] [CHECKSUM]（前2字节为DATA_H、DATA_M）
    uint8_t data_h = response_data[2];
    uint8_t data_m = response_data[3];  
    uint8_t data_l = response_data[4];
    uint8_t received_checksum = response_data[5];
    
    // 计算期望的校验和：(0x82 + address + data_h + data_m + data_l) ^ 0xFF
    uint8_t expected_checksum = (BL0906_SPI_READ_CMD + address + data_h + data_m + data_l) ^ 0xFF;
    if (received_checksum != expected_checksum) {
        return BL0906_COMM_ERROR_DATA_CONVERSION;
    }
    
    return BL0906_COMM_SUCCESS;
}

// SPI读取命令准备实现
static bl0906_comm_result_t prepare_read_command_impl(
    uint8_t address, 
    uint8_t* command_buffer, 
    size_t* command_len) {
    
    if (!command_buffer || !command_len) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // SPI读取命令格式：[READ_CMD] [ADDRESS] [0x00] [0x00] [0x00] [0x00]
    // 48位连续传输，后4字节为占位符（双向传输时接收数据）
    command_buffer[0] = BL0906_SPI_READ_CMD;
    command_buffer[1] = address;
    command_buffer[2] = 0x00;
    command_buffer[3] = 0x00;
    command_buffer[4] = 0x00;
    command_buffer[5] = 0x00;
    *command_len = BL0906_SPI_TRANSFER_LEN;
    
    return BL0906_COMM_SUCCESS;
}

// SPI写入命令准备实现
static bl0906_comm_result_t prepare_write_command_impl(
    uint8_t address, 
    int16_t value, 
    uint8_t* command_buffer, 
    size_t* command_len) {
    
    if (!command_buffer || !command_len) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 获取通用API来准备数据
    const bl0906_comm_common_api_t* common_api = bl0906_get_common_api();
    bl0906_register_packet_t packet;
    
    bl0906_comm_result_t result = common_api->prepare_write_data(address, value, &packet);
    if (result != BL0906_COMM_SUCCESS) {
        return result;
    }
    
    // 计算校验和
    uint8_t checksum = calculate_checksum_impl(BL0906_SPI_WRITE_CMD, address, &packet);
    
    // SPI写入命令格式：[WRITE_CMD] [ADDRESS] [DATA_H] [DATA_M] [DATA_L] [CHECKSUM]
    command_buffer[0] = BL0906_SPI_WRITE_CMD;
    command_buffer[1] = address;
    command_buffer[2] = packet.data_h;
    command_buffer[3] = packet.data_m;
    command_buffer[4] = packet.data_l;
    command_buffer[5] = checksum;
    *command_len = BL0906_SPI_TRANSFER_LEN;
    
    return BL0906_COMM_SUCCESS;
}

// SPI响应数据解析实现
static bl0906_comm_result_t parse_response_data_impl(
    uint8_t address,
    const uint8_t* response_data,
    size_t response_len,
    bl0906_register_packet_t* packet) {
    
    if (!response_data || !packet || response_len != BL0906_SPI_TRANSFER_LEN) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 先验证响应
    bl0906_comm_result_t result = verify_response_impl(address, response_data, response_len);
    if (result != BL0906_COMM_SUCCESS) {
        return result;
    }
    
    // 提取数据到packet结构（直接取后4字节）
    packet->data_h = response_data[2];    // 高字节
    packet->data_m = response_data[3];    // 中字节  
    packet->data_l = response_data[4];    // 低字节
    packet->checksum = response_data[5];  // 校验和
    
    return BL0906_COMM_SUCCESS;
}

// 协议信息
const char* bl0906_spi_get_protocol_info(void) {
    return "BL0906 SPI Protocol v1.0 - 读取命令:0x82, 写入命令:0x81, 48位连续传输, 校验和:(CMD+ADDR+DATA)^0xFF";
}

// ============================================================================
// API实例定义
// ============================================================================

static const bl0906_spi_api_t g_spi_api = {
    .calculate_checksum = calculate_checksum_impl,
    .verify_response = verify_response_impl,
    .prepare_read_command = prepare_read_command_impl,
    .prepare_write_command = prepare_write_command_impl,
    .parse_response_data = parse_response_data_impl
};

const bl0906_spi_api_t* bl0906_get_spi_api(void) {
    return &g_spi_api;
} 