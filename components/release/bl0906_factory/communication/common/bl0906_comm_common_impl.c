#include "bl0906_comm_common_api.h"

// ============================================================================
// 寄存器类型判断实现（从 bl0906_chip_params.h 提取）
// ============================================================================

static bool is_16bit_register_impl(uint8_t address) {
    // 校准寄存器都是16位
    return (address >= 0x6C && address <= 0x80) ||  // RMSGN/RMSOS 范围
           (address >= 0xA0 && address <= 0xC9) ||  // CHGN/CHOS/WATTGN/WATTOS 范围
           (address == 0xAA) || (address == 0xB5);  // 电压校准寄存器
}

static bool is_unsigned_register_impl(uint8_t address) {
    return (address == 0x4E) || (address == 0x5E) ||  // 频率、温度
           (address == 0x16) || (address == 0x39);    // 电压、总脉冲计数
}

static bool is_24bit_register_impl(uint8_t address) {
    return address == 0x97;  // MODE2寄存器
}

// ============================================================================
// 数据类型转换实现
// ============================================================================

static int32_t convert_to_signed_24bit_impl(uint8_t data_h, uint8_t data_m, uint8_t data_l) {
    // 带符号24位，需要符号扩展
    int32_t value = ((int8_t)data_h << 16) | 
                   ((uint32_t)data_m << 8) | 
                   (uint32_t)data_l;
    return value;
}

static uint32_t convert_to_unsigned_24bit_impl(uint8_t data_h, uint8_t data_m, uint8_t data_l) {
    // 无符号24位
    uint32_t value = ((uint32_t)data_h << 16) | 
                    ((uint32_t)data_m << 8) | 
                    (uint32_t)data_l;
    return value;
}

static int16_t convert_to_signed_16bit_impl(uint8_t data_m, uint8_t data_l) {
    // 16位有符号数
    int16_t value = (data_m << 8) | data_l;
    return value;
}

// ============================================================================
// 核心数据处理实现（合并UART和SPI的相同逻辑）
// ============================================================================

static bl0906_comm_result_t parse_register_data_impl(
    uint8_t address, 
    const bl0906_register_packet_t* packet, 
    int32_t* result) {
    
    if (!packet || !result) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 根据寄存器类型返回正确的数据
    if (is_16bit_register_impl(address)) {
        // 16位寄存器：只使用低16位，处理符号扩展
        int16_t value = convert_to_signed_16bit_impl(packet->data_m, packet->data_l);
        *result = (int32_t)value;
        return BL0906_COMM_SUCCESS;
    } else {
        // 24位寄存器
        if (is_unsigned_register_impl(address)) {
            // 无符号24位
            uint32_t value = convert_to_unsigned_24bit_impl(packet->data_h, packet->data_m, packet->data_l);
            *result = (int32_t)value;
            return BL0906_COMM_SUCCESS;
        } else {
            // 带符号24位，需要符号扩展
            int32_t value = convert_to_signed_24bit_impl(packet->data_h, packet->data_m, packet->data_l);
            *result = value;
            return BL0906_COMM_SUCCESS;
        }
    }
}

static bl0906_comm_result_t prepare_write_data_impl(
    uint8_t address, 
    int16_t value, 
    bl0906_register_packet_t* packet) {
    
    if (!packet) {
        return BL0906_COMM_ERROR_INVALID_PARAM;
    }
    
    // 根据寄存器类型决定如何准备数据
    if (is_16bit_register_impl(address)) {
        // 对于16位寄存器
        packet->data_l = value & 0xFF;          // 低字节
        packet->data_m = (value >> 8) & 0xFF;   // 中字节
        packet->data_h = 0;                     // 高字节为0
        return BL0906_COMM_SUCCESS;
    } else {
        // 对于24位寄存器，使用符号扩展
        // 修复：正确处理16位到24位的符号扩展
        int32_t value_24bit = (int32_t)value;  // 先转换为32位有符号数
        if (value < 0) {
            // 负数需要扩展高8位为0xFF
            value_24bit = value_24bit | 0xFF000000;  // 设置高8位
            value_24bit = value_24bit & 0x00FFFFFF;  // 只保留低24位
        }
        packet->data_l = value_24bit & 0xFF;          // 低字节
        packet->data_m = (value_24bit >> 8) & 0xFF;   // 中字节
        packet->data_h = (value_24bit >> 16) & 0xFF;  // 高字节
        return BL0906_COMM_SUCCESS;
    }
}

static bl0906_comm_result_t verify_write_result_impl(
    int16_t expected_value, 
    int32_t actual_value) {
    
    if (actual_value == expected_value) {
        return BL0906_COMM_SUCCESS;
    } else {
        return BL0906_COMM_ERROR_DATA_CONVERSION;
    }
}

// ============================================================================
// 错误描述实现
// ============================================================================

const char* bl0906_comm_get_error_string(bl0906_comm_result_t result) {
    switch (result) {
        case BL0906_COMM_SUCCESS:
            return "操作成功";
        case BL0906_COMM_ERROR_INVALID_PARAM:
            return "无效参数";
        case BL0906_COMM_ERROR_INVALID_REGISTER:
            return "无效寄存器地址";
        case BL0906_COMM_ERROR_DATA_CONVERSION:
            return "数据转换错误";
        case BL0906_COMM_ERROR_UNKNOWN:
        default:
            return "未知错误";
    }
}

// ============================================================================
// API实例定义
// ============================================================================

static const bl0906_comm_common_api_t g_common_api = {
    .parse_register_data = parse_register_data_impl,
    .prepare_write_data = prepare_write_data_impl,
    .is_16bit_register = is_16bit_register_impl,
    .is_unsigned_register = is_unsigned_register_impl,
    .is_24bit_register = is_24bit_register_impl,
    .verify_write_result = verify_write_result_impl,
    .convert_to_signed_24bit = convert_to_signed_24bit_impl,
    .convert_to_unsigned_24bit = convert_to_unsigned_24bit_impl,
    .convert_to_signed_16bit = convert_to_signed_16bit_impl
};

const bl0906_comm_common_api_t* bl0906_get_common_api(void) {
    return &g_common_api;
} 