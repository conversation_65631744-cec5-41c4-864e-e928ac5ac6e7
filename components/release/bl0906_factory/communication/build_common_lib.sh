#!/bin/bash

# BL0906 通用通信库构建脚本
# 此脚本构建通用数据处理静态库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="bl0906_comm_common"
VERSION="1.0.0"

# 目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMMON_DIR="$SCRIPT_DIR/common"
BUILD_DIR="$SCRIPT_DIR/build"
LIBS_DIR="$SCRIPT_DIR/../libs"

# 源文件配置
COMMON_SOURCES=(
    "bl0906_comm_common_impl.c"
)

HEADER_FILES=(
    "bl0906_comm_common_api.h"
)

# 函数定义
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  BL0906 通用通信库构建工具 v$VERSION${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

print_step() {
    echo -e "${GREEN}[步骤]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[信息]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_step "检查构建依赖..."
    
    # 检查编译器
    if ! command -v gcc &> /dev/null; then
        print_error "gcc编译器未找到"
        exit 1
    fi
    
    if ! command -v ar &> /dev/null; then
        print_error "ar归档工具未找到"
        exit 1
    fi
    
    print_info "✓ gcc: $(gcc --version | head -n1)"
    print_info "✓ ar: $(ar --version | head -n1)"
    
    print_success "依赖检查完成"
}

# 准备目录
prepare_directories() {
    print_step "准备构建目录..."
    
    # 清理并创建构建目录
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    mkdir -p "$BUILD_DIR"
    
    # 创建库输出目录
    mkdir -p "$LIBS_DIR"
    
    print_success "目录准备完成"
}

# 验证源文件
validate_sources() {
    print_step "验证源文件..."
    
    for source in "${COMMON_SOURCES[@]}"; do
        if [ ! -f "$COMMON_DIR/$source" ]; then
            print_error "源文件不存在: $source"
            exit 1
        fi
        print_info "✓ $source"
    done
    
    for header in "${HEADER_FILES[@]}"; do
        if [ ! -f "$COMMON_DIR/$header" ]; then
            print_error "头文件不存在: $header"
            exit 1
        fi
        print_info "✓ $header"
    done
    
    print_success "源文件验证完成"
}

# 编译通用库
compile_common_library() {
    print_step "编译通用库..."
    
    cd "$BUILD_DIR"
    
    # 编译器配置
    CC="gcc"
    AR="ar"
    CFLAGS="-Os -ffunction-sections -fdata-sections -Wall -Wextra -std=c99"
    INCLUDES="-I$COMMON_DIR"
    
    # 编译所有源文件
    OBJECT_FILES=()
    for source in "${COMMON_SOURCES[@]}"; do
        source_path="$COMMON_DIR/$source"
        object_file="${source%.*}.o"
        
        print_info "编译 $source..."
        
        # 编译C源文件
        $CC $CFLAGS $INCLUDES -c "$source_path" -o "$object_file"
        
        if [ $? -ne 0 ]; then
            print_error "编译失败: $source"
            exit 1
        fi
        
        OBJECT_FILES+=("$object_file")
        print_info "✓ $source -> $object_file"
    done
    
    # 创建静态库
    LIB_NAME="libbl0906_comm_common.a"
    print_info "创建静态库 $LIB_NAME..."
    $AR rcs "$LIB_NAME" "${OBJECT_FILES[@]}"
    
    if [ $? -ne 0 ]; then
        print_error "创建静态库失败"
        exit 1
    fi
    
    # 优化库文件
    print_info "优化库文件..."
    strip --strip-unneeded "$LIB_NAME" 2>/dev/null || true
    
    print_success "通用库编译完成"
}

# 创建发布包
create_release_package() {
    print_step "创建发布包..."
    
    # 复制库文件
    cp "$BUILD_DIR/libbl0906_comm_common.a" "$LIBS_DIR/"
    
    # 复制头文件
    for header in "${HEADER_FILES[@]}"; do
        cp "$COMMON_DIR/$header" "$LIBS_DIR/"
    done
    
    # 生成版本信息
    local version_file="$LIBS_DIR/VERSION_COMMON"
    local build_date=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$version_file" << EOF
BL0906 通用通信库版本信息
========================================
版本: $VERSION
构建日期: $build_date
编译器: $(gcc --version | head -n1)

核心功能:
- 寄存器数据解析
- 寄存器类型判断
- 数据类型转换
- 写入数据准备
- 写入验证逻辑

文件列表:
- libbl0906_comm_common.a - 通用静态库
- bl0906_comm_common_api.h - C接口头文件
- VERSION_COMMON - 版本信息文件
EOF
    
    print_success "发布包创建完成"
}

# 显示库信息
show_library_info() {
    print_step "库信息统计..."
    
    local lib_file="$LIBS_DIR/libbl0906_comm_common.a"
    if [ -f "$lib_file" ]; then
        local lib_size=$(du -h "$lib_file" | cut -f1)
        print_info "库文件大小: $lib_size"
        
        # 显示符号表（如果有nm工具）
        if command -v nm &> /dev/null; then
            local symbol_count=$(nm "$lib_file" 2>/dev/null | grep -c "T " || echo "未知")
            print_info "导出函数数量: $symbol_count"
        fi
    fi
    
    print_success "库信息统计完成"
}

# 清理
cleanup() {
    if [ "$SKIP_CLEANUP" != "true" ]; then
        print_step "清理构建文件..."
        if [ -d "$BUILD_DIR" ]; then
            rm -rf "$BUILD_DIR"
        fi
        print_success "清理完成"
    fi
}

# 主函数
main() {
    print_header
    
    # 检查命令行参数
    SKIP_CLEANUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --help)
                echo "使用方法: $0 [选项]"
                echo "选项:"
                echo "  --skip-cleanup    跳过清理步骤"
                echo "  --help            显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行构建步骤
    check_dependencies
    prepare_directories
    validate_sources
    compile_common_library
    create_release_package
    show_library_info
    cleanup
    
    echo
    print_success "通用库构建完成!"
    echo -e "${GREEN}库文件位于:${NC} $LIBS_DIR/libbl0906_comm_common.a"
    echo -e "${GREEN}头文件位于:${NC} $LIBS_DIR/bl0906_comm_common_api.h"
    echo
    print_info "下一步操作:"
    echo "1. 构建协议特定库 (UART/SPI)"
    echo "2. 更新ESPHome适配器以使用C API"
    echo "3. 测试组件功能"
    echo
}

# 捕获退出信号进行清理
trap cleanup EXIT

# 运行主函数
main "$@" 