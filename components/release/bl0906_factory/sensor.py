"""BL0906 Factory - 发布版传感器配置"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import sensor
from esphome.const import (
    CONF_ID,
    DEVICE_CLASS_VOLTAGE,
    DEVICE_CLASS_CURRENT,
    DEVICE_CLASS_POWER,
    DEVICE_CLASS_ENERGY,
    DEVICE_CLASS_FREQUENCY,
    DEVICE_CLASS_TEMPERATURE,
    STATE_CLASS_MEASUREMENT,
    STATE_CLASS_TOTAL_INCREASING,
    UNIT_VOLT,
    UNIT_AMPERE,
    UNIT_WATT,
    UNIT_WATT_HOURS,
    UNIT_HERTZ,
    UNIT_CELSIUS,
)

from . import (
    <PERSON><PERSON>090<PERSON><PERSON>rap<PERSON>,
    CONF_BL0906_FACTORY_ID,
)

DEPENDENCIES = ["bl0906_factory"]

# 全局传感器配置
GLOBAL_SENSOR_CONFIGS = {
    "voltage": {
        "device_class": DEVICE_CLASS_VOLTAGE,
        "state_class": STATE_CLASS_MEASUREMENT,
        "unit_of_measurement": UNIT_VOLT,
        "accuracy_decimals": 2,
        "sensor_type": "voltage",
    },
    "frequency": {
        "device_class": DEVICE_CLASS_FREQUENCY,
        "state_class": STATE_CLASS_MEASUREMENT,
        "unit_of_measurement": UNIT_HERTZ,
        "accuracy_decimals": 2,
        "sensor_type": "frequency",
    },
    "temperature": {
        "device_class": DEVICE_CLASS_TEMPERATURE,
        "state_class": STATE_CLASS_MEASUREMENT,
        "unit_of_measurement": UNIT_CELSIUS,
        "accuracy_decimals": 1,
        "sensor_type": "temperature",
    },
    "power_sum": {
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
        "unit_of_measurement": UNIT_WATT,
        "accuracy_decimals": 2,
        "sensor_type": "power_sum",
    },
    "energy_sum": {
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
        "unit_of_measurement": UNIT_WATT_HOURS,
        "accuracy_decimals": 6,
        "sensor_type": "energy_sum",
    },
}

# 通道传感器配置
CHANNEL_SENSOR_CONFIGS = {
    "current": {
        "device_class": DEVICE_CLASS_CURRENT,
        "state_class": STATE_CLASS_MEASUREMENT,
        "unit_of_measurement": UNIT_AMPERE,
        "accuracy_decimals": 3,
        "sensor_type": "current",
    },
    "power": {
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
        "unit_of_measurement": UNIT_WATT,
        "accuracy_decimals": 2,
        "sensor_type": "power",
    },
    "energy": {
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
        "unit_of_measurement": UNIT_WATT_HOURS,
        "accuracy_decimals": 6,
        "sensor_type": "energy",
    },
    "total_energy": {
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
        "unit_of_measurement": UNIT_WATT_HOURS,
        "accuracy_decimals": 6,
        "sensor_type": "total_energy",
    },
}

def build_channel_sensor_schema():
    """构建单个通道的传感器配置模式"""
    schema_dict = {}
    for sensor_key, config in CHANNEL_SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(sensor_key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit_of_measurement"],
            accuracy_decimals=config["accuracy_decimals"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )
    return cv.Schema(schema_dict)

def build_config_schema():
    """构建嵌套配置模式，支持动态通道数量"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Wrapper)}

    # 添加全局传感器配置
    for key, config in GLOBAL_SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit_of_measurement"],
            accuracy_decimals=config["accuracy_decimals"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )

    # 添加通道配置，支持最大10个通道（BL0910的最大通道数）
    channel_schema = build_channel_sensor_schema()
    for i in range(1, 11):  # 支持ch1到ch10
        schema_dict[cv.Optional(f"ch{i}")] = channel_schema

    return cv.Schema(schema_dict)

CONFIG_SCHEMA = build_config_schema()

async def to_code(config):
    """生成代码"""
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])
    
    # 处理全局传感器
    for sensor_key, sensor_config in GLOBAL_SENSOR_CONFIGS.items():
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            sensor_type = sensor_config["sensor_type"]
            
            # 生成传感器枚举表达式
            enum_expr = cg.RawExpression(f"bl0906_factory::SensorType::SENSOR_{sensor_type.upper()}")
            cg.add(var.set_sensor(enum_expr, sens, 0))

    # 处理通道传感器，支持最多10个通道
    for i in range(1, 11):
        channel_key = f"ch{i}"
        if channel_key in config:
            channel_config = config[channel_key]
            channel_index = i - 1  # 内部使用从0开始的索引
            
            for sensor_key, sensor_config in CHANNEL_SENSOR_CONFIGS.items():
                if sensor_key in channel_config:
                    sens = await sensor.new_sensor(channel_config[sensor_key])
                    sensor_type = sensor_config["sensor_type"]
                    
                    # 生成传感器枚举表达式
                    enum_expr = cg.RawExpression(f"bl0906_factory::SensorType::SENSOR_{sensor_type.upper()}")
                    cg.add(var.set_sensor(enum_expr, sens, channel_index)) 