"""BL0906 Factory - 发布版电能计量组件"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, i2c, spi, sensor
from esphome import pins
from esphome.const import (
    CONF_ID,
    CONF_UPDATE_INTERVAL
)

# 发布版依赖管理
DEPENDENCIES = []
CODEOWNERS = ["@carrot8848"]
AUTO_LOAD = ["sensor"]  # 发布版不包含number组件
MULTI_CONF = True

# 发布版类声明 - 使用薄包装层
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Wrapper = bl0906_factory_ns.class_("BL0906Wrapper", cg.PollingComponent)

# 通信适配器类声明（发布版仍需要通信功能）
CommunicationAdapterInterface = bl0906_factory_ns.class_("CommunicationAdapterInterface")
UartCommunicationAdapter = bl0906_factory_ns.class_("UartCommunicationAdapter", CommunicationAdapterInterface, uart.UARTDevice)
SpiCommunicationAdapter = bl0906_factory_ns.class_("SpiCommunicationAdapter", CommunicationAdapterInterface, spi.SPIDevice)

# 发布版枚举定义（只保留必要的）
SensorType = bl0906_factory_ns.enum("SensorType", is_class=True)
ChipModel = bl0906_factory_ns.enum("ChipModel", is_class=True)
VoltageSamplingMode = bl0906_factory_ns.enum("VoltageSamplingMode", is_class=True)
FreqAdaptMode = bl0906_factory_ns.enum("FreqAdaptMode", is_class=True)
EEPROMType = bl0906_factory_ns.enum("EEPROMType", is_class=True)

# 传感器类型枚举值定义
SENSOR_TYPE_ENUMS = {
    "SENSOR_VOLTAGE": SensorType.SENSOR_VOLTAGE,
    "SENSOR_FREQUENCY": SensorType.SENSOR_FREQUENCY,
    "SENSOR_TEMPERATURE": SensorType.SENSOR_TEMPERATURE,
    "SENSOR_CURRENT": SensorType.SENSOR_CURRENT,
    "SENSOR_POWER": SensorType.SENSOR_POWER,
    "SENSOR_ENERGY": SensorType.SENSOR_ENERGY,
    "SENSOR_POWER_SUM": SensorType.SENSOR_POWER_SUM,
    "SENSOR_ENERGY_SUM": SensorType.SENSOR_ENERGY_SUM,
    "SENSOR_TOTAL_ENERGY": SensorType.SENSOR_TOTAL_ENERGY,
}

# 发布版常量定义（简化）
CONF_BL0906_FACTORY_ID = "bl0906_factory_id"
CONF_CHIP_MODEL = "chip_model"
CONF_COMMUNICATION = "communication"
CONF_INSTANCE_ID = "instance_id"
CONF_UART_ID = "uart_id"
CONF_SPI_ID = "spi_id"
CONF_CS_PIN = "cs_pin"
CONF_VOLTAGE_SAMPLING_MODE = "voltage_sampling_mode"
CONF_FREQ_ADAPT = "freq_adapt"
CONF_ENERGY_PERSISTENCE = "energy_persistence"
CONF_FAULT_TOLERANT = "fault_tolerant"
CONF_EEPROM_TYPE = "eeprom_type"
CONF_I2C_ADDRESS = "i2c_address"
CONF_I2C_ID = "i2c_id"

# 发布版配置映射（简化版）
CHIP_MODELS = {
    "bl0906": ChipModel.BL0906,
    "bl0910": ChipModel.BL0910,
}

COMMUNICATION_MODES = {
    "uart": "uart",
    "spi": "spi",
}

VOLTAGE_SAMPLING_MODES = {
    "transformer": VoltageSamplingMode.TRANSFORMER,
    "resistor_divider": VoltageSamplingMode.RESISTOR_DIVIDER,
}

FREQ_ADAPT_MODES = {
    "off": FreqAdaptMode.OFF,
    "auto": FreqAdaptMode.AUTO,
    "60hz": FreqAdaptMode.HZ60,
}

EEPROM_TYPES = {
    "24c02": EEPROMType.EEPROM_24C02,
    "24c04": EEPROMType.EEPROM_24C04,
    "24c08": EEPROMType.EEPROM_24C08,
    "24c16": EEPROMType.EEPROM_24C16,
}

# 发布版传感器类型映射（与生产版兼容）
SENSOR_TYPES = {
    "SENSOR_VOLTAGE": SensorType.SENSOR_VOLTAGE,
    "SENSOR_FREQUENCY": SensorType.SENSOR_FREQUENCY,
    "SENSOR_TEMPERATURE": SensorType.SENSOR_TEMPERATURE,
    "SENSOR_POWER_SUM": SensorType.SENSOR_POWER_SUM,
    "SENSOR_ENERGY_SUM": SensorType.SENSOR_ENERGY_SUM,
    "SENSOR_CURRENT": SensorType.SENSOR_CURRENT,
    "SENSOR_POWER": SensorType.SENSOR_POWER,
    "SENSOR_ENERGY": SensorType.SENSOR_ENERGY,
    "SENSOR_TOTAL_ENERGY": SensorType.SENSOR_TOTAL_ENERGY,
}

# 发布版配置验证
def validate_communication_config(config):
    """验证通信配置"""
    comm_mode = config[CONF_COMMUNICATION]
    
    if comm_mode == "uart":
        if CONF_UART_ID not in config:
            raise cv.Invalid("UART通信模式需要指定uart_id")
        # 添加UART依赖
        if "uart" not in DEPENDENCIES:
            DEPENDENCIES.append("uart")
    elif comm_mode == "spi":
        if CONF_SPI_ID not in config:
            raise cv.Invalid("SPI通信模式需要指定spi_id")
        if CONF_CS_PIN not in config:
            raise cv.Invalid("SPI通信模式需要指定cs_pin")
        # 添加SPI依赖
        if "spi" not in DEPENDENCIES:
            DEPENDENCIES.append("spi")
    
    return config

def validate_chip_configuration(config):
    """验证芯片配置"""
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    
    if chip_model not in CHIP_MODELS:
        raise cv.Invalid(f"不支持的芯片型号: {chip_model}")
    
    return config

def validate_eeprom_configuration(config):
    """验证EEPROM配置"""
    eeprom_type = config.get(CONF_EEPROM_TYPE, "24c08")
    
    if eeprom_type not in EEPROM_TYPES:
        raise cv.Invalid(f"不支持的EEPROM型号: {eeprom_type}")
    
    # 添加I2C依赖
    if "i2c" not in DEPENDENCIES:
        DEPENDENCIES.append("i2c")
    
    return config

# 发布版基础配置模式（简化）
BASE_CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Wrapper),
    cv.Optional(CONF_CHIP_MODEL, default="bl0906"): cv.enum(CHIP_MODELS, lower=True),
    cv.Required(CONF_COMMUNICATION): cv.enum(COMMUNICATION_MODES, lower=True),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Required(CONF_INSTANCE_ID): cv.hex_uint32_t,
    cv.Optional(CONF_VOLTAGE_SAMPLING_MODE, default="transformer"): cv.enum(VOLTAGE_SAMPLING_MODES, lower=True),
    cv.Optional(CONF_FREQ_ADAPT, default="off"): cv.enum(FREQ_ADAPT_MODES, lower=True),
    cv.Optional(CONF_ENERGY_PERSISTENCE, default=True): cv.boolean,
    cv.Optional(CONF_FAULT_TOLERANT, default=True): cv.boolean,
    # I2C EEPROM存储配置（发布版默认启用）
    cv.Optional(CONF_EEPROM_TYPE, default="24c08"): cv.enum(EEPROM_TYPES, lower=True),
    cv.Optional(CONF_I2C_ADDRESS, default=0x50): cv.hex_uint8_t,
    cv.Optional(CONF_I2C_ID): cv.use_id(i2c.I2CBus),
    # UART模式配置
    cv.Optional(CONF_UART_ID): cv.use_id(uart.UARTComponent),
    # SPI模式配置
    cv.Optional(CONF_SPI_ID): cv.use_id(spi.SPIComponent),
    cv.Optional(CONF_CS_PIN): pins.gpio_output_pin_schema,
}).extend(cv.polling_component_schema("60s"))

CONFIG_SCHEMA = cv.All(
    BASE_CONFIG_SCHEMA,
    validate_communication_config,
    validate_chip_configuration,
    validate_eeprom_configuration,
)

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)

    # 动态构建静态库链接配置
    import os
    lib_dir = os.path.dirname(__file__)

    # 基础库（总是需要）
    required_libs = [
        "bl0906_core_esp32c3",           # 核心算法库
        "bl0906_comm_common_esp32c3"     # 通用通信库
    ]

    # 根据通信方式添加对应的库
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "uart":
        required_libs.append("bl0906_uart_comm_esp32c3")
        print(f"[bl0906_factory] 添加UART通信库: libbl0906_uart_comm_esp32c3.a")
    elif comm_mode == "spi":
        required_libs.append("bl0906_spi_comm_esp32c3")
        print(f"[bl0906_factory] 添加SPI通信库: libbl0906_spi_comm_esp32c3.a")

    # 根据是否启用EEPROM存储添加校准存储库
    eeprom_type = config.get(CONF_EEPROM_TYPE)
    if eeprom_type is not None:
        required_libs.append("calibration_storage_esp32c3")
        print(f"[bl0906_factory] 添加EEPROM存储库: libcalibration_storage_esp32c3.a")

    # 构建build_flags
    build_flags = [f"-I{lib_dir}", f"-L{lib_dir}"]
    for lib in required_libs:
        build_flags.append(f"-l{lib}")

    # 使用ESPHome推荐的platformio_options方式
    cg.add_platformio_option("build_flags", build_flags)

    # 输出链接的库信息（用于调试）
    print(f"[bl0906_factory] 链接的静态库: {', '.join(required_libs)}")

    # 添加发布版标识
    cg.add_define("USE_BL0906_FACTORY")
    cg.add_define("BL0906_RELEASE_BUILD")
    
    # 设置芯片型号
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    cg.add(var.set_chip_model(CHIP_MODELS[chip_model]))
    
    # 设置实例ID
    cg.add(var.set_instance_id(config[CONF_INSTANCE_ID]))
    
    # 设置电压采样模式
    voltage_mode = config.get(CONF_VOLTAGE_SAMPLING_MODE, "transformer")
    cg.add(var.set_voltage_sampling_mode(VOLTAGE_SAMPLING_MODES[voltage_mode]))
    
    # 设置频率适配模式
    freq_adapt = config.get(CONF_FREQ_ADAPT, "off")
    cg.add(var.set_freq_adapt_mode(FREQ_ADAPT_MODES[freq_adapt]))
    
    # 设置电量持久化
    energy_persistence = config.get(CONF_ENERGY_PERSISTENCE, True)
    cg.add(var.set_energy_persistence_enabled(energy_persistence))
    
    # 设置容错模式
    fault_tolerant = config.get(CONF_FAULT_TOLERANT, True)
    cg.add(var.set_fault_tolerant_mode(fault_tolerant))
    
    # 配置I2C EEPROM存储（发布版默认启用）
    eeprom_type = config.get(CONF_EEPROM_TYPE, "24c08")
    i2c_address = config.get(CONF_I2C_ADDRESS, 0x50)
    cg.add(var.set_eeprom_storage(EEPROM_TYPES[eeprom_type], i2c_address))
    
    # 设置I2C父组件（如果指定）
    if CONF_I2C_ID in config:
        i2c_component = await cg.get_variable(config[CONF_I2C_ID])
        cg.add(var.set_i2c_parent(i2c_component))
    
    # 添加I2C EEPROM存储编译标志
    cg.add_build_flag("-DUSE_I2C_EEPROM_CALIBRATION")
    
    # 根据通信方式配置适配器（发布版：通过预编译库工厂函数创建）
    comm_mode = config[CONF_COMMUNICATION]

    if comm_mode == "uart":
        # 配置UART适配器参数（发布版：适配器在预编译库中）
        uart_component = await cg.get_variable(config[CONF_UART_ID])
        # 发布版：通过包装层的工厂方法创建适配器
        cg.add(var.create_uart_adapter(uart_component))

        # 添加UART适配器编译标志
        cg.add_build_flag("-DUSE_UART_COMMUNICATION_ADAPTER")

    elif comm_mode == "spi":
        # 配置SPI适配器参数（发布版：适配器在预编译库中）
        spi_component = await cg.get_variable(config[CONF_SPI_ID])
        cs_pin = await cg.gpio_pin_expression(config[CONF_CS_PIN])
        # 发布版：通过包装层的工厂方法创建适配器
        cg.add(var.create_spi_adapter(spi_component, cs_pin))

        # 添加SPI适配器编译标志
        cg.add_build_flag("-DUSE_SPI_COMMUNICATION_ADAPTER")
        cg.add_library("SPI", None)

# 发布版传感器配置生成器
def generate_sensor_config(sensor_type, channel=None):
    """生成传感器配置"""
    config = {
        cv.Required(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Wrapper),
    }
    
    if channel is not None:
        config[cv.Optional("channel", default=channel)] = cv.int_range(min=0, max=9)
    
    return sensor.sensor_schema().extend(config)

# 发布版传感器代码生成器
async def generate_sensor_code(config, sensor_type, channel=None):
    """生成传感器代码"""
    var = await sensor.new_sensor(config)
    parent = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])
    
    sensor_type_id = SENSOR_TYPES.get(sensor_type, 0)
    channel_id = config.get("channel", channel) if channel is not None else 0
    
    cg.add(parent.set_sensor(var, sensor_type_id, channel_id))
    
    return var
