#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// BL0906 Core API - 预编译库接口
// 这个文件定义了薄包装层与预编译库之间的接口

// 结果状态枚举
typedef enum {
    BL0906_SUCCESS = 0,
    BL0906_ERROR_NOT_INITIALIZED = 1,
    BL0906_ERROR_INVALID_PARAMETER = 2,
    BL0906_ERROR_COMMUNICATION = 3,
    BL0906_ERROR_TIMEOUT = 4,
    BL0906_ERROR_CHECKSUM = 5,
    BL0906_ERROR_WRITE_PROTECT = 6,
    BL0906_ERROR_OPERATION_FAILED = 7,
    BL0906_ERROR_UNKNOWN = 99
} bl0906_result_t;

// 芯片型号枚举
typedef enum {
    BL0906_CHIP_BL0906 = 0,
    BL0906_CHIP_BL0910 = 1
} bl0906_chip_model_t;

// 通信方式枚举
typedef enum {
    BL0906_COMM_UART = 0,
    BL0906_COMM_SPI = 1
} bl0906_comm_type_t;

// 寄存器类型枚举（从 bl0906_chip_params.h 提取）
typedef enum {
    BL0906_REG_I_RMS = 0,     // 电流有效值
    BL0906_REG_WATT = 1,      // 功率
    BL0906_REG_CF_CNT = 2,    // 脉冲计数
    BL0906_REG_RMSGN = 3,     // 有效值增益
    BL0906_REG_RMSOS = 4,     // 有效值偏置
    BL0906_REG_CHGN = 5,      // 电流增益
    BL0906_REG_CHOS = 6,      // 电流偏置
    BL0906_REG_WATTGN = 7,    // 功率增益
    BL0906_REG_WATTOS = 8,    // 功率偏置
    BL0906_REG_CHGN_V = 9,    // 电压增益
    BL0906_REG_CHOS_V = 10,   // 电压偏置
    BL0906_REG_VOLTAGE = 11,  // 电压
    BL0906_REG_FREQUENCY = 12,// 频率
    BL0906_REG_TEMPERATURE = 13,// 温度
    BL0906_REG_WATT_SUM = 14, // 总功率
    BL0906_REG_CF_SUM = 15,   // 总脉冲计数
    BL0906_REG_MODE2 = 16     // 工作模式寄存器2
} bl0906_register_type_t;

// 电压采样模式枚举（从 bl0906_calibration.h 提取）
typedef enum {
    BL0906_VOLTAGE_SAMPLING_TRANSFORMER = 0,    // 电压互感器采样
    BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER = 1 // 电阻分压采样
} bl0906_voltage_sampling_mode_t;

// 芯片信息结构
typedef struct {
    uint8_t max_channels;
    const char* chip_name;
    bool is_valid;
} bl0906_chip_info_t;

// 校准系数结构（从 bl0906_calibration.h 提取）
typedef struct {
    float Ki;      // 电流系数
    float Kv;      // 电压系数
    float Kp;      // 功率系数
    float Ke;      // 电量系数
    float Kp_sum;  // 总功率系数
    float Ke_sum;  // 总电量系数
    float FREF;    // 频率转换系数
    float TREF;    // 温度转换系数
} bl0906_calibration_coefficients_t;

// 参考参数结构（从 bl0906_calibration.h 提取）
typedef struct {
    float Vref;    // 内部参考电压 (V)
    int Gain_V;    // 电压通道增益
    int Gain_I;    // 电流通道增益
    float RL;      // 互感器副边负载电阻 (Ω)
    float Rt;      // 互感器变比
    float Rf;      // 分压电阻 (Ω)
    float R46;     // 电压采样电阻 (Ω) - 仅互感器模式
    float Rv;      // 分压下拉电阻 (kΩ) - 仅电阻分压模式
} bl0906_reference_params_t;

// 传感器通道数据结构
typedef struct {
    float current;
    float power;
    float energy;
} bl0906_channel_data_t;

// 传感器数据结构
typedef struct {
    uint32_t timestamp;
    float voltage;
    float frequency;
    float temperature;
    float power_sum;
    float energy_sum;
    bl0906_channel_data_t channels[10];  // 支持最多10个通道
    int channel_count;
    bool valid;
} bl0906_sensor_data_t;

// 校准数据结构
typedef struct {
    uint8_t register_addr;
    int16_t value;
} bl0906_calibration_entry_t;

// EEPROM型号枚举
typedef enum {
    BL0906_EEPROM_24C02 = 0,
    BL0906_EEPROM_24C04 = 1,
    BL0906_EEPROM_24C08 = 2,
    BL0906_EEPROM_24C16 = 3
} bl0906_eeprom_type_t;

// 通信回调函数类型
typedef bl0906_result_t (*bl0906_read_register_func_t)(uint8_t address, int32_t* value);
typedef bl0906_result_t (*bl0906_write_register_func_t)(uint8_t address, int16_t value);
typedef bl0906_result_t (*bl0906_send_raw_command_func_t)(const uint8_t* data, size_t length);

// 通信回调结构
typedef struct {
    bl0906_read_register_func_t read_register;
    bl0906_write_register_func_t write_register;
    bl0906_send_raw_command_func_t send_raw_command;
} bl0906_comm_callbacks_t;

// ============================================================================
// 核心API函数声明
// ============================================================================

/**
 * @brief 初始化BL0906核心库
 * @param chip_model 芯片型号
 * @param comm_type 通信方式
 * @param callbacks 通信回调函数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_init(bl0906_chip_model_t chip_model, 
                                bl0906_comm_type_t comm_type,
                                const bl0906_comm_callbacks_t* callbacks);

/**
 * @brief 读取传感器数据
 * @param data 输出的传感器数据
 * @return 操作结果
 */
bl0906_result_t bl0906_core_read_sensor_data(bl0906_sensor_data_t* data);

/**
 * @brief 转换原始寄存器值为实际值
 * @param register_addr 寄存器地址
 * @param raw_value 原始值
 * @param converted_value 转换后的值
 * @return 操作结果
 */
bl0906_result_t bl0906_core_convert_raw_to_value(uint8_t register_addr, 
                                                int32_t raw_value, 
                                                float* converted_value);

/**
 * @brief 应用校准数据到芯片
 * @param entries 校准条目数组
 * @param count 校准条目数量
 * @return 操作结果
 */
bl0906_result_t bl0906_core_apply_calibration(const bl0906_calibration_entry_t* entries, 
                                             size_t count);

/**
 * @brief 计算校准系数
 * @param register_addr 寄存器地址
 * @param raw_value 原始测量值
 * @param reference_value 参考值
 * @param calibration_coefficient 计算出的校准系数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_calculate_calibration(uint8_t register_addr,
                                                 int32_t raw_value,
                                                 float reference_value,
                                                 int16_t* calibration_coefficient);

/**
 * @brief 解除写保护
 * @return 操作结果
 */
bl0906_result_t bl0906_core_disable_write_protection(void);

/**
 * @brief 设置电网频率模式
 * @param is_60hz true为60Hz，false为50Hz
 * @return 操作结果
 */
bl0906_result_t bl0906_core_set_frequency_mode(bool is_60hz);

/**
 * @brief 检测电网频率
 * @param frequency 检测到的频率值
 * @return 操作结果
 */
bl0906_result_t bl0906_core_detect_grid_frequency(float* frequency);

// 新增：频率适配相关API
bl0906_result_t bl0906_core_auto_detect_frequency();
bl0906_result_t bl0906_core_set_frequency_mode(bool is_60hz);

// 新增：24位寄存器写入API
bl0906_result_t bl0906_core_write_register_24bit(uint8_t address, uint32_t value);

// 新增：电量持久化API
bl0906_result_t bl0906_core_set_energy_persistence(bool enabled);
bl0906_result_t bl0906_core_get_persistent_energy(int channel, float* energy);

// 新增：芯片重启检测API
bl0906_result_t bl0906_core_get_restart_count(uint32_t* count);
bl0906_result_t bl0906_core_reset_restart_detection();

// 新增：运行时芯片信息API（声明在下方）

// 新增：时间相关弱符号函数声明
uint32_t bl0906_core_get_millis() __attribute__((weak));
void bl0906_core_delay_ms(uint32_t ms) __attribute__((weak));

// ============================================================================
// 芯片参数相关API（从 bl0906_chip_params.h 迁移）
// ============================================================================

/**
 * @brief 获取芯片信息
 * @param chip_model 芯片型号
 * @param info 输出的芯片信息
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_chip_info(bl0906_chip_model_t chip_model, 
                                          bl0906_chip_info_t* info);

/**
 * @brief 获取寄存器地址
 * @param chip_model 芯片型号
 * @param reg_type 寄存器类型
 * @param channel 通道号（-1表示电压通道）
 * @param address 输出的寄存器地址
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_register_address(bl0906_chip_model_t chip_model,
                                                 bl0906_register_type_t reg_type,
                                                 int channel,
                                                 uint8_t* address);

/**
 * @brief 验证寄存器地址是否有效
 * @param chip_model 芯片型号
 * @param address 寄存器地址
 * @param is_valid 输出是否有效
 * @return 操作结果
 */
bl0906_result_t bl0906_core_validate_register_address(bl0906_chip_model_t chip_model,
                                                      uint8_t address,
                                                      bool* is_valid);

/**
 * @brief 检查寄存器是否为16位寄存器
 * @param address 寄存器地址
 * @param is_16bit 输出是否为16位
 * @return 操作结果
 */
bl0906_result_t bl0906_core_is_16bit_register(uint8_t address, bool* is_16bit);

/**
 * @brief 检查寄存器是否为无符号寄存器
 * @param address 寄存器地址
 * @param is_unsigned 输出是否为无符号
 * @return 操作结果
 */
bl0906_result_t bl0906_core_is_unsigned_register(uint8_t address, bool* is_unsigned);

// ============================================================================
// 校准系数相关API（从 bl0906_calibration.h 迁移）
// ============================================================================

/**
 * @brief 计算校准系数
 * @param sampling_mode 电压采样模式
 * @param ref_params 参考参数
 * @param coefficients 输出的校准系数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_calculate_calibration_coefficients(bl0906_voltage_sampling_mode_t sampling_mode,
                                                              const bl0906_reference_params_t* ref_params,
                                                              bl0906_calibration_coefficients_t* coefficients);

/**
 * @brief 获取默认参考参数
 * @param sampling_mode 电压采样模式
 * @param ref_params 输出的默认参考参数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_default_reference_params(bl0906_voltage_sampling_mode_t sampling_mode,
                                                         bl0906_reference_params_t* ref_params);

// ============================================================================
// 工具函数
// ============================================================================

/**
 * @brief 获取错误描述
 * @param result 错误代码
 * @return 错误描述字符串
 */
const char* bl0906_core_get_error_string(bl0906_result_t result);

/**
 * @brief 获取芯片名称
 * @return 芯片名称字符串
 */
const char* bl0906_core_get_chip_name(void);

/**
 * @brief 获取通道数量
 * @return 通道数量
 */
int bl0906_core_get_channel_count(void);

/**
 * @brief 设置频率模式
 * @param is_60hz true为60Hz模式，false为50Hz模式
 * @return 操作结果
 */
bl0906_result_t bl0906_core_set_frequency_mode(bool is_60hz);

/**
 * @brief 获取持久化电量
 * @param channel 通道号
 * @param total_energy 输出的总电量
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_persistent_energy(int channel, float* total_energy);

/**
 * @brief 获取库版本信息
 * @return 版本字符串
 */
const char* bl0906_core_get_version(void);

/**
 * @brief 反初始化核心库
 */
void bl0906_core_deinit(void);

// ============================================================================
// I2C EEPROM存储相关API
// ============================================================================

/**
 * @brief 初始化I2C EEPROM存储
 * @param eeprom_type EEPROM型号
 * @param i2c_address I2C地址
 * @return 操作结果
 */
bl0906_result_t bl0906_core_init_eeprom_storage(bl0906_eeprom_type_t eeprom_type, uint8_t i2c_address);

/**
 * @brief 从EEPROM加载校准数据
 * @param instance_id 实例ID
 * @param entries 输出的校准条目数组
 * @param max_entries 最大条目数
 * @param actual_count 实际读取的条目数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_load_calibration_from_eeprom(uint32_t instance_id,
                                                        bl0906_calibration_entry_t* entries,
                                                        size_t max_entries,
                                                        size_t* actual_count);

/**
 * @brief 保存校准数据到EEPROM
 * @param instance_id 实例ID
 * @param entries 校准条目数组
 * @param entry_count 条目数量
 * @return 操作结果
 */
bl0906_result_t bl0906_core_save_calibration_to_eeprom(uint32_t instance_id,
                                                      const bl0906_calibration_entry_t* entries,
                                                      size_t entry_count);

/**
 * @brief 获取EEPROM中的实例列表
 * @param instance_ids 输出的实例ID数组
 * @param max_instances 最大实例数
 * @param actual_count 实际实例数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_eeprom_instance_list(uint32_t* instance_ids,
                                                    size_t max_instances,
                                                    size_t* actual_count);

/**
 * @brief 清除EEPROM中的所有数据
 * @return 操作结果
 */
bl0906_result_t bl0906_core_clear_eeprom_storage(void);

/**
 * @brief 获取EEPROM存储状态
 * @param max_instances 最大实例数
 * @param used_instances 已使用实例数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_eeprom_storage_status(size_t* max_instances, size_t* used_instances);

// ============================================================================
// 通信适配器工厂函数（发布版专用）
// 注意：这些函数只在C++环境中可用，因为涉及C++类型
// ============================================================================

#ifdef __cplusplus
// 前向声明
namespace bl0906_core {
    class CommunicationAdapterInterface;
}

/**
 * @brief 创建UART通信适配器
 * @param uart_component UART组件指针
 * @return 通信适配器接口指针，失败返回nullptr
 */
void* bl0906_core_create_uart_adapter(void* uart_component);

/**
 * @brief 创建SPI通信适配器
 * @param spi_component SPI组件指针
 * @param cs_pin CS引脚指针
 * @return 通信适配器接口指针，失败返回nullptr
 */
void* bl0906_core_create_spi_adapter(void* spi_component, void* cs_pin);

/**
 * @brief 销毁通信适配器
 * @param adapter 要销毁的适配器指针
 */
void bl0906_core_destroy_adapter(void* adapter);
#endif

#ifdef __cplusplus
}
#endif