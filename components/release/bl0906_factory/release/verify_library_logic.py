#!/usr/bin/env python3
"""
简化的库选择逻辑验证脚本
直接测试库选择逻辑，不依赖ESPHome模块
"""

def simulate_library_selection(config):
    """模拟库选择逻辑"""
    # 基础库（总是需要）
    required_libs = [
        "bl0906_core_esp32c3",           # 核心算法库
        "bl0906_comm_common_esp32c3"     # 通用通信库
    ]
    
    # 根据通信方式添加对应的库
    comm_mode = config.get("communication")
    if comm_mode == "uart":
        required_libs.append("bl0906_uart_comm_esp32c3")
    elif comm_mode == "spi":
        required_libs.append("bl0906_spi_comm_esp32c3")
    
    # 根据是否启用EEPROM存储添加校准存储库
    eeprom_type = config.get("eeprom_type")
    if eeprom_type is not None:
        required_libs.append("calibration_storage_esp32c3")
    
    return required_libs

def test_library_selection():
    """测试库选择逻辑"""
    print("🧪 BL0906 Factory 库选择逻辑验证")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "UART + EEPROM",
            "config": {
                "communication": "uart",
                "uart_id": "uart_bus",
                "eeprom_type": "24c08"
            },
            "expected": [
                "bl0906_core_esp32c3",
                "bl0906_comm_common_esp32c3",
                "bl0906_uart_comm_esp32c3",
                "calibration_storage_esp32c3"
            ]
        },
        {
            "name": "SPI + EEPROM", 
            "config": {
                "communication": "spi",
                "spi_id": "spi_bus",
                "cs_pin": "GPIO5",
                "eeprom_type": "24c16"
            },
            "expected": [
                "bl0906_core_esp32c3",
                "bl0906_comm_common_esp32c3", 
                "bl0906_spi_comm_esp32c3",
                "calibration_storage_esp32c3"
            ]
        },
        {
            "name": "UART + 无EEPROM",
            "config": {
                "communication": "uart",
                "uart_id": "uart_bus"
            },
            "expected": [
                "bl0906_core_esp32c3",
                "bl0906_comm_common_esp32c3",
                "bl0906_uart_comm_esp32c3"
            ]
        },
        {
            "name": "SPI + 无EEPROM",
            "config": {
                "communication": "spi", 
                "spi_id": "spi_bus",
                "cs_pin": "GPIO5"
            },
            "expected": [
                "bl0906_core_esp32c3",
                "bl0906_comm_common_esp32c3",
                "bl0906_spi_comm_esp32c3"
            ]
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"   配置: {test_case['config']}")
        
        actual = simulate_library_selection(test_case['config'])
        expected = test_case['expected']
        
        print(f"   期望: {expected}")
        print(f"   实际: {actual}")
        
        if set(actual) == set(expected):
            print("   ✅ 通过")
            passed += 1
        else:
            print("   ❌ 失败")
            missing = set(expected) - set(actual)
            extra = set(actual) - set(expected)
            if missing:
                print(f"      缺少: {missing}")
            if extra:
                print(f"      多余: {extra}")
    
    print("\n" + "=" * 50)
    print(f"📊 结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！库选择逻辑正确。")
        return True
    else:
        print("💥 部分测试失败。")
        return False

def analyze_library_combinations():
    """分析不同配置组合的库使用情况"""
    print("\n📈 库使用情况分析")
    print("=" * 50)
    
    configs = [
        ("UART + EEPROM", {"communication": "uart", "eeprom_type": "24c08"}),
        ("SPI + EEPROM", {"communication": "spi", "eeprom_type": "24c08"}),
        ("UART + 无EEPROM", {"communication": "uart"}),
        ("SPI + 无EEPROM", {"communication": "spi"})
    ]
    
    lib_usage = {}
    
    for name, config in configs:
        libs = simulate_library_selection(config)
        print(f"\n{name}:")
        for lib in libs:
            print(f"  📦 {lib}")
            lib_usage[lib] = lib_usage.get(lib, 0) + 1
    
    print(f"\n📊 库使用频率:")
    for lib, count in sorted(lib_usage.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(configs)) * 100
        print(f"  {lib}: {count}/{len(configs)} ({percentage:.0f}%)")
    
    # 计算固件大小节省
    print(f"\n💾 固件大小优化分析:")
    all_libs = set(lib_usage.keys())
    for name, config in configs:
        used_libs = set(simulate_library_selection(config))
        unused_libs = all_libs - used_libs
        if unused_libs:
            print(f"  {name}: 节省 {len(unused_libs)} 个库 ({', '.join(unused_libs)})")
        else:
            print(f"  {name}: 使用所有库")

def main():
    success = test_library_selection()
    analyze_library_combinations()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
