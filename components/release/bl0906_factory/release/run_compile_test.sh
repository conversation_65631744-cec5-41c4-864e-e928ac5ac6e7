#!/bin/bash

# BL0906 Factory 发布版编译测试脚本

echo "🔧 BL0906 Factory 发布版编译测试"
echo "=================================="

# 设置编译器和标志
CXX="g++"
CXXFLAGS="-std=c++11 -Wall -Wextra -I."

# 测试文件
TEST_FILE="test_compile.cpp"
OUTPUT_FILE="test_compile.o"

# 清理之前的输出
if [ -f "$OUTPUT_FILE" ]; then
    rm "$OUTPUT_FILE"
    echo "🧹 清理之前的编译输出"
fi

echo "📋 编译测试文件: $TEST_FILE"
echo "🔧 编译器: $CXX"
echo "🚩 编译标志: $CXXFLAGS"
echo ""

# 执行编译测试
echo "▶️  开始编译测试..."
$CXX $CXXFLAGS -c "$TEST_FILE" -o "$OUTPUT_FILE" 2>&1

# 检查编译结果
if [ $? -eq 0 ] && [ -f "$OUTPUT_FILE" ]; then
    echo "✅ 编译测试通过！"
    echo "📦 生成的目标文件: $OUTPUT_FILE"
    
    # 显示目标文件信息
    if command -v file >/dev/null 2>&1; then
        echo "📄 文件信息:"
        file "$OUTPUT_FILE"
    fi
    
    if command -v size >/dev/null 2>&1; then
        echo "📏 文件大小:"
        size "$OUTPUT_FILE"
    fi
    
    echo ""
    echo "🎉 所有头文件和API声明正确！"
    echo "✅ 发布版可以正常编译"
    
    # 清理测试文件
    rm "$OUTPUT_FILE"
    echo "🧹 清理测试输出文件"
    
    exit 0
else
    echo "❌ 编译测试失败！"
    echo ""
    echo "💡 可能的问题："
    echo "   - 头文件包含错误"
    echo "   - API函数声明不正确"
    echo "   - C/C++语法错误"
    echo "   - 命名空间问题"
    echo ""
    echo "🔍 请检查编译错误信息并修复相应问题"
    
    exit 1
fi
