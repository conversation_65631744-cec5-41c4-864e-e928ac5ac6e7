/*
 * BL0906 Factory 发布版编译测试
 * 验证头文件和API声明的正确性
 */

// 测试C++编译环境
#include <memory>
#include <string>

// 模拟ESPHome环境
namespace esphome {
namespace uart {
    class UARTComponent {};
}
namespace spi {
    class SPIComponent {};
}
class GPIOPin {};
namespace sensor {
    class Sensor {};
}
namespace i2c {
    class I2CBus {};
}
}

// 测试API头文件包含
#include "bl0906_core_api.h"

// 测试通信适配器接口
#include "communication_adapter_interface.h"

// 测试包装层头文件
#include "bl0906_wrapper.h"

int main() {
    // 测试API函数声明是否正确
    bl0906_result_t result;
    
    // 测试基础API
    result = bl0906_core_init(BL0906_CHIP_BL0906, BL0906_COMM_UART, nullptr);
    
    // 测试传感器数据读取
    bl0906_sensor_data_t data;
    result = bl0906_core_read_sensor_data(&data);
    
    // 测试EEPROM存储API
    result = bl0906_core_init_eeprom_storage(BL0906_EEPROM_24C08, 0x50);
    
    // 测试频率适配API
    result = bl0906_core_auto_detect_frequency();
    result = bl0906_core_set_frequency_mode(true);
    
    // 测试工厂函数（C++环境）
    void* uart_adapter = bl0906_core_create_uart_adapter(nullptr);
    void* spi_adapter = bl0906_core_create_spi_adapter(nullptr, nullptr);
    
    if (uart_adapter) {
        bl0906_core_destroy_adapter(uart_adapter);
    }
    if (spi_adapter) {
        bl0906_core_destroy_adapter(spi_adapter);
    }
    
    // 测试包装层类
    esphome::bl0906_factory::BL0906Wrapper wrapper;
    
    // 测试枚举
    esphome::bl0906_factory::ChipModel chip = esphome::bl0906_factory::ChipModel::BL0906;
    esphome::bl0906_factory::SensorType sensor_type = esphome::bl0906_factory::SensorType::SENSOR_VOLTAGE;
    
    // 测试配置方法
    wrapper.set_chip_model(chip);
    wrapper.set_instance_id(0x12345678);
    wrapper.set_energy_persistence_enabled(true);
    wrapper.set_fault_tolerant_mode(true);
    
    return 0;
}

/*
编译测试命令:
g++ -std=c++11 -I. -c test_compile.cpp -o test_compile.o

如果编译成功，说明头文件和API声明正确。
如果编译失败，需要修复相应的声明问题。
*/
