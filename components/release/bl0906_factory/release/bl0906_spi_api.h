#pragma once

#include "../common/bl0906_comm_common_api.h"

#ifdef __cplusplus
extern "C" {
#endif

// SPI协议常量
#define BL0906_SPI_READ_CMD     0x82
#define BL0906_SPI_WRITE_CMD    0x81

// SPI数据传输长度
#define BL0906_SPI_TRANSFER_LEN     6   // 48位连续传输
#define BL0906_SPI_DATA_OFFSET      2   // 有效数据偏移（跳过命令和地址回显）

// SPI协议处理API结构
typedef struct {
    // 校验和计算：SPI协议的校验和算法
    uint8_t (*calculate_checksum)(
        uint8_t cmd, 
        uint8_t address, 
        const bl0906_register_packet_t* packet
    );
    
    // 响应验证：验证SPI响应数据的完整性
    bl0906_comm_result_t (*verify_response)(
        uint8_t address, 
        const uint8_t* response_data, 
        size_t response_len
    );
    
    // 读取命令准备：准备SPI读取命令（48位传输）
    bl0906_comm_result_t (*prepare_read_command)(
        uint8_t address, 
        uint8_t* command_buffer, 
        size_t* command_len
    );
    
    // 写入命令准备：准备SPI写入命令（48位传输）
    bl0906_comm_result_t (*prepare_write_command)(
        uint8_t address, 
        int16_t value, 
        uint8_t* command_buffer, 
        size_t* command_len
    );
    
    // 解析响应数据：从SPI响应中提取寄存器数据
    bl0906_comm_result_t (*parse_response_data)(
        uint8_t address,
        const uint8_t* response_data,
        size_t response_len,
        bl0906_register_packet_t* packet
    );
    
} bl0906_spi_api_t;

// 获取SPI API实例
const bl0906_spi_api_t* bl0906_get_spi_api(void);

// 工具函数：获取SPI协议描述
const char* bl0906_spi_get_protocol_info(void);

#ifdef __cplusplus
}
#endif 