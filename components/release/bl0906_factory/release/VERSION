BL0906 Factory 预编译库版本信息
========================================
版本: 2.0.0
构建日期: 2025-07-25 14:45:03
Git提交: unknown
编译器: riscv32-esp-elf-g++
目标架构: ESP32-C3 (RISC-V)
电压采样方式: transformer

核心功能:
- BL0906/BL0910 双芯片支持
- 运行时芯片型号切换
- 芯片参数管理 (从 bl0906_chip_params.h 集成)
- 校准系数计算 (从 bl0906_calibration.h 编译时计算)
- 寄存器地址映射算法
- 数据转换算法
- 写保护解除算法
- 频率模式设置
- 双电压采样模式支持 (互感器/电阻分压)

预编译保护:
- 寄存器地址映射表
- 校准系数计算公式
- 数据转换算法
- 写保护解除时序
- 芯片差异处理逻辑

构建配置:
- 优化级别: -Os (size优化)
- 调试信息: 已移除
- 符号表: 已剥离
- 异常处理: 已禁用
- RTTI: 已禁用
