#!/usr/bin/env python3
"""
BL0906 Factory 发布版完整性验证脚本
检查发布版文件的完整性和依赖关系的正确性
"""

import os
import sys
import re
from pathlib import Path

class ReleaseValidator:
    def __init__(self, release_dir):
        self.release_dir = Path(release_dir)
        self.errors = []
        self.warnings = []
        
    def log_error(self, message):
        self.errors.append(f"❌ ERROR: {message}")
        
    def log_warning(self, message):
        self.warnings.append(f"⚠️  WARNING: {message}")
        
    def log_info(self, message):
        print(f"ℹ️  INFO: {message}")
        
    def validate_required_files(self):
        """验证必需文件是否存在"""
        self.log_info("检查必需文件...")
        
        required_files = [
            # 预编译库文件
            "libbl0906_core_esp32c3.a",
            "libcalibration_storage_esp32c3.a", 
            "libbl0906_comm_common_esp32c3.a",
            "libbl0906_uart_comm_esp32c3.a",
            "libbl0906_spi_comm_esp32c3.a",
            
            # C/C++接口文件
            "bl0906_core_api.h",
            "bl0906_wrapper.h",
            "bl0906_wrapper.cpp",
            "communication_adapter_interface.h",
            
            # Python组件文件
            "__init__.py",
            "sensor.py",
            
            # 文档文件
            "README.md",
            "VERSION"
        ]
        
        for file_name in required_files:
            file_path = self.release_dir / file_name
            if not file_path.exists():
                self.log_error(f"缺少必需文件: {file_name}")
            else:
                self.log_info(f"✓ 找到文件: {file_name}")
                
    def validate_library_dependencies(self):
        """验证库依赖关系"""
        self.log_info("检查库依赖关系...")
        
        init_py = self.release_dir / "__init__.py"
        if not init_py.exists():
            self.log_error("__init__.py文件不存在")
            return
            
        with open(init_py, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查动态库选择逻辑
        if 'required_libs = [' not in content:
            self.log_error("缺少动态库选择逻辑")
        else:
            self.log_info("✓ 找到动态库选择逻辑")
            
        # 检查基础库
        if '"bl0906_core_esp32c3"' not in content:
            self.log_error("缺少核心库链接")
        if '"bl0906_comm_common_esp32c3"' not in content:
            self.log_error("缺少通用通信库链接")
            
        # 检查条件库选择
        if 'comm_mode == "uart"' not in content:
            self.log_error("缺少UART库选择逻辑")
        if 'comm_mode == "spi"' not in content:
            self.log_error("缺少SPI库选择逻辑")
        if 'eeprom_type is not None' not in content:
            self.log_error("缺少EEPROM库选择逻辑")
            
    def validate_api_completeness(self):
        """验证API接口完整性"""
        self.log_info("检查API接口完整性...")
        
        api_header = self.release_dir / "bl0906_core_api.h"
        if not api_header.exists():
            self.log_error("API头文件不存在")
            return
            
        with open(api_header, 'r', encoding='utf-8') as f:
            api_content = f.read()
            
        # 检查关键API函数
        required_apis = [
            "bl0906_core_init",
            "bl0906_core_read_sensor_data",
            "bl0906_core_init_eeprom_storage",
            "bl0906_core_load_calibration_from_eeprom",
            "bl0906_core_auto_detect_frequency",
            "bl0906_core_set_frequency_mode",
            "bl0906_core_create_uart_adapter",
            "bl0906_core_create_spi_adapter"
        ]
        
        for api in required_apis:
            if api not in api_content:
                self.log_error(f"缺少API函数声明: {api}")
            else:
                self.log_info(f"✓ 找到API函数: {api}")
                
    def validate_wrapper_implementation(self):
        """验证包装层实现"""
        self.log_info("检查包装层实现...")
        
        wrapper_cpp = self.release_dir / "bl0906_wrapper.cpp"
        if not wrapper_cpp.exists():
            self.log_error("包装层实现文件不存在")
            return
            
        with open(wrapper_cpp, 'r', encoding='utf-8') as f:
            wrapper_content = f.read()
            
        # 检查工厂方法实现
        if 'create_uart_adapter' not in wrapper_content:
            self.log_error("缺少UART适配器工厂方法实现")
        if 'create_spi_adapter' not in wrapper_content:
            self.log_error("缺少SPI适配器工厂方法实现")
            
        # 检查API调用
        if 'bl0906_core_create_uart_adapter' not in wrapper_content:
            self.log_error("包装层未调用UART适配器工厂API")
        if 'bl0906_core_create_spi_adapter' not in wrapper_content:
            self.log_error("包装层未调用SPI适配器工厂API")
            
    def validate_sensor_configuration(self):
        """验证传感器配置"""
        self.log_info("检查传感器配置...")
        
        sensor_py = self.release_dir / "sensor.py"
        if not sensor_py.exists():
            self.log_error("传感器配置文件不存在")
            return
            
        with open(sensor_py, 'r', encoding='utf-8') as f:
            sensor_content = f.read()
            
        # 检查传感器类型引用
        if 'bl0906_factory::SensorType::' not in sensor_content:
            self.log_error("传感器类型引用不正确")
        else:
            self.log_info("✓ 传感器类型引用正确")
            
    def validate_version_info(self):
        """验证版本信息"""
        self.log_info("检查版本信息...")
        
        version_file = self.release_dir / "VERSION"
        if not version_file.exists():
            self.log_error("版本信息文件不存在")
            return
            
        with open(version_file, 'r', encoding='utf-8') as f:
            version_content = f.read()
            
        if "ESP32-C3 (RISC-V)" not in version_content:
            self.log_warning("版本文件中未明确标注目标架构")
        if "2.0.0" not in version_content:
            self.log_warning("版本号可能需要更新")
            
    def run_validation(self):
        """运行完整验证"""
        print("🔍 开始BL0906 Factory发布版完整性验证...")
        print(f"📁 验证目录: {self.release_dir}")
        print("=" * 60)
        
        self.validate_required_files()
        self.validate_library_dependencies()
        self.validate_api_completeness()
        self.validate_wrapper_implementation()
        self.validate_sensor_configuration()
        self.validate_version_info()
        
        print("=" * 60)
        print("📊 验证结果:")
        
        if self.errors:
            print(f"❌ 发现 {len(self.errors)} 个错误:")
            for error in self.errors:
                print(f"  {error}")
                
        if self.warnings:
            print(f"⚠️  发现 {len(self.warnings)} 个警告:")
            for warning in self.warnings:
                print(f"  {warning}")
                
        if not self.errors and not self.warnings:
            print("✅ 验证通过！发布版完整性良好。")
            return True
        elif not self.errors:
            print("✅ 验证基本通过，但有一些警告需要注意。")
            return True
        else:
            print("❌ 验证失败，请修复上述错误后重新验证。")
            return False

def main():
    if len(sys.argv) != 2:
        print("用法: python validate_release.py <release_directory>")
        sys.exit(1)
        
    release_dir = sys.argv[1]
    if not os.path.exists(release_dir):
        print(f"错误: 目录 {release_dir} 不存在")
        sys.exit(1)
        
    validator = ReleaseValidator(release_dir)
    success = validator.run_validation()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
