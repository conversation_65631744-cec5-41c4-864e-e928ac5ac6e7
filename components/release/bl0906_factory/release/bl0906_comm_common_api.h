#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 结果状态枚举
typedef enum {
    BL0906_COMM_SUCCESS = 0,
    BL0906_COMM_ERROR_INVALID_PARAM = 1,
    BL0906_COMM_ERROR_INVALID_REGISTER = 2,
    BL0906_COMM_ERROR_DATA_CONVERSION = 3,
    BL0906_COMM_ERROR_UNKNOWN = 99
} bl0906_comm_result_t;

// 寄存器数据包结构
typedef struct {
    uint8_t data_h;    // 高字节
    uint8_t data_m;    // 中字节  
    uint8_t data_l;    // 低字节
    uint8_t checksum;  // 校验和（可选，用于某些协议）
} bl0906_register_packet_t;

// 核心数据处理API结构
typedef struct {
    // 数据解析：将3字节原始数据转换为int32_t值
    bl0906_comm_result_t (*parse_register_data)(
        uint8_t address, 
        const bl0906_register_packet_t* packet, 
        int32_t* result
    );
    
    // 数据准备：将int16_t值转换为3字节写入数据
    bl0906_comm_result_t (*prepare_write_data)(
        uint8_t address, 
        int16_t value, 
        bl0906_register_packet_t* packet
    );
    
    // 寄存器类型判断函数
    bool (*is_16bit_register)(uint8_t address);
    bool (*is_unsigned_register)(uint8_t address);
    bool (*is_24bit_register)(uint8_t address);
    
    // 写入验证
    bl0906_comm_result_t (*verify_write_result)(
        int16_t expected_value, 
        int32_t actual_value
    );
    
    // 数据类型转换辅助函数
    int32_t (*convert_to_signed_24bit)(uint8_t data_h, uint8_t data_m, uint8_t data_l);
    uint32_t (*convert_to_unsigned_24bit)(uint8_t data_h, uint8_t data_m, uint8_t data_l);
    int16_t (*convert_to_signed_16bit)(uint8_t data_m, uint8_t data_l);
    
} bl0906_comm_common_api_t;

// 获取通用API实例
const bl0906_comm_common_api_t* bl0906_get_common_api(void);

// 工具函数：获取错误描述
const char* bl0906_comm_get_error_string(bl0906_comm_result_t result);

#ifdef __cplusplus
}
#endif 