#pragma once
#include "calibration_storage_base.h"
#include <cstddef>
#include <cstdint>

namespace esphome {
namespace bl0906_factory {

// EEPROM类型枚举（兼容性）
enum class EEPROMType {
    TYPE_24C02 = 0,
    TYPE_24C04 = 1,
    TYPE_24C08 = 2,
    TYPE_24C16 = 3
};


class I2CInterface {
public:
    virtual bool read(uint8_t addr, uint8_t* data, size_t len) = 0;
    virtual ~I2CInterface() {}
};

class I2CEepromCalibrationStorage : public CalibrationStorageBase {
public:
    I2CEepromCalibrationStorage(I2CInterface* i2c, uint8_t dev_addr, size_t data_len);

    // 实现CalibrationStorageInterface的纯虚函数
    bool init() override;
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;
    bool verify() override;
    bool erase() override;
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override;
    std::string get_storage_type() const override;

    ~I2CEepromCalibrationStorage() override;

protected:
    // 实现CalibrationStorageBase的纯虚函数
    bool read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) override;

private:
    I2CInterface* i2c_;
    uint8_t dev_addr_;
    size_t data_len_;
};

}  // namespace bl0906_factory
}  // namespace esphome