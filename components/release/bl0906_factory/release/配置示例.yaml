# BL0906 Factory 发布版配置示例
# 展示不同通信方式和存储配置的正确用法

# ============================================================================
# 示例1: UART通信 + EEPROM存储 (完整配置)
# 链接库: core + comm_common + uart_comm + calibration_storage
# ============================================================================

# UART配置
uart:
  id: uart_bus
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 4800
  parity: NONE
  stop_bits: 1

# I2C配置 (用于EEPROM)
i2c:
  id: i2c_bus
  sda: GPIO21
  scl: GPIO22
  scan: true

# BL0906 Factory组件配置
bl0906_factory:
  id: bl0906_1
  chip_model: bl0906          # 或 bl0910
  communication: uart         # UART通信方式
  uart_id: uart_bus
  instance_id: 0x12345678     # 唯一实例ID
  
  # EEPROM存储配置
  eeprom_type: 24c08          # 24c02, 24c04, 24c08, 24c16
  i2c_address: 0x50
  i2c_id: i2c_bus
  
  # 其他配置
  voltage_sampling_mode: transformer  # 或 resistor_divider
  freq_adapt: auto            # off, auto, 60hz
  energy_persistence: true
  fault_tolerant: true
  update_interval: 60s

# 传感器配置
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_1
    
    # 全局传感器
    voltage:
      name: "电压"
      id: voltage_sensor
    frequency:
      name: "频率"
      id: frequency_sensor
    temperature:
      name: "温度"
      id: temperature_sensor
    power_sum:
      name: "总功率"
      id: power_sum_sensor
    energy_sum:
      name: "总电量"
      id: energy_sum_sensor
    
    # 通道传感器 (BL0906: 1-6, BL0910: 1-10)
    ch1:
      current:
        name: "通道1电流"
        id: ch1_current
      power:
        name: "通道1功率"
        id: ch1_power
      energy:
        name: "通道1电量"
        id: ch1_energy
    
    ch2:
      current:
        name: "通道2电流"
      power:
        name: "通道2功率"
      energy:
        name: "通道2电量"

---

# ============================================================================
# 示例2: SPI通信 + 无EEPROM存储 (精简配置)
# 链接库: core + comm_common + spi_comm (不包含calibration_storage)
# ============================================================================

# SPI配置
spi:
  id: spi_bus
  clk_pin: GPIO18
  mosi_pin: GPIO23
  miso_pin: GPIO19

# BL0906 Factory组件配置
bl0906_factory:
  id: bl0906_2
  chip_model: bl0910          # 10通道芯片
  communication: spi          # SPI通信方式
  spi_id: spi_bus
  cs_pin: GPIO5               # SPI片选引脚
  instance_id: 0x87654321
  
  # 不配置EEPROM相关参数，将不链接calibration_storage库
  # eeprom_type: 24c08        # 注释掉，不使用EEPROM
  # i2c_address: 0x50
  # i2c_id: i2c_bus
  
  voltage_sampling_mode: resistor_divider
  freq_adapt: 60hz            # 强制60Hz模式
  energy_persistence: false   # 不使用电量持久化
  fault_tolerant: true
  update_interval: 30s

# 传感器配置 (仅配置需要的传感器)
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_2
    
    voltage:
      name: "SPI电压"
    power_sum:
      name: "SPI总功率"
    
    # BL0910支持10个通道
    ch1:
      current:
        name: "SPI通道1电流"
    ch2:
      current:
        name: "SPI通道2电流"
    # ... 可配置到ch10

---

# ============================================================================
# 示例3: UART通信 + 自定义EEPROM配置
# 链接库: core + comm_common + uart_comm + calibration_storage
# ============================================================================

uart:
  id: uart_bus_custom
  tx_pin: GPIO17
  rx_pin: GPIO16
  baud_rate: 4800

i2c:
  id: i2c_bus_custom
  sda: GPIO4
  scl: GPIO15
  frequency: 100kHz

bl0906_factory:
  id: bl0906_custom
  chip_model: bl0906
  communication: uart
  uart_id: uart_bus_custom
  instance_id: 0xABCDEF00
  
  # 自定义EEPROM配置
  eeprom_type: 24c16          # 更大容量的EEPROM
  i2c_address: 0x51           # 非标准I2C地址
  i2c_id: i2c_bus_custom
  
  voltage_sampling_mode: transformer
  freq_adapt: off             # 关闭频率适配
  energy_persistence: true
  fault_tolerant: false       # 严格模式
  update_interval: 10s        # 更频繁的更新

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_custom
    
    voltage:
      name: "自定义电压"
      accuracy_decimals: 3
    frequency:
      name: "自定义频率"
      accuracy_decimals: 3
    
    ch1:
      current:
        name: "自定义通道1电流"
        accuracy_decimals: 4
      power:
        name: "自定义通道1功率"
        accuracy_decimals: 3

---

# ============================================================================
# 配置说明
# ============================================================================

# 1. 通信方式选择:
#    - uart: 需要配置uart_id
#    - spi:  需要配置spi_id和cs_pin

# 2. EEPROM存储选择:
#    - 配置eeprom_type: 自动链接calibration_storage库
#    - 不配置eeprom_type: 不链接calibration_storage库

# 3. 芯片型号:
#    - bl0906: 6通道，支持ch1-ch6
#    - bl0910: 10通道，支持ch1-ch10

# 4. 库链接策略:
#    - 基础库: 总是链接core和comm_common
#    - 通信库: 根据communication参数选择uart_comm或spi_comm
#    - 存储库: 根据是否配置eeprom_type决定是否链接calibration_storage

# 5. 容错模式:
#    - fault_tolerant: true  - 遇到错误继续运行
#    - fault_tolerant: false - 遇到错误停止组件
