# BL0906 Factory 预编译库 - ESP32-C3 (RISC-V)

## 概述

这是BL0906 Factory组件的预编译核心库，包含了关键的算法实现和IP保护。
本版本专为 **ESP32-C3 (RISC-V)** 架构编译。

## 架构信息

- **目标架构**: ESP32-C3 (RISC-V)
- **编译器**: riscv32-esp-elf-g++
- **架构标志**: -march=rv32imc -mabi=ilp32
- **库文件名**: libbl0906_core_esp32c3.a

## 文件说明

### 预编译库文件 (5个)
- `libbl0906_core_esp32c3.a` - 核心算法库（ESP32-C3 (RISC-V) 架构）
- `libcalibration_storage_esp32c3.a` - 校准数据存储库
- `libbl0906_comm_common_esp32c3.a` - 通用通信库
- `libbl0906_uart_comm_esp32c3.a` - UART协议库
- `libbl0906_spi_comm_esp32c3.a` - SPI协议库

### C/C++接口文件 (14个)
- `bl0906_core_api.h` - 核心C接口（包含校准数据结构）
- `bl0906_wrapper.h/.cpp` - ESPHome包装层
- `bl0906_*_api.h` - 通信接口头文件
- `calibration_storage_*.h` - 校准存储接口
- `communication_adapter_*.h/.cpp` - 通信适配器

### Python组件文件 (2个)
- `__init__.py` - ESPHome组件定义
- `sensor.py` - 传感器实现

### 文档文件 (2个)
- `VERSION` - 版本信息文件
- `README.md` - 说明文档

## 构建信息

- **构建日期**: 2025-07-25 14:45:03
- **目标架构**: ESP32-C3 (RISC-V)
- **库文件名**: libbl0906_core_esp32c3.a
