#!/usr/bin/env python3
"""
BL0906 Factory 发布版库选择逻辑测试脚本
测试不同配置组合下的库链接是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

# 模拟ESPHome环境
class MockCodegen:
    def __init__(self):
        self.platformio_options = {}
        
    def add_platformio_option(self, key, value):
        if key not in self.platformio_options:
            self.platformio_options[key] = []
        self.platformio_options[key].extend(value)
        
    def get_build_flags(self):
        return self.platformio_options.get("build_flags", [])

# 模拟配置验证
class MockConfigValidation:
    @staticmethod
    def enum(values, **kwargs):
        return lambda x: x if x in values else None

# 模拟ESPHome模块
class MockESPHome:
    def __init__(self):
        self.codegen = MockCodegen()
        self.config_validation = MockConfigValidation()

# 设置模拟环境
mock_esphome = MockESPHome()
sys.modules['esphome.codegen'] = mock_esphome.codegen
sys.modules['esphome.config_validation'] = mock_esphome.config_validation
sys.modules['esphome.components.uart'] = type('MockModule', (), {})()
sys.modules['esphome.components.i2c'] = type('MockModule', (), {})()
sys.modules['esphome.components.spi'] = type('MockModule', (), {})()
sys.modules['esphome.components.sensor'] = type('MockModule', (), {})()
sys.modules['esphome'] = type('MockModule', (), {'pins': type('MockModule', (), {})()})()
sys.modules['esphome.const'] = type('MockModule', (), {
    'CONF_ID': 'id',
    'CONF_UPDATE_INTERVAL': 'update_interval'
})()

# 导入我们要测试的模块
try:
    from __init__ import (
        CONF_COMMUNICATION, CONF_EEPROM_TYPE, CONF_UART_ID, CONF_SPI_ID, CONF_CS_PIN,
        COMMUNICATION_MODES, EEPROM_TYPES
    )
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class LibrarySelectionTester:
    def __init__(self):
        self.test_cases = []
        self.results = []
        
    def add_test_case(self, name, config, expected_libs):
        """添加测试用例"""
        self.test_cases.append({
            'name': name,
            'config': config,
            'expected_libs': expected_libs
        })
        
    def simulate_to_code(self, config):
        """模拟to_code函数的库选择逻辑"""
        # 重置codegen状态
        mock_esphome.codegen.platformio_options = {}
        
        # 模拟库选择逻辑（从__init__.py复制）
        required_libs = [
            "bl0906_core_esp32c3",           # 核心算法库
            "bl0906_comm_common_esp32c3"     # 通用通信库
        ]
        
        # 根据通信方式添加对应的库
        comm_mode = config[CONF_COMMUNICATION]
        if comm_mode == "uart":
            required_libs.append("bl0906_uart_comm_esp32c3")
        elif comm_mode == "spi":
            required_libs.append("bl0906_spi_comm_esp32c3")
        
        # 根据是否启用EEPROM存储添加校准存储库
        eeprom_type = config.get(CONF_EEPROM_TYPE)
        if eeprom_type is not None:
            required_libs.append("calibration_storage_esp32c3")
        
        # 构建build_flags
        build_flags = [f"-I.", f"-L."]
        for lib in required_libs:
            build_flags.append(f"-l{lib}")
        
        mock_esphome.codegen.add_platformio_option("build_flags", build_flags)
        
        return required_libs
        
    def run_test_case(self, test_case):
        """运行单个测试用例"""
        print(f"\n🧪 测试用例: {test_case['name']}")
        print(f"📋 配置: {test_case['config']}")
        
        try:
            actual_libs = self.simulate_to_code(test_case['config'])
            expected_libs = test_case['expected_libs']
            
            print(f"🎯 期望库: {expected_libs}")
            print(f"📦 实际库: {actual_libs}")
            
            # 检查库是否匹配
            if set(actual_libs) == set(expected_libs):
                print("✅ 测试通过")
                return True
            else:
                print("❌ 测试失败")
                missing = set(expected_libs) - set(actual_libs)
                extra = set(actual_libs) - set(expected_libs)
                if missing:
                    print(f"   缺少库: {missing}")
                if extra:
                    print(f"   多余库: {extra}")
                return False
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False
            
    def run_all_tests(self):
        """运行所有测试用例"""
        print("🚀 开始BL0906 Factory库选择逻辑测试")
        print("=" * 60)
        
        passed = 0
        total = len(self.test_cases)
        
        for test_case in self.test_cases:
            if self.run_test_case(test_case):
                passed += 1
                
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！库选择逻辑正确。")
            return True
        else:
            print("💥 部分测试失败，请检查库选择逻辑。")
            return False

def main():
    tester = LibrarySelectionTester()
    
    # 测试用例1: UART + EEPROM
    tester.add_test_case(
        "UART通信 + EEPROM存储",
        {
            CONF_COMMUNICATION: "uart",
            CONF_UART_ID: "uart_bus",
            CONF_EEPROM_TYPE: "24c08"
        },
        [
            "bl0906_core_esp32c3",
            "bl0906_comm_common_esp32c3", 
            "bl0906_uart_comm_esp32c3",
            "calibration_storage_esp32c3"
        ]
    )
    
    # 测试用例2: SPI + EEPROM
    tester.add_test_case(
        "SPI通信 + EEPROM存储",
        {
            CONF_COMMUNICATION: "spi",
            CONF_SPI_ID: "spi_bus",
            CONF_CS_PIN: "GPIO5",
            CONF_EEPROM_TYPE: "24c16"
        },
        [
            "bl0906_core_esp32c3",
            "bl0906_comm_common_esp32c3",
            "bl0906_spi_comm_esp32c3", 
            "calibration_storage_esp32c3"
        ]
    )
    
    # 测试用例3: UART + 无EEPROM
    tester.add_test_case(
        "UART通信 + 无EEPROM存储",
        {
            CONF_COMMUNICATION: "uart",
            CONF_UART_ID: "uart_bus"
            # 不设置CONF_EEPROM_TYPE
        },
        [
            "bl0906_core_esp32c3",
            "bl0906_comm_common_esp32c3",
            "bl0906_uart_comm_esp32c3"
            # 不包含calibration_storage_esp32c3
        ]
    )
    
    # 测试用例4: SPI + 无EEPROM
    tester.add_test_case(
        "SPI通信 + 无EEPROM存储", 
        {
            CONF_COMMUNICATION: "spi",
            CONF_SPI_ID: "spi_bus",
            CONF_CS_PIN: "GPIO5"
            # 不设置CONF_EEPROM_TYPE
        },
        [
            "bl0906_core_esp32c3",
            "bl0906_comm_common_esp32c3",
            "bl0906_spi_comm_esp32c3"
            # 不包含calibration_storage_esp32c3
        ]
    )
    
    # 运行所有测试
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
