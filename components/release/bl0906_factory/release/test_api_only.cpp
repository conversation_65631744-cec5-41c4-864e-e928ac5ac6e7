/*
 * BL0906 Factory API 声明测试
 * 只测试API头文件的正确性，不依赖ESPHome
 */

#include <cstdint>
#include <cstddef>

// 测试API头文件
#include "bl0906_core_api.h"

// 测试通信适配器接口
#include "communication_adapter_interface.h"

int main() {
    // 测试C API函数声明
    bl0906_result_t result;
    
    // 测试基础API
    bl0906_comm_callbacks_t callbacks = {nullptr, nullptr, nullptr};
    result = bl0906_core_init(BL0906_CHIP_BL0906, BL0906_COMM_UART, &callbacks);
    
    // 测试传感器数据读取
    bl0906_sensor_data_t data;
    result = bl0906_core_read_sensor_data(&data);
    
    // 测试EEPROM存储API
    result = bl0906_core_init_eeprom_storage(BL0906_EEPROM_24C08, 0x50);
    
    bl0906_calibration_entry_t entries[10];
    size_t actual_count;
    result = bl0906_core_load_calibration_from_eeprom(0x12345678, entries, 10, &actual_count);
    
    // 测试频率适配API
    result = bl0906_core_auto_detect_frequency();
    result = bl0906_core_set_frequency_mode(true);
    
    // 测试工厂函数（C++环境）
    void* uart_adapter = bl0906_core_create_uart_adapter(nullptr);
    void* spi_adapter = bl0906_core_create_spi_adapter(nullptr, nullptr);
    
    if (uart_adapter) {
        bl0906_core_destroy_adapter(uart_adapter);
    }
    if (spi_adapter) {
        bl0906_core_destroy_adapter(spi_adapter);
    }
    
    // 测试通信适配器接口
    bl0906_core::CommunicationError error = bl0906_core::CommunicationError::SUCCESS;
    bl0906_core::CommunicationStats stats;
    
    // 测试枚举值
    bl0906_chip_model_t chip = BL0906_CHIP_BL0906;
    bl0906_comm_type_t comm = BL0906_COMM_UART;
    bl0906_eeprom_type_t eeprom = BL0906_EEPROM_24C08;
    bl0906_voltage_sampling_mode_t voltage_mode = BL0906_VOLTAGE_SAMPLING_TRANSFORMER;
    
    // 测试结构体
    bl0906_chip_info_t chip_info;
    bl0906_calibration_coefficients_t coefficients;
    bl0906_reference_params_t ref_params;
    
    // 避免未使用变量警告
    (void)result;
    (void)data;
    (void)actual_count;
    (void)error;
    (void)stats;
    (void)chip;
    (void)comm;
    (void)eeprom;
    (void)voltage_mode;
    (void)chip_info;
    (void)coefficients;
    (void)ref_params;
    
    return 0;
}

/*
这个测试只验证API声明的正确性：
1. 所有函数都能正确声明
2. 所有枚举类型都能正确使用
3. 所有结构体都能正确定义
4. C/C++混合编译没有问题
*/
