#pragma once

#include <stdarg.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

// 日志级别枚举
typedef enum {
    BL0906_LOG_LEVEL_NONE = 0,
    BL0906_LOG_LEVEL_ERROR = 1,
    BL0906_LOG_LEVEL_WARN = 2,
    BL0906_LOG_LEVEL_INFO = 3,
    BL0906_LOG_LEVEL_DEBUG = 4,
    BL0906_LOG_LEVEL_VERBOSE = 5
} bl0906_log_level_t;

// 日志回调函数类型
typedef void (*bl0906_log_callback_t)(bl0906_log_level_t level, const char* tag, const char* format, va_list args);

// 默认日志实现（使用printf）
void bl0906_log_default(bl0906_log_level_t level, const char* tag, const char* format, va_list args);

// 设置日志回调函数
void bl0906_log_set_callback(bl0906_log_callback_t callback);

// 设置日志级别
void bl0906_log_set_level(bl0906_log_level_t level);

// 日志宏定义
#define BL0906_LOG(level, tag, format, ...) bl0906_log_printf(level, tag, format, ##__VA_ARGS__)
#define BL0906_LOGE(tag, format, ...) BL0906_LOG(BL0906_LOG_LEVEL_ERROR, tag, format, ##__VA_ARGS__)
#define BL0906_LOGW(tag, format, ...) BL0906_LOG(BL0906_LOG_LEVEL_WARN, tag, format, ##__VA_ARGS__)
#define BL0906_LOGI(tag, format, ...) BL0906_LOG(BL0906_LOG_LEVEL_INFO, tag, format, ##__VA_ARGS__)
#define BL0906_LOGD(tag, format, ...) BL0906_LOG(BL0906_LOG_LEVEL_DEBUG, tag, format, ##__VA_ARGS__)
#define BL0906_LOGV(tag, format, ...) BL0906_LOG(BL0906_LOG_LEVEL_VERBOSE, tag, format, ##__VA_ARGS__)

// 内部函数
void bl0906_log_printf(bl0906_log_level_t level, const char* tag, const char* format, ...);

#ifdef __cplusplus
}

// C++ 命名空间兼容
namespace bl0906_core {

// C++ 日志类
class Logger {
public:
    static void set_callback(bl0906_log_callback_t callback) {
        bl0906_log_set_callback(callback);
    }
    
    static void set_level(bl0906_log_level_t level) {
        bl0906_log_set_level(level);
    }
    
    static void log(bl0906_log_level_t level, const char* tag, const char* format, ...) {
        va_list args;
        va_start(args, format);
        bl0906_log_printf(level, tag, format, args);
        va_end(args);
    }
};

}  // namespace bl0906_core

#endif 