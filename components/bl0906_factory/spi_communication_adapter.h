#pragma once

// 只有在启用SPI通信适配器时才编译此文件
#ifdef USE_SPI_COMMUNICATION_ADAPTER

#include "communication_adapter_base.h"
#include "esphome/components/spi/spi.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include "esphome/core/gpio.h"
#include <functional>

namespace esphome {
namespace bl0906_factory {

/**
 * SPI通信适配器实现
 * 
 * 实现基于SPI的BL0906通信协议
 * 
 * ==================== SPI通信重要注意事项 ====================
 * 
 * 1. 【继承架构】- 绝对不要修改！
 *    - 必须同时继承 CommunicationAdapterBase 和 spi::SPIDevice<模板参数>
 *    - 使用继承而不是组合模式，这是ESPHome SPI的标准用法
 *    - 模板参数：BIT_ORDER_MSB_FIRST, CLOCK_POLARITY_LOW, CLOCK_PHASE_TRAILING, DATA_RATE_1MHZ
 * 
 * 2. 【构造函数】- 保持简单！
 *    - 使用默认构造函数：SpiCommunicationAdapter() = default
 *    - 不要在构造函数中传递SPI组件或CS引脚参数
 *    - 通过setter方法设置：set_spi_parent() 和 set_cs_pin()
 * 
 * 3. 【成员变量】- 使用继承的变量！
 *    - 使用 this->parent_ (来自SPIDevice基类)
 *    - 使用 this->cs_ (来自SPIDevice基类)
 *    - 不要自定义 spi_parent_, cs_pin_ 等变量
 * 
 * 4. 【SPI操作】- 直接调用继承方法！
 *    - 使用 this->enable() / this->disable()
 *    - 使用 this->write_array() / this->transfer_array()
 *    - 使用 this->spi_setup() 初始化
 *    - 不要通过中间变量调用
 * 
 * 5. 【初始化流程】- 严格按顺序！
 *    - 1) 创建对象：new SpiCommunicationAdapter()
 *    - 2) 设置父组件：adapter->set_spi_parent(spi_component)
 *    - 3) 设置CS引脚：adapter->set_cs_pin(cs_pin)
 *    - 4) 调用初始化：adapter->initialize()
 * 
 * 6. 【BL0906协议常量】- 使用固定值！
 *    - SPI读命令：0x82 (不要使用宏定义)
 *    - SPI写命令：0x81 (不要使用宏定义)
 *    - 这些值是BL0906芯片固定的协议常量
 * 
 * 7. 【延时函数】- 使用正确的函数！
 *    - 微秒延时：delayMicroseconds(us)
 *    - 毫秒延时：delay(ms)
 *    - 不要使用 esphome::delay()
 * 
 * 8. 【错误处理】- 继承基类功能！
 *    - 使用基类的 set_error(), update_statistics()
 *    - 使用基类的重试机制 execute_with_retry()
 *    - 不要重复实现错误处理逻辑
 * 
 * 9. 【编译保护】- 条件编译很重要！
 *    - 整个文件用 #ifdef USE_SPI_COMMUNICATION_ADAPTER 保护
 *    - 确保只在启用SPI适配器时才编译
 * 
 * 10. 【调试日志】- 使用统一的TAG！
 *     - 使用 static const char *const TAG = "spi_comm_adapter"
 *     - 日志级别：ESP_LOGI, ESP_LOGD, ESP_LOGV, ESP_LOGE
 * 
 * ==================== 修改代码前必读 ====================
 * 如果需要修改此文件，请：
 * 1. 仔细阅读上述注意事项
 * 2. 参考重构前的工作代码 (tests/components/old/)
 * 3. 测试UART和SPI两种通信方式
 * 4. 确保编译通过且功能正常
 * =========================================================
 */
class SpiCommunicationAdapter : public CommunicationAdapterBase, 
                               public spi::SPIDevice<spi::BIT_ORDER_MSB_FIRST, spi::CLOCK_POLARITY_LOW, spi::CLOCK_PHASE_TRAILING, spi::DATA_RATE_1MHZ> {
public:
  SpiCommunicationAdapter() = default;
  ~SpiCommunicationAdapter() override = default;
  
  // ========== 设置方法 ==========
  void set_spi_parent(spi::SPIComponent *parent);
  void set_cs_pin(GPIOPin *cs_pin);
  
  // ========== CommunicationAdapterInterface 实现 ==========
  
  bool initialize() override;
  int32_t read_register(uint8_t address, bool* success = nullptr) override;
  bool write_register(uint8_t address, int16_t value) override;
  bool send_raw_command(const uint8_t* command, size_t length) override;
  
  bool is_available() override;
  bool is_connected() override;
  void flush_buffer() override;
  
  std::string get_adapter_type() const override;
  std::string get_status_info() const override;
  bool self_test() override;

private:
  // ========== 配置参数 ==========
  
  // 【BL0906 SPI时序参数】- 不要随意修改！
  static constexpr uint32_t SPI_DELAY_US = 50;       // SPI操作间延时（微秒）
  static constexpr uint32_t CS_SETUP_DELAY_US = 10;  // CS建立时间（微秒）
  
  // 注意：这些时序参数是根据BL0906芯片规格书确定的
  // 修改可能导致通信不稳定或失败
  
  // ========== 内部方法 ==========
  
  /**
   * 发送SPI读取命令并接收响应
   * @param address 寄存器地址
   * @param success 成功标志指针
   * @return 读取到的值
   */
  int32_t send_spi_read_command(uint8_t address, bool* success);
  
  /**
   * 发送SPI写入命令
   * @param address 寄存器地址
   * @param value 要写入的值
   * @return true 写入成功，false 写入失败
   */
  bool send_spi_write_command(uint8_t address, int16_t value);
  
  // 注意：以下方法已被通用通信库替代，无需在此声明：
  // - calculate_spi_checksum -> 由 bl0906_spi_api_t 提供
  // - 寄存器类型判断 -> 由 bl0906_comm_common_api_t::is_*_register 函数提供
  
  /**
   * 安全的SPI操作包装器
   * @param operation SPI操作函数
   * @return 操作是否成功
   */
  bool safe_spi_operation(std::function<void()> operation);
  
  // 其他重复方法已移至基类
};

} // namespace bl0906_factory
} // namespace esphome

#endif // USE_SPI_COMMUNICATION_ADAPTER