"""
BL0906Factory 统一配置映射模块
消除所有Python配置文件中的重复映射逻辑
"""

from esphome import config_validation as cv
from esphome.const import (
    UNIT_AMPERE,
    UNIT_CELSIUS,
    UNIT_HERTZ,
    UNIT_KILOWATT_HOURS,
    UNIT_VOLT,
    UNIT_WATT,
    DEVICE_CLASS_CURRENT,
    DEVICE_CLASS_ENERGY,
    DEVICE_CLASS_FREQUENCY,
    DEVICE_CLASS_POWER,
    DEVICE_CLASS_TEMPERATURE,
    DEVICE_CLASS_VOLTAGE,
    STATE_CLASS_MEASUREMENT,
    STATE_CLASS_TOTAL_INCREASING,
    CONF_CURRENT,
    CONF_ENERGY,
    CONF_FREQUENCY,
    CONF_POWER,
    CONF_TEMPERATURE,
    CONF_VOLTAGE,
    ICON_EMPTY,
    UNIT_EMPTY
)

# === 全局常量定义 ===
# 通道数量现在根据芯片型号动态确定
DEFAULT_CHANNEL_COUNT = 6  # BL0906默认通道数

# 芯片型号配置（移除宏定义字段）
CHIP_MODELS = {
    "bl0906": {
        "max_channels": 6
    },
    "bl0910": {
        "max_channels": 10
    }
}

# 用于向后兼容的通道数量常量
CHANNEL_COUNT = DEFAULT_CHANNEL_COUNT

# === 通信模式配置 ===
COMMUNICATION_MODES = {
    "uart": "uart",
    "spi": "spi",
}

# === 存储类型配置 ===
STORAGE_TYPES = {
    "preferences": "preferences",
    "eeprom": "eeprom"
}
EEPROM_TYPES = {
    "24c02": "24c02",
    "24c04": "24c04", 
    "24c08": "24c08",
    "24c16": "24c16"
}

# === 频率适配模式 ===
FREQ_ADAPT_MODES = {
    "off": "OFF",
    "auto": "AUTO",
    "60": "HZ60"
}

# === 电压采样模式 ===
VOLTAGE_SAMPLING_MODES = {
    "transformer": "TRANSFORMER",
    "resistor_divider": "RESISTOR_DIVIDER"
}

# === 传感器类型枚举映射 ===
SENSOR_TYPES = {
    "VOLTAGE": 0,
    "FREQUENCY": 1,
    "TEMPERATURE": 2,
    "CURRENT": 3,
    "POWER": 4,
    "ENERGY": 5,
    "POWER_SUM": 6,
    "ENERGY_SUM": 7,
    "TOTAL_ENERGY": 8,
    "TOTAL_ENERGY_SUM": 9
}

# === 统计传感器类型枚举映射 ===
STATISTICS_SENSOR_TYPES = {
    "YESTERDAY_ENERGY": 0,
    "TODAY_ENERGY": 1,
    "WEEK_ENERGY": 2,
    "MONTH_ENERGY": 3,
    "YEAR_ENERGY": 4,
    "YESTERDAY_TOTAL_ENERGY": 5,
    "TODAY_TOTAL_ENERGY": 6,
    "WEEK_TOTAL_ENERGY": 7,
    "MONTH_TOTAL_ENERGY": 8,
    "YEAR_TOTAL_ENERGY": 9
}

# === 设备属性配置模板 ===
DEVICE_PROPERTY_TEMPLATES = {
    # 基础电学量单位映射
    "voltage": {
        "unit": UNIT_VOLT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_VOLTAGE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "current": {
        "unit": UNIT_AMPERE,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_CURRENT,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "power": {
        "unit": UNIT_WATT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "energy": {
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "frequency": {
        "unit": UNIT_HERTZ,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_FREQUENCY,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "temperature": {
        "unit": UNIT_CELSIUS,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_TEMPERATURE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
}

# === 全局传感器配置映射 ===
GLOBAL_SENSOR_CONFIGS = {
    CONF_FREQUENCY: {
        "type": "FREQUENCY",
        **DEVICE_PROPERTY_TEMPLATES["frequency"]
    },
    CONF_TEMPERATURE: {
        "type": "TEMPERATURE",
        **DEVICE_PROPERTY_TEMPLATES["temperature"]
    },
    CONF_VOLTAGE: {
        "type": "VOLTAGE",
        **DEVICE_PROPERTY_TEMPLATES["voltage"]
    },
    "power_sum": {
        "type": "POWER_SUM",
        **DEVICE_PROPERTY_TEMPLATES["power"]
    },
    "energy_sum": {
        "type": "ENERGY_SUM",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "total_energy_sum": {
        "type": "TOTAL_ENERGY_SUM",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    # 总电量统计传感器
    "yesterday_total_energy": {
        "type": "YESTERDAY_TOTAL_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "today_total_energy": {
        "type": "TODAY_TOTAL_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "week_total_energy": {
        "type": "WEEK_TOTAL_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "month_total_energy": {
        "type": "MONTH_TOTAL_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "year_total_energy": {
        "type": "YEAR_TOTAL_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
}

# === 通道传感器配置模板 ===
CHANNEL_SENSOR_TEMPLATES = {
    CONF_CURRENT: {
        "type": "CURRENT",
        **DEVICE_PROPERTY_TEMPLATES["current"]
    },
    CONF_POWER: {
        "type": "POWER",
        **DEVICE_PROPERTY_TEMPLATES["power"]
    },
    CONF_ENERGY: {
        "type": "ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "total_energy": {
        "type": "TOTAL_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    # 电量统计传感器
    "yesterday_energy": {
        "type": "YESTERDAY_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "today_energy": {
        "type": "TODAY_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "week_energy": {
        "type": "WEEK_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "month_energy": {
        "type": "MONTH_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
    "year_energy": {
        "type": "YEAR_ENERGY",
        **DEVICE_PROPERTY_TEMPLATES["energy"]
    },
}

# === 校准类型配置 ===
# 使用最大通道数以支持所有芯片型号，但在运行时会根据实际芯片型号进行验证
MAX_CHANNELS = max(chip_info["max_channels"] for chip_info in CHIP_MODELS.values())

CALIB_TYPES = {
    "CHGN": {
        "name": "电流增益",
        "channels": list(range(1, MAX_CHANNELS + 1)) + [-1],  # 1-10通道 + 电压通道(-1)
        "register_prefix": "BL0906_CHGN"
    },
    "CHOS": {
        "name": "电流偏置",
        "channels": list(range(1, MAX_CHANNELS + 1)) + [-1],
        "register_prefix": "BL0906_CHOS"
    },
    "RMSGN": {
        "name": "有效值增益",
        "channels": list(range(1, MAX_CHANNELS + 1)),
        "register_prefix": "BL0906_RMSGN"
    },
    "RMSOS": {
        "name": "有效值偏置",
        "channels": list(range(1, MAX_CHANNELS + 1)),
        "register_prefix": "BL0906_RMSOS"
    },
    "WATTGN": {
        "name": "功率增益",
        "channels": list(range(1, MAX_CHANNELS + 1)),
        "register_prefix": "BL0906_WATTGN"
    },
    "WATTOS": {
        "name": "功率偏置",
        "channels": list(range(1, MAX_CHANNELS + 1)),
        "register_prefix": "BL0906_WATTOS"
    }
}

# === 校准Number配置生成器 ===
def generate_number_configs():
    """动态生成Number配置映射"""
    number_configs = {}
    
    for calib_type, type_config in CALIB_TYPES.items():
        for channel in type_config["channels"]:
            if channel == -1:  # 电压通道
                key = f"{calib_type.lower()}_v_decimal"
                internal_channel = -1
            else:
                key = f"{calib_type.lower()}_decimal_{channel}"
                internal_channel = channel - 1  # 内部使用0-5索引

            number_configs[key] = {
                "type": calib_type,
                "channel": internal_channel,
                "register_prefix": type_config["register_prefix"],
                "unit": UNIT_EMPTY,
                "icon": ICON_EMPTY,
                "min_value": -32768,
                "max_value": 32767,
                "step": 1.0,
            }
    
    return number_configs

# === 缓存的Number配置 ===
NUMBER_CONFIGS = generate_number_configs()

# === 配置验证器 ===
class ConfigValidator:
    """统一的配置验证器"""
    
    @staticmethod
    def validate_communication_config(config):
        """验证通信配置"""
        comm_mode = config.get("communication")
        if not comm_mode:
            return config
            
        if comm_mode == "uart":
            if "uart_id" not in config:
                raise cv.Invalid("uart_id is required when using UART communication")
            # 清理SPI配置
            config.pop("spi_id", None)
            config.pop("cs_pin", None)
        elif comm_mode == "spi":
            if "spi_id" not in config:
                raise cv.Invalid("spi_id is required when using SPI communication")
            if "cs_pin" not in config:
                raise cv.Invalid("cs_pin is required when using SPI communication")
            # 清理UART配置
            config.pop("uart_id", None)
        
        return config
    
    @staticmethod
    def validate_i2c_dependency(config):
        """验证I2C依赖（用于EEPROM存储）"""
        calibration = config.get("calibration", {})
        if calibration.get("storage_type") == "eeprom":
            if "i2c_id" not in config:
                raise cv.Invalid("i2c_id is required when using EEPROM storage")
            if "address" not in config:
                config["address"] = 0x50  # 默认EEPROM地址
        return config

# === 配置模式生成器 ===
class SchemaBuilder:
    """统一的配置模式构建器"""
    
    @staticmethod
    def get_initial_calibration_schema():
        """获取初始校准配置模式"""
        return cv.Schema({
            cv.Required("register"): cv.hex_uint8_t,
            cv.Required("value"): cv.int_range(min=-32768, max=32767),
        })
    
    @staticmethod
    def get_calibration_schema():
        """获取校准配置模式"""
        return cv.Schema({
            cv.Optional("enabled", default=True): cv.boolean,
            cv.Optional("storage_type", default="preference"): cv.one_of(*STORAGE_TYPES, lower=True),
            cv.Optional("eeprom_type", default="24c02"): cv.one_of(*EEPROM_TYPES, lower=True),
        })

# === 工具函数 ===
def is_statistics_sensor(sensor_type):
    """判断是否为统计传感器"""
    return sensor_type in STATISTICS_SENSOR_TYPES

def get_sensor_enum_expression(sensor_type, is_statistics=False):
    """获取传感器枚举表达式"""
    if is_statistics:
        return f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}"
    else:
        return f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}"

# ✅ 废弃函数已删除 - 避免无意义的包装函数
# 原 get_calib_register_expression 函数已删除，因为：
# 1. 只返回占位值，没有实际功能
# 2. 违反了代码质量标准中的"禁止无意义中间层"原则
# 3. 重构后的系统通过 CalibNumberType 和通道号直接操作，不需要此函数

def get_calib_type_enum_expression(calib_type):
    """获取校准类型枚举表达式"""
    return f"esphome::bl0906_factory::BL0906Factory::CalibNumberType::{calib_type}" 