#include "communication_adapter_base.h"
#include "bl0906_chip_params.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "comm_adapter_base";

// ========== 通用接口实现 ==========

std::string CommunicationAdapterBase::get_last_error() const {
  return last_error_message_;
}

void CommunicationAdapterBase::reset_error_state() {
  last_error_ = CommunicationError::SUCCESS;
  last_error_message_.clear();
}

CommunicationError CommunicationAdapterBase::get_last_error_code() const {
  return last_error_;
}

size_t CommunicationAdapterBase::get_success_count() const {
  return stats_.success_count;
}

size_t CommunicationAdapterBase::get_error_count() const {
  return stats_.error_count;
}

CommunicationStats CommunicationAdapterBase::get_statistics() const {
  return stats_;
}

void CommunicationAdapterBase::reset_statistics() {
  stats_ = CommunicationStats{};
}

// ========== 保护的通用方法实现 ==========

void CommunicationAdapterBase::set_error(CommunicationError error, const std::string& message) {
  last_error_ = error;
  last_error_message_ = message;
  stats_.last_error = error;
  stats_.last_error_timestamp = esphome::millis();
  
  ESP_LOGW(TAG, "通信错误 [%s]: %s", get_adapter_type().c_str(), message.c_str());
}

void CommunicationAdapterBase::update_statistics(bool success, CommunicationError error) {
  if (success) {
    stats_.success_count++;
  } else {
    stats_.error_count++;
    
    // 根据错误类型更新特定计数器
    switch (error) {
      case CommunicationError::TIMEOUT:
        stats_.timeout_count++;
        break;
      case CommunicationError::CHECKSUM_ERROR:
        stats_.checksum_error_count++;
        break;
      case CommunicationError::HARDWARE_ERROR:
        stats_.hardware_error_count++;
        break;
      case CommunicationError::DEVICE_NOT_AVAILABLE:
        stats_.device_not_available_count++;
        break;
      case CommunicationError::INVALID_RESPONSE:
        stats_.invalid_response_count++;
        break;
      default:
        break;
    }
  }
}

bool CommunicationAdapterBase::check_initialized() const {
  if (!initialized_) {
    ESP_LOGW(TAG, "%s适配器未初始化", get_adapter_type().c_str());
    return false;
  }
  return true;
}

bool CommunicationAdapterBase::verify_register_value(int16_t expected_value, int32_t actual_value, uint8_t register_address) {
  if (actual_value == expected_value) {
    ESP_LOGI(TAG, "%s寄存器 0x%02X 写入验证成功，值: %d", 
             get_adapter_type().c_str(), register_address, expected_value);
    return true;
  } else {
    std::string error_msg = get_adapter_type() + "寄存器写入验证失败，写入值=" + 
                            std::to_string(expected_value) + "，读回值=" + std::to_string(actual_value);
    set_error(CommunicationError::HARDWARE_ERROR, error_msg);
    return false;
  }
}

void CommunicationAdapterBase::log_operation_start(const std::string& operation_name, uint8_t register_address, int32_t value) {
  if (value != 0) {
    ESP_LOGI(TAG, "开始%s %s寄存器 0x%02X，值: %d", 
             operation_name.c_str(), get_adapter_type().c_str(), register_address, value);
  } else {
    ESP_LOGI(TAG, "开始%s %s寄存器 0x%02X", 
             operation_name.c_str(), get_adapter_type().c_str(), register_address);
  }
}

void CommunicationAdapterBase::log_operation_result(const std::string& operation_name, uint8_t register_address, bool success, int32_t value) {
  if (success) {
    if (value != 0) {
      ESP_LOGI(TAG, "%s %s寄存器 0x%02X 成功，值: %d", 
               operation_name.c_str(), get_adapter_type().c_str(), register_address, value);
    } else {
      ESP_LOGI(TAG, "%s %s寄存器 0x%02X 成功", 
               operation_name.c_str(), get_adapter_type().c_str(), register_address);
    }
  } else {
    ESP_LOGE(TAG, "%s %s寄存器 0x%02X 失败: %s", 
             operation_name.c_str(), get_adapter_type().c_str(), register_address, last_error_message_.c_str());
  }
}

// ========== 通用数据处理方法实现（内联实现，统一UART和SPI逻辑）==========

int32_t CommunicationAdapterBase::parse_register_response(uint8_t address, uint8_t data_h, uint8_t data_m, uint8_t data_l) {
  // 根据寄存器类型选择解析方式
  if (is_16bit_register(address)) {
    // 16位寄存器：只使用中低字节
    return convert_to_signed_16bit(data_m, data_l);
  } else if (is_unsigned_register(address)) {
    // 24位无符号寄存器
    return static_cast<int32_t>(convert_to_unsigned_24bit(data_h, data_m, data_l));
  } else {
    // 24位有符号寄存器
    return convert_to_signed_24bit(data_h, data_m, data_l);
  }
}

bool CommunicationAdapterBase::prepare_register_write_data(uint8_t address, int16_t value, uint8_t& data_h, uint8_t& data_m, uint8_t& data_l) {
  // 校准寄存器写入数据准备
  if (is_16bit_register(address)) {
    // 16位寄存器：高字节为0
    data_h = 0x00;
    data_m = (value >> 8) & 0xFF;
    data_l = value & 0xFF;
  } else {
    // 24位寄存器：扩展符号位
    if (value < 0) {
      // 负数：扩展符号位
      data_h = 0xFF;
      data_m = (value >> 8) & 0xFF;
      data_l = value & 0xFF;
    } else {
      // 正数：高字节为0
      data_h = 0x00;
      data_m = (value >> 8) & 0xFF;
      data_l = value & 0xFF;
    }
  }
  return true;
}

bool CommunicationAdapterBase::verify_write_operation(uint8_t address, int16_t expected_value) {
  // 读回寄存器值进行验证
  bool read_success = false;
  int32_t actual_value = read_register(address, &read_success);
  
  if (!read_success) {
    set_error(CommunicationError::INVALID_RESPONSE, "写入验证读取失败");
    return false;
  }
  
  // 验证结果（考虑16位和24位寄存器的差异）
  if (is_16bit_register(address)) {
    // 16位寄存器：只比较低16位
    int16_t actual_16bit = static_cast<int16_t>(actual_value & 0xFFFF);
    if (actual_16bit != expected_value) {
      set_error(CommunicationError::HARDWARE_ERROR, 
                "写入验证失败，期望值=" + std::to_string(expected_value) + 
                "，实际值=" + std::to_string(actual_16bit));
      return false;
    }
  } else {
    // 24位寄存器：扩展期望值到24位后比较
    int32_t expected_24bit = (expected_value < 0) ? (expected_value | 0xFF0000) : expected_value;
    if (actual_value != expected_24bit) {
      set_error(CommunicationError::HARDWARE_ERROR, 
                "写入验证失败，期望值=" + std::to_string(expected_24bit) + 
                "，实际值=" + std::to_string(actual_value));
      return false;
    }
  }
  
  return true;
}

bool CommunicationAdapterBase::is_16bit_register(uint8_t address) {
  // 根据BL0906芯片手册，校准寄存器大多为16位
  // 使用地址范围判断，避免依赖具体的地址常量
  
  // 校准寄存器地址范围：
  // RMSGN: 0x6C-0x75 (BL0906: 0x6D-0x74, BL0910: 0x6C-0x75)
  // RMSOS: 0x78-0x81 (BL0906: 0x78-0x7F, BL0910: 0x78-0x81)  
  // WATTGN: 0x83-0x8C (BL0906: 0x83-0x8A, BL0910: 0x83-0x8C)
  // WATTOS: 0x8E-0x97 (BL0906: 0x8E-0x95, BL0910: 0x8E-0x97)
  // CHGN: 0xA0-0xAA (BL0906: 0xA1-0xA8, BL0910: 0xA0-0xAA)
  // CHOS: 0xAC-0xB6 (BL0906: 0xAC-0xB3, BL0910: 0xAC-0xB6)
  
  if ((address >= 0x6C && address <= 0x75) ||  // RMSGN 范围
      (address >= 0x78 && address <= 0x81) ||  // RMSOS 范围
      (address >= 0x83 && address <= 0x8C) ||  // WATTGN 范围
      (address >= 0x8E && address <= 0x97) ||  // WATTOS 范围
      (address >= 0xA0 && address <= 0xAA) ||  // CHGN 范围
      (address >= 0xAC && address <= 0xB6)) {  // CHOS 范围
    return true;
  }
  
  return false;
}

bool CommunicationAdapterBase::is_unsigned_register(uint8_t address) {
  // 根据BL0906芯片手册，能量计数器为无符号数
  // 使用地址范围判断，避免依赖具体的地址常量
  
  // 能量计数器地址范围：
  // CF_CNT: 0x30-0x39 (BL0906: 0x30-0x37, BL0910: 0x30-0x39)
  // CF_SUM: 0x07 (两个芯片都是)
  
  if ((address >= 0x30 && address <= 0x39) ||  // CF_CNT 范围
      address == 0x07) {                        // CF_SUM
    return true;
  }
  
  return false;
}

int32_t CommunicationAdapterBase::convert_to_signed_24bit(uint8_t data_h, uint8_t data_m, uint8_t data_l) {
  // 组合24位数据
  uint32_t raw_value = (static_cast<uint32_t>(data_h) << 16) | 
                       (static_cast<uint32_t>(data_m) << 8) | 
                       static_cast<uint32_t>(data_l);
  
  // 检查符号位（第23位）
  if (raw_value & 0x800000) {
    // 负数：扩展符号位到32位
    return static_cast<int32_t>(raw_value | 0xFF000000);
  } else {
    // 正数：直接转换
    return static_cast<int32_t>(raw_value);
  }
}

uint32_t CommunicationAdapterBase::convert_to_unsigned_24bit(uint8_t data_h, uint8_t data_m, uint8_t data_l) {
  // 直接组合24位数据（无符号）
  return (static_cast<uint32_t>(data_h) << 16) | 
         (static_cast<uint32_t>(data_m) << 8) | 
         static_cast<uint32_t>(data_l);
}

int16_t CommunicationAdapterBase::convert_to_signed_16bit(uint8_t data_m, uint8_t data_l) {
  // 组合16位数据
  uint16_t raw_value = (static_cast<uint16_t>(data_m) << 8) | static_cast<uint16_t>(data_l);
  return static_cast<int16_t>(raw_value);
}

} // namespace bl0906_factory
} // namespace esphome 