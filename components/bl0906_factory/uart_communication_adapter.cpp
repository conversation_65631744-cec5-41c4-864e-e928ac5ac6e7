#ifdef USE_UART_COMMUNICATION_ADAPTER

#include "uart_communication_adapter.h"
#include "bl0906_chip_params.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include <functional>

// 移除对外部C库的依赖，使用基类的通用数据处理方法

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "uart_comm_adapter";

// ========== 设置方法 ==========
// 注意：移除了set_uart_parent方法，因为现在直接在构造函数中设置parent

// ========== CommunicationAdapterInterface 实现 ==========

bool UartCommunicationAdapter::initialize() {
  if (initialized_) {
    return true;
  }
  
  if (!this->parent_) {
    set_error(CommunicationError::HARDWARE_ERROR, "UART父组件未设置");
    return false;
  }
  
  // 重置统计信息
  reset_statistics();
  reset_error_state();
  
  // 清空缓冲区
  flush_buffer();
  
  initialized_ = true;
  ESP_LOGI(TAG, "UART通信适配器初始化成功");
  return true;
}

int32_t UartCommunicationAdapter::read_register(uint8_t address, bool* success) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    if (success) *success = false;
    return 0;
  }

  // 使用重试机制执行读取操作  
  auto read_operation = [this, address, success]() -> int32_t {
    return this->send_read_command_and_receive(address, success);
  };

  return execute_with_retry<int32_t>(read_operation);
}

bool UartCommunicationAdapter::write_register(uint8_t address, int16_t value) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    return false;
  }
  
  // 使用重试机制执行写入操作
  auto write_operation = [this, address, value]() -> bool {
    return this->send_write_command(address, value);
  };
  
  return execute_with_retry<bool>(write_operation);
}

bool UartCommunicationAdapter::send_raw_command(const uint8_t* command, size_t length) {
  if (!command || length == 0) {
    set_error(CommunicationError::INVALID_RESPONSE, "无效的原始命令参数");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  if (!is_available()) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "UART设备不可用");
    update_statistics(false, CommunicationError::DEVICE_NOT_AVAILABLE);
    return false;
  }

  ESP_LOGV(TAG, "发送原始命令，长度: %zu", length);
  
  // 清空接收缓冲区
  flush_buffer();
  
  // 发送原始命令 - 使用继承的方法
  this->write_array(command, length);
  this->flush();
  
  // 记录发送的命令（用于调试）
  std::string cmd_str = "原始命令: ";
  for (size_t i = 0; i < length; i++) {
    cmd_str += format_hex(command[i]);
    if (i < length - 1) cmd_str += " ";
  }
  ESP_LOGV(TAG, "%s", cmd_str.c_str());
  
  update_statistics(true);
  return true;
}

bool UartCommunicationAdapter::is_available() {
  return initialized_ && this->parent_;
}

bool UartCommunicationAdapter::is_connected() {
  return is_available();
}

void UartCommunicationAdapter::flush_buffer() {
  if (!this->parent_) {
    return;
  }
  
  // 批量读取剩余数据，减少系统调用次数
  constexpr size_t BUFFER_SIZE = 64;
  uint8_t temp_buffer[BUFFER_SIZE];
  
  while (this->available() > 0) {
    size_t bytes_to_read = std::min(static_cast<size_t>(this->available()), BUFFER_SIZE);
    this->read_array(temp_buffer, bytes_to_read);
  }
  
  ESP_LOGV(TAG, "已清空UART接收缓冲区");
}

// 错误处理和统计方法已移至基类

std::string UartCommunicationAdapter::get_adapter_type() const {
  return "UART";
}

std::string UartCommunicationAdapter::get_status_info() const {
  auto stats = get_statistics();
  char buffer[256];
  snprintf(buffer, sizeof(buffer),
           "UART适配器状态: 初始化=%s, 成功=%zu, 错误=%zu, 超时=%zu, 校验和错误=%zu",
           initialized_ ? "是" : "否",
           stats.success_count,
           stats.error_count,
           stats.timeout_count,
           stats.checksum_error_count);
  return std::string(buffer);
}

bool UartCommunicationAdapter::self_test() {
  if (!is_available()) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "设备不可用");
    return false;
  }
  
  // 尝试读取温度寄存器作为自检
  bool success = false;
  int32_t temp_value = read_register(TEMPERATURE_ADDR, &success);
  
  if (success && temp_value > 0) {
    ESP_LOGI(TAG, "UART适配器自检通过，温度值: %d", temp_value);
    return true;
  } else {
    set_error(CommunicationError::HARDWARE_ERROR, "自检失败：无法读取温度寄存器");
    return false;
  }
}

// ========== 内部方法实现 ==========

bool UartCommunicationAdapter::wait_until_available(size_t len, uint32_t timeout_ms) {
  const uint32_t start = esphome::millis();
  ESP_LOGV(TAG, "等待数据可用，需要%zu字节，当前可用%d字节，超时%ums",
           len, this->available(), timeout_ms);

  while (this->available() < len) {
    if (esphome::millis() - start > timeout_ms) {
      ESP_LOGW(TAG, "等待数据超时，期望%zu字节，实际可用:%d字节", len, this->available());
      return false;
    }

    // 更频繁地让出CPU时间片
    yield();

    // 短暂延时，减轻CPU负担
    esphome::delay(1);
  }

  ESP_LOGV(TAG, "数据可用，需要%zu字节，当前可用%d字节，等待了%ums",
           len, this->available(), (esphome::millis() - start));
  return true;
}

int32_t UartCommunicationAdapter::send_read_command_and_receive(uint8_t address, bool* success) {
  // 清空缓冲区
  flush_buffer();
  
  // 发送读命令 - 使用继承的方法
  this->write_byte(UART_READ_COMMAND);  // UART读命令(0x35)
  this->write_byte(address);                   // 寄存器地址
  this->flush(); // 确保命令已发送

  // 等待响应数据（3字节数据 + 1字节校验和）
  uint8_t response_data[4];
  if (!wait_until_available(sizeof(response_data), UART_TIMEOUT_MS)) {
    set_error(CommunicationError::TIMEOUT, 
              "等待读取寄存器响应数据超时 (地址: 0x" + format_hex(address) + ")");
    if (success) *success = false;
    update_statistics(false, CommunicationError::TIMEOUT);
    return 0;
  }

  // 读取响应数据 - 使用继承的方法
  if (!this->read_array(response_data, sizeof(response_data))) {
    set_error(CommunicationError::INVALID_RESPONSE,
              "读取寄存器数据包失败 (地址: 0x" + format_hex(address) + ")");
    if (success) *success = false;
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return 0;
  }

  // 验证UART响应数据
  // UART响应格式：[DATA_L] [DATA_M] [DATA_H] [CHECKSUM]
  uint8_t data_l = response_data[0];
  uint8_t data_m = response_data[1];
  uint8_t data_h = response_data[2];
  uint8_t checksum = response_data[3];
  
  // 验证校验和（UART校验和计算方式）
  uint8_t expected_checksum = ~(address + data_h + data_m + data_l);
  if (checksum != expected_checksum) {
    set_error(CommunicationError::CHECKSUM_ERROR,
              "UART校验和错误 地址:0x" + format_hex(address) + 
              " 期望:0x" + format_hex(expected_checksum) + " 实际:0x" + format_hex(checksum));
    if (success) *success = false;
    update_statistics(false, CommunicationError::CHECKSUM_ERROR);
    return 0;
  }

  // 使用基类的统一数据解析方法
  int32_t result_value = parse_register_response(address, data_h, data_m, data_l);

  ESP_LOGV(TAG, "UART读取寄存器 0x%02X: 原始数据[%02X %02X %02X], 解析值: %d", 
           address, data_h, data_m, data_l, result_value);
  
  update_statistics(true);
  if (success) *success = true;
  return result_value;
}

bool UartCommunicationAdapter::send_write_command(uint8_t address, int16_t value) {
  ESP_LOGI(TAG, "正在写入寄存器 0x%02X 值: %d", address, value);

  // 清空接收缓冲区
  flush_buffer();

  // 使用基类的统一数据准备方法
  uint8_t data_h, data_m, data_l;
  if (!prepare_register_write_data(address, value, data_h, data_m, data_l)) {
    set_error(CommunicationError::INVALID_RESPONSE,
              "UART写入数据准备失败 地址:0x" + format_hex(address));
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  // 准备UART写入命令：[0xCA] [ADDR] [DATA_H] [DATA_M] [DATA_L] [CHECKSUM]
  uint8_t buffer[6];
  buffer[0] = 0xCA;       // UART写命令
  buffer[1] = address;    // 寄存器地址
  buffer[2] = data_h;     // 数据高字节
  buffer[3] = data_m;     // 数据中字节
  buffer[4] = data_l;     // 数据低字节
  // 计算校验和：~(ADDR + DATA_H + DATA_M + DATA_L)
  buffer[5] = ~(address + data_h + data_m + data_l);

  ESP_LOGV(TAG, "UART写入寄存器 0x%02X 值: %d", address, value);

  // 发送写命令 - 使用继承的方法
  this->write_array(buffer, 6);
  this->flush();

  ESP_LOGV(TAG, "发送UART写命令: %02X %02X %02X %02X %02X %02X",
           buffer[0], buffer[1], buffer[2], buffer[3], buffer[4], buffer[5]);

  // 延时等待写入完成
  esphome::delay(5);

  // 读回并验证写入的值
  bool read_success = false;
  int32_t read_value = read_register(address, &read_success);

  if (!read_success) {
    set_error(CommunicationError::INVALID_RESPONSE, "写入验证读取失败");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  // 使用基类的统一验证方法
  if (verify_write_operation(address, value)) {
    ESP_LOGI(TAG, "UART寄存器 0x%02X 写入成功，值: %d", address, value);
    update_statistics(true);
    return true;
  } else {
    // 错误信息已在verify_write_operation中设置
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
}

// 注意：原有的辅助函数已被基类的统一方法替代：
// - 数据准备和解析逻辑统一到基类
// - 校验和计算内联实现
// - 错误处理、统计和重试方法已移至基类

} // namespace bl0906_factory
} // namespace esphome

#endif // USE_UART_COMMUNICATION_ADAPTER 