// 只有在启用SPI通信适配器时才编译此文件
#ifdef USE_SPI_COMMUNICATION_ADAPTER

#include "spi_communication_adapter.h"
#include "bl0906_chip_params.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include <functional>

// 移除对外部C库的依赖，使用基类的通用数据处理方法

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "spi_comm_adapter";

/*
 * ==================== SPI通信实现注意事项 ====================
 * 
 * 本文件实现BL0906芯片的SPI通信协议，基于以下关键原则：
 * 
 * 1. 【继承SPIDevice】：直接继承ESPHome的SPIDevice模板类
 *    - 不使用组合模式，避免SPIClient构造函数问题
 *    - 直接调用this->enable(), this->write_array()等方法
 * 
 * 2. 【BL0906 SPI协议】：48位连续传输
 *    - 读取：0x82 + ADDR + 0x00*4 -> CMD_ECHO + ADDR_ECHO + DATA[3] + CHECKSUM
 *    - 写入：0x81 + ADDR + DATA[3] + CHECKSUM (单向传输)
 * 
 * 3. 【关键时序】：
 *    - CS建立时间：10微秒
 *    - SPI操作间延时：50微秒
 *    - 写入后验证延时：10毫秒
 * 
 * 4. 【错误恢复】：使用基类的重试机制，最多3次重试
 * 
 * 5. 【调试建议】：
 *    - 使用ESP_LOGV查看详细的SPI数据传输
 *    - 检查校验和计算是否正确
 *    - 确认CS引脚时序
 * 
 * ============================================================
 */

// ========== 设置方法 ==========
/**
 * 设置SPI父组件
 * 
 * 【重要】：直接设置继承的parent_成员变量
 * - 不要创建新的成员变量
 * - 不要在这里创建SPIDevice对象
 */
void SpiCommunicationAdapter::set_spi_parent(spi::SPIComponent *parent) {
  this->parent_ = parent;  // 设置SPIDevice基类的parent_成员
  ESP_LOGD(TAG, "SPI父组件已设置: %p", parent);
}

/**
 * 设置CS引脚
 * 
 * 【重要】：直接设置继承的cs_成员变量
 * - 不要创建新的成员变量
 * - CS引脚由SPIDevice基类管理
 */
void SpiCommunicationAdapter::set_cs_pin(GPIOPin *cs_pin) {
  this->cs_ = cs_pin;      // 设置SPIDevice基类的cs_成员
  ESP_LOGD(TAG, "CS引脚已设置: %p", cs_pin);
}

// ========== CommunicationAdapterInterface 实现 ==========

bool SpiCommunicationAdapter::initialize() {
  if (initialized_) {
    return true;
  }
  
  if (!this->parent_) {
    set_error(CommunicationError::HARDWARE_ERROR, "SPI父组件未设置");
    return false;
  }
  
  if (!this->cs_) {
    set_error(CommunicationError::HARDWARE_ERROR, "CS引脚未设置");
    return false;
  }
  
  // 关键修复：调用spi_setup()初始化SPI设备
  ESP_LOGI(TAG, "正在初始化SPI设备...");
  this->spi_setup();
  ESP_LOGI(TAG, "SPI设备初始化完成");
  
  // 重置统计信息
  reset_statistics();
  reset_error_state();
  
  initialized_ = true;
  ESP_LOGI(TAG, "SPI通信适配器初始化成功");
  return true;
}

int32_t SpiCommunicationAdapter::read_register(uint8_t address, bool* success) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    if (success) *success = false;
    return 0;
  }
  
  // 使用重试机制执行读取操作
  auto read_operation = [this, address, success]() -> int32_t {
    return this->send_spi_read_command(address, success);
  };
  
  return execute_with_retry<int32_t>(read_operation);
}

bool SpiCommunicationAdapter::write_register(uint8_t address, int16_t value) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    return false;
  }
  
  // 使用重试机制执行写入操作
  auto write_operation = [this, address, value]() -> bool {
    return this->send_spi_write_command(address, value);
  };
  
  return execute_with_retry<bool>(write_operation);
}

bool SpiCommunicationAdapter::send_raw_command(const uint8_t* command, size_t length) {
  ESP_LOGI(TAG, "=== 开始执行SPI原始命令发送 ===");
  
  if (!command || length == 0) {
    ESP_LOGE(TAG, "无效的原始命令参数: command=%p, length=%zu", command, length);
    set_error(CommunicationError::INVALID_RESPONSE, "无效的原始命令参数");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  ESP_LOGI(TAG, "SPI设备可用性检查...");
  if (!is_available()) {
    ESP_LOGE(TAG, "SPI设备不可用: initialized_=%s, parent_=%p, cs_=%p", 
             initialized_ ? "true" : "false", this->parent_, this->cs_);
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "SPI设备不可用");
    update_statistics(false, CommunicationError::DEVICE_NOT_AVAILABLE);
    return false;
  }

  ESP_LOGI(TAG, "发送SPI原始命令，长度: %zu", length);
  
  // 记录要发送的命令（用于调试）
  std::string cmd_str = "准备发送SPI原始命令: ";
  for (size_t i = 0; i < length; i++) {
    cmd_str += format_hex(command[i]);
    if (i < length - 1) cmd_str += " ";
  }
  ESP_LOGI(TAG, "%s", cmd_str.c_str());
  
  // 执行SPI原始命令发送
  ESP_LOGI(TAG, "开始执行SPI操作...");
  bool spi_success = safe_spi_operation([this, command, length]() {
    ESP_LOGV(TAG, "SPI使能CS引脚...");
    this->enable();
    // 短暂延时确保CS稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    
    ESP_LOGV(TAG, "开始SPI数据传输...");
    this->write_array(command, length);
    
    // 添加延时确保数据稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    ESP_LOGV(TAG, "SPI禁用CS引脚...");
    this->disable();
  });
  
  ESP_LOGI(TAG, "SPI操作完成，结果: %s", spi_success ? "成功" : "失败");
  
  if (!spi_success) {
    ESP_LOGE(TAG, "SPI原始命令发送失败");
    set_error(CommunicationError::HARDWARE_ERROR, "SPI原始命令发送失败");
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
  
  // 再次记录发送的命令（确认）
  cmd_str = "SPI原始命令已发送: ";
  for (size_t i = 0; i < length; i++) {
    cmd_str += format_hex(command[i]);
    if (i < length - 1) cmd_str += " ";
  }
  ESP_LOGI(TAG, "%s", cmd_str.c_str());
  
  update_statistics(true);
  ESP_LOGI(TAG, "=== SPI原始命令发送完成 ===");
  return true;
}

bool SpiCommunicationAdapter::is_available() {
  return initialized_ && this->parent_ && this->cs_;
}

bool SpiCommunicationAdapter::is_connected() {
  return is_available();
}

void SpiCommunicationAdapter::flush_buffer() {
  // SPI模式下无需清空缓冲区，这是一个空操作
  ESP_LOGV(TAG, "SPI模式无需清空缓冲区");
}

// 错误处理和统计方法已移至基类

std::string SpiCommunicationAdapter::get_adapter_type() const {
  return "SPI";
}

std::string SpiCommunicationAdapter::get_status_info() const {
  auto stats = get_statistics();
  char buffer[256];
  snprintf(buffer, sizeof(buffer),
           "SPI适配器状态: 初始化=%s, 成功=%zu, 错误=%zu, 超时=%zu, 校验和错误=%zu",
           initialized_ ? "是" : "否",
           stats.success_count,
           stats.error_count,
           stats.timeout_count,
           stats.checksum_error_count);
  return std::string(buffer);
}

bool SpiCommunicationAdapter::self_test() {
  if (!is_available()) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "设备不可用");
    return false;
  }
  
  // 尝试读取温度寄存器作为自检
  bool success = false;
  int32_t temp_value = read_register(TEMPERATURE_ADDR, &success);
  
  if (success && temp_value > 0) {
    ESP_LOGI(TAG, "SPI适配器自检通过，温度值: %d", temp_value);
    return true;
  } else {
    set_error(CommunicationError::HARDWARE_ERROR, "自检失败：无法读取温度寄存器");
    return false;
  }
}

// ========== 内部方法实现 ==========

/**
 * SPI读取命令实现
 * 
 * 【BL0906 SPI读取协议】：
 * 发送：[0x82] [ADDR] [0x00] [0x00] [0x00] [0x00]
 * 接收：[0x82] [ADDR] [DATA_H] [DATA_M] [DATA_L] [CHECKSUM]
 * 
 * 【关键要点】：
 * 1. 使用transfer_array()实现48位连续双向传输
 * 2. 同一数组既用于发送也用于接收（in-place操作）
 * 3. 校验和计算：(CMD + ADDR + DATA_H + DATA_M + DATA_L) ^ 0xFF
 * 4. 前2字节是命令和地址的回显，后4字节是有效数据
 * 
 * 【时序要求】：
 * - CS建立时间：10微秒
 * - 数据稳定时间：10微秒
 */
int32_t SpiCommunicationAdapter::send_spi_read_command(uint8_t address, bool* success) {
  // 初始化返回值
  if (success) *success = false;
  
  ESP_LOGV(TAG, "开始SPI读取操作: 地址=0x%02X", address);
  
  // 按照BL0906规格书要求：连续的48位SPI传输
  // ESPHome SPI的transfer_array是in-place操作，同一数组既用于发送也用于接收
  uint8_t spi_data[6] = {0x82, address, 0x00, 0x00, 0x00, 0x00};  // 0x82=SPI读命令
  
  ESP_LOGV(TAG, "发送SPI命令: 0x%02X 0x%02X (连续48位传输)", spi_data[0], spi_data[1]);
  ESP_LOGV(TAG, "SPI发送数据: %02X %02X %02X %02X %02X %02X", 
           spi_data[0], spi_data[1], spi_data[2], spi_data[3], spi_data[4], spi_data[5]);
  
  // 执行连续的48位SPI传输
  bool spi_success = safe_spi_operation([this, &spi_data]() {
    // 【关键】：使用继承的enable()方法，拉低CS引脚开始传输
    this->enable();
    // 短暂延时确保CS稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    
    // 【关键】：使用transfer_array实现真正的双向同步传输（连续48位）
    // ESPHome的transfer_array是in-place操作：发送spi_data，结果覆盖到spi_data
    // 这是BL0906 SPI协议的核心：48位连续双向传输
    this->transfer_array(spi_data, 6);
    
    // 添加延时确保数据稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    // 【关键】：使用继承的disable()方法，拉高CS引脚结束传输
    this->disable();
  });
  
  ESP_LOGV(TAG, "SPI操作结果: %s", spi_success ? "成功" : "失败");
  ESP_LOGV(TAG, "SPI完整接收数据: %02X %02X %02X %02X %02X %02X", 
           spi_data[0], spi_data[1], spi_data[2], spi_data[3], spi_data[4], spi_data[5]);
  
  if (!spi_success) {
    set_error(CommunicationError::HARDWARE_ERROR, "SPI操作异常，地址: 0x" + format_hex(address));
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return 0;
  }
  
  // BL0906 SPI响应格式：Data_H Data_M Data_L SUM（无命令和地址回显）
  uint8_t data_h = spi_data[2];
  uint8_t data_m = spi_data[3];
  uint8_t data_l = spi_data[4];
  uint8_t checksum = spi_data[5];
  
  // 校验和计算：(0x82 + address + data_h + data_m + data_l) ^ 0xFF
  uint8_t expected_checksum = (0x82 + address + data_h + data_m + data_l) ^ 0xFF;
  if (checksum != expected_checksum) {
    set_error(CommunicationError::CHECKSUM_ERROR,
              "SPI校验和错误 地址:0x" + format_hex(address) + 
              " 期望:0x" + format_hex(expected_checksum) + " 实际:0x" + format_hex(checksum));
    if (success) *success = false;
    update_statistics(false, CommunicationError::CHECKSUM_ERROR);
    return 0;
  }

  // 使用基类的统一数据解析方法
  int32_t result_value = parse_register_response(address, data_h, data_m, data_l);

  ESP_LOGV(TAG, "SPI读取寄存器 0x%02X: 原始数据[%02X %02X %02X], 解析值: %d", 
           address, data_h, data_m, data_l, result_value);
  
  if (success) *success = true;
  update_statistics(true);
  return result_value;
}

/**
 * SPI写入命令实现
 * 
 * 【BL0906 SPI写入协议】：
 * 发送：[0x81] [ADDR] [DATA_H] [DATA_M] [DATA_L] [CHECKSUM]
 * 
 * 【关键要点】：
 * 1. 使用write_array()实现48位单向传输（只发送）
 * 2. 16位寄存器：DATA_H=0x00, 使用DATA_M和DATA_L
 * 3. 24位寄存器：使用符号扩展填充DATA_H
 * 4. 校验和计算：(CMD + ADDR + DATA_H + DATA_M + DATA_L) ^ 0xFF
 * 5. 写入后必须读回验证
 * 
 * 【时序要求】：
 * - 写入延时：10毫秒
 * - 验证读取：立即进行
 */
bool SpiCommunicationAdapter::send_spi_write_command(uint8_t address, int16_t value) {
  ESP_LOGI(TAG, "正在写入寄存器 0x%02X 值: %d (SPI)", address, value);
  
  // 使用基类的统一数据准备方法
  uint8_t data_h, data_m, data_l;
  if (!prepare_register_write_data(address, value, data_h, data_m, data_l)) {
    set_error(CommunicationError::INVALID_RESPONSE,
              "SPI写入数据准备失败 地址:0x" + format_hex(address));
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }
  
  // 准备SPI写入命令：[0x81] [ADDR] [DATA_H] [DATA_M] [DATA_L] [CHECKSUM]
  uint8_t tx_data[6];
  tx_data[0] = 0x81;      // SPI写命令
  tx_data[1] = address;   // 寄存器地址
  tx_data[2] = data_h;    // 数据高字节
  tx_data[3] = data_m;    // 数据中字节
  tx_data[4] = data_l;    // 数据低字节
  // 计算校验和：(CMD + ADDR + DATA_H + DATA_M + DATA_L) ^ 0xFF
  tx_data[5] = (tx_data[0] + tx_data[1] + tx_data[2] + tx_data[3] + tx_data[4]) ^ 0xFF;

  ESP_LOGV(TAG, "SPI写入寄存器 0x%02X 值: %d", address, value);
  
  // 执行连续的48位SPI传输
  bool spi_success = safe_spi_operation([this, &tx_data]() {
    // 【关键】：使用继承的enable()方法，拉低CS引脚开始传输
    this->enable();
    // 短暂延时确保CS稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    
    // 【关键】：write_array实现48位单向传输（只发送，不接收）
    // 这是BL0906 SPI写入协议：48位连续发送（6字节）
    this->write_array(tx_data, 6);
    
    // 添加延时确保数据发送完成
    delayMicroseconds(SPI_DELAY_US);
    // 【关键】：使用继承的disable()方法，拉高CS引脚结束传输
    this->disable();
  });
  
  if (!spi_success) {
    set_error(CommunicationError::HARDWARE_ERROR, "SPI写入操作异常，寄存器: 0x" + format_hex(address));
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
  
  ESP_LOGV(TAG, "SPI写入命令: %02X %02X %02X %02X %02X %02X", 
           tx_data[0], tx_data[1], tx_data[2], tx_data[3], tx_data[4], tx_data[5]);
  
  // 延时等待写入完成
  delay(10);  // 增加延时时间
  
  // 验证写入
  bool read_success = false;
  int32_t read_value = read_register(address, &read_success);
  
  if (!read_success) {
    set_error(CommunicationError::INVALID_RESPONSE, "SPI寄存器写入验证读取失败");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }
  
  // 使用基类的统一验证方法
  if (verify_write_operation(address, value)) {
    ESP_LOGI(TAG, "SPI寄存器 0x%02X 写入成功，值: %d", address, value);
    update_statistics(true);
    return true;
  } else {
    // 错误信息已在verify_write_operation中设置
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
}

// 注意：原有的辅助函数已被基类的统一方法替代：
// - 数据准备和解析逻辑统一到基类
// - 校验和计算内联实现
// - 错误处理、统计和重试方法已移至基类

bool SpiCommunicationAdapter::safe_spi_operation(std::function<void()> operation) {
  // ESP平台不支持异常处理，直接执行操作
  operation();
  return true;
}

} // namespace bl0906_factory
} // namespace esphome

/*
 * ==================== SPI通信修改警告 ====================
 * 
 * 如果SPI通信出现问题，请检查：
 * 
 * 1. 【编译错误】：
 *    - 确认继承spi::SPIDevice而不是组合
 *    - 使用this->parent_和this->cs_，不要自定义变量
 *    - 调用this->enable(), this->write_array()等继承方法
 * 
 * 2. 【通信失败】：
 *    - 检查CS引脚配置和时序
 *    - 确认SPI模式：MSB_FIRST, POLARITY_LOW, PHASE_TRAILING
 *    - 验证波特率：1MHz
 * 
 * 3. 【数据错误】：
 *    - 检查命令字节：读=0x82, 写=0x81
 *    - 验证校验和计算
 *    - 确认48位连续传输
 * 
 * 4. 【参考代码】：
 *    - 重构前的工作代码：tests/components/old/
 *    - ESPHome SPI文档和示例
 * 
 * 【重要】：修改前请备份工作版本！
 * ========================================================
 */

#endif // USE_SPI_COMMUNICATION_ADAPTER