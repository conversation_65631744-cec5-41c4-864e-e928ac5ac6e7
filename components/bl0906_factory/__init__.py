"""BL0906 Factory - 生产版电能计量组件（完整功能）"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, number, i2c, spi
from esphome import pins
from esphome.const import (
    CONF_ID,
    CONF_UPDATE_INTERVAL
)

# 根据配置动态添加依赖
def _get_dependencies(config):
    deps = []
    if config and CONF_COMMUNICATION in config:
        comm_mode = config[CONF_COMMUNICATION]
        if comm_mode == "uart":
            deps.append("uart")
        elif comm_mode == "spi":
            deps.append("spi")
    return deps

DEPENDENCIES = []  # 基础依赖为空，会在配置验证时动态添加
CODEOWNERS = ["@carrot8848"]
AUTO_LOAD = ["sensor", "number"]  # 生产版包含number组件
MULTI_CONF = True

# 现代化命名空间定义
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Factory = bl0906_factory_ns.class_("BL0906Factory", cg.PollingComponent)
BL0906Number = bl0906_factory_ns.class_("BL0906Number", number.Number, cg.Component)

# 通信适配器类声明
CommunicationAdapterInterface = bl0906_factory_ns.class_("CommunicationAdapterInterface")
UartCommunicationAdapter = bl0906_factory_ns.class_("UartCommunicationAdapter", CommunicationAdapterInterface, uart.UARTDevice)
SpiCommunicationAdapter = bl0906_factory_ns.class_("SpiCommunicationAdapter", CommunicationAdapterInterface, spi.SPIDevice)

# 现代化枚举定义
CalibRegType = bl0906_factory_ns.enum("CalibRegType")
SensorType = bl0906_factory_ns.enum("SensorType", is_class=True)
CalibNumberType = bl0906_factory_ns.enum("CalibNumberType", is_class=True)
StatisticsSensorType = bl0906_factory_ns.enum("StatisticsSensorType", is_class=True)
EEPROMType = bl0906_factory_ns.enum("EEPROMType", is_class=True)
FreqAdaptMode = bl0906_factory_ns.enum("FreqAdaptMode", is_class=True)

# 导入统一配置映射模块
from .config_mappings import (
    COMMUNICATION_MODES,
    STORAGE_TYPES,
    EEPROM_TYPES,
    FREQ_ADAPT_MODES,
    VOLTAGE_SAMPLING_MODES,
    CHIP_MODELS,
    ConfigValidator,
    SchemaBuilder,
)

# 现代化常量定义
CONF_BL0906_FACTORY_ID = "bl0906_factory_id"
CONF_CHIP_MODEL = "chip_model"
CONF_COMMUNICATION = "communication"
CONF_CALIBRATION = "calibration"
CONF_CALIBRATION_MODE = "calibration_mode"
CONF_INITIAL_CALIBRATION = "initial_calibration"
CONF_REGISTER = "register"
CONF_VALUE = "value"
CONF_STORAGE_TYPE = "storage_type"
CONF_EEPROM_TYPE = "eeprom_type"
CONF_INSTANCE_ID = "instance_id"
CONF_UART_ID = "uart_id"
CONF_SPI_ID = "spi_id"
CONF_CS_PIN = "cs_pin"
CONF_FREQ_ADAPT = "freq_adapt"
CONF_VOLTAGE_SAMPLING_MODE = "voltage_sampling_mode"
CONF_ENERGY_STATISTICS = "energy_statistics"
CONF_TIME_ID = "time_id"

# 使用统一的配置模式
INITIAL_CALIBRATION_SCHEMA = SchemaBuilder.get_initial_calibration_schema()
CALIBRATION_SCHEMA = SchemaBuilder.get_calibration_schema()

# ✅ 直接使用验证器方法，避免包装函数反模式
# 在CONFIG_SCHEMA中直接使用 ConfigValidator.validate_communication_config

def validate_chip_configuration(config):
    """验证芯片配置的合理性"""
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    chip_info = CHIP_MODELS[chip_model]
    max_channels = chip_info["max_channels"]
    
    # 检查所有传感器配置是否在芯片支持范围内
    def check_sensors(sensor_list, sensor_type):
        if sensor_list:
            for sensor_config in sensor_list:
                channel = sensor_config.get("channel")
                if channel and channel > max_channels:
                    raise cv.Invalid(
                        f"{sensor_type} channel {channel} exceeds {chip_model} "
                        f"maximum channels ({max_channels})"
                    )
    
    # 注意：这里暂时不验证传感器配置，因为传感器配置在sensor.py中处理
    # 实际的通道验证会在传感器配置时进行
    
    return config

def validate_energy_statistics_config(config):
    """验证电量统计配置的完整性"""
    energy_statistics_enabled = config.get(CONF_ENERGY_STATISTICS, False)
    time_id_configured = CONF_TIME_ID in config
    
    # 如果启用了电量统计功能，必须配置时间组件
    if energy_statistics_enabled and not time_id_configured:
        raise cv.Invalid(
            f"When '{CONF_ENERGY_STATISTICS}' is set to true, "
            f"'{CONF_TIME_ID}' must be configured. "
            f"Energy statistics require a time component to track daily, weekly, "
            f"monthly and yearly energy consumption."
        )
    
    return config

# 基础配置模式（包含I2C可选项）
BASE_CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Optional(CONF_CHIP_MODEL, default="bl0906"): cv.enum(CHIP_MODELS, lower=True),
    cv.Required(CONF_COMMUNICATION): cv.enum(COMMUNICATION_MODES, lower=True),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Optional(CONF_CALIBRATION): CALIBRATION_SCHEMA,
    cv.Optional(CONF_CALIBRATION_MODE, default=False): cv.boolean,
    cv.Optional(CONF_INITIAL_CALIBRATION): cv.ensure_list(INITIAL_CALIBRATION_SCHEMA),
    cv.Required(CONF_INSTANCE_ID): cv.hex_uint32_t,
    cv.Optional(CONF_FREQ_ADAPT, default="off"): cv.enum(FREQ_ADAPT_MODES, lower=True),
    cv.Optional(CONF_VOLTAGE_SAMPLING_MODE, default="transformer"): cv.enum(VOLTAGE_SAMPLING_MODES, lower=True),
    # 电量统计功能配置
    cv.Optional(CONF_ENERGY_STATISTICS, default=False): cv.boolean,
    cv.Optional(CONF_TIME_ID): cv.use_id("time"),
    # UART模式配置
    cv.Optional(CONF_UART_ID): cv.use_id(uart.UARTComponent),
    # SPI模式配置
    cv.Optional(CONF_SPI_ID): cv.use_id(spi.SPIComponent),
    cv.Optional(CONF_CS_PIN): pins.gpio_output_pin_schema,
    # I2C模式配置（用于EEPROM存储）
    cv.Optional("i2c_id"): cv.use_id(i2c.I2CBus),
    cv.Optional("address"): cv.i2c_address,
    # CHGN批量修改配置
    cv.Optional("chgn_reference_sensor"): cv.use_id("sensor"),
}).extend(cv.polling_component_schema("60s"))

CONFIG_SCHEMA = cv.All(
    BASE_CONFIG_SCHEMA,
    ConfigValidator.validate_communication_config,
    ConfigValidator.validate_i2c_dependency,
    validate_chip_configuration,
    validate_energy_statistics_config
)

FINAL_VALIDATE_SCHEMA = ConfigValidator.validate_i2c_dependency

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)

    # 添加生产版标识
    cg.add_define("USE_BL0906_FACTORY")
    cg.add_define("BL0906_DEVELOPMENT_BUILD")
    
    # 设置运行时芯片型号（不再生成宏定义）
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    chip_enum_map = {
        "bl0906": "esphome::bl0906_factory::ChipModel::BL0906",
        "bl0910": "esphome::bl0906_factory::ChipModel::BL0910"
    }
    cg.add(var.set_chip_model(cg.RawExpression(chip_enum_map[chip_model])))

    # 首先根据通讯方式添加编译宏（必须在其他代码之前）
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "spi":
        # 添加SPI适配器编译宏
        cg.add_build_flag("-DUSE_SPI_COMMUNICATION_ADAPTER")
        # 确保SPI依赖被添加
        cg.add_library("SPI", None)
    elif comm_mode == "uart":
        # 添加UART适配器编译宏
        cg.add_build_flag("-DUSE_UART_COMMUNICATION_ADAPTER")
    
    # 根据通讯方式创建和配置适配器
    if comm_mode == "spi":
        
        # 获取SPI组件和CS引脚
        spi_component = await cg.get_variable(config[CONF_SPI_ID])
        cs_pin = await cg.gpio_pin_expression(config[CONF_CS_PIN])
        
        # 创建SPI适配器并配置（使用条件编译保护）
        cg.add(cg.RawExpression(f"""
        #ifdef USE_SPI_COMMUNICATION_ADAPTER
        {{
            auto spi_adapter_impl = new esphome::bl0906_factory::SpiCommunicationAdapter();
            spi_adapter_impl->set_spi_parent({spi_component});
            spi_adapter_impl->set_cs_pin({cs_pin});
            auto spi_adapter = std::unique_ptr<esphome::bl0906_factory::CommunicationAdapterInterface>(spi_adapter_impl);
            {var}->set_communication_adapter(std::move(spi_adapter));
        }}
        #else
        #error "SPI communication adapter is not enabled. Please check your configuration."
        #endif
        """))
        
        # SPI适配器会自己处理SPI设备的初始化
        
    elif comm_mode == "uart":
        # 获取UART组件
        uart_component = await cg.get_variable(config[CONF_UART_ID])
        
        # 创建UART适配器并配置（使用条件编译保护）
        cg.add(cg.RawExpression(f"""
        #ifdef USE_UART_COMMUNICATION_ADAPTER
        {{
            auto uart_adapter_impl = new esphome::bl0906_factory::UartCommunicationAdapter({uart_component});
            auto uart_adapter = std::unique_ptr<esphome::bl0906_factory::CommunicationAdapterInterface>(uart_adapter_impl);
            {var}->set_communication_adapter(std::move(uart_adapter));
        }}
        #else
        #error "UART communication adapter is not enabled. Please check your configuration."
        #endif
        """))
        
        # UART适配器会自己处理UART设备注册

    # 处理校准模式
    if config.get(CONF_CALIBRATION_MODE, False):
        cg.add_define("BL0906_CALIBRATION_MODE")
    
    # 处理存储类型
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        storage_type = calib_config.get(CONF_STORAGE_TYPE, "preference")
        
        cg.add(var.set_storage_type(storage_type))
        
        if storage_type == "eeprom":
            # 只有在使用EEPROM存储时才定义I2C EEPROM宏
            cg.add_build_flag("-DUSE_I2C_EEPROM_CALIBRATION")

            # 注册为I2C设备（使用条件编译保护）
            if "i2c_id" in config and "address" in config:
                # 获取I2C组件并设置
                i2c_component = await cg.get_variable(config["i2c_id"])

                # 使用条件编译保护I2C EEPROM相关的方法调用
                cg.add(cg.RawExpression(f"""
                #ifdef USE_I2C_EEPROM_CALIBRATION
                {var}->set_i2c_parent({i2c_component});
                {var}->set_i2c_address({config["address"]});
                #else
                #error "I2C EEPROM calibration storage is not enabled. Please check your configuration."
                #endif
                """))

                eeprom_type = calib_config.get(CONF_EEPROM_TYPE, "24c02")
                eeprom_enum_map = {
                    "24c02": "esphome::bl0906_factory::EEPROMType::TYPE_24C02",
                    "24c04": "esphome::bl0906_factory::EEPROMType::TYPE_24C04",
                    "24c08": "esphome::bl0906_factory::EEPROMType::TYPE_24C08",
                    "24c16": "esphome::bl0906_factory::EEPROMType::TYPE_24C16"
                }

                # 使用条件编译保护EEPROM类型设置
                cg.add(cg.RawExpression(f"""
                #ifdef USE_I2C_EEPROM_CALIBRATION
                {var}->set_eeprom_type({eeprom_enum_map[eeprom_type]});
                #endif
                """))

                # 添加I2C依赖
                cg.add_library("Wire", None)
            else:
                raise ValueError("EEPROM storage requires i2c_id and address configuration")
        else:
            # preference存储不需要额外的宏定义，使用默认的ESPHome preferences
            pass
    else:
        # 如果没有配置calibration，默认使用preference存储
        cg.add(var.set_storage_type("preference"))
    
    # 处理实例ID（现在是必填项）
    instance_id = config[CONF_INSTANCE_ID]
    cg.add(var.set_instance_id(instance_id))
    
    # 处理频率适配模式配置
    freq_adapt_mode = config[CONF_FREQ_ADAPT]
    freq_adapt_enum_map = {
        "off": "esphome::bl0906_factory::BL0906Factory::FreqAdaptMode::OFF",
        "auto": "esphome::bl0906_factory::BL0906Factory::FreqAdaptMode::AUTO",
        "60": "esphome::bl0906_factory::BL0906Factory::FreqAdaptMode::HZ60"
    }
    cg.add(var.set_freq_adapt_mode(cg.RawExpression(freq_adapt_enum_map[freq_adapt_mode])))
    
    # 处理电压采样模式配置 - 生成条件编译宏
    voltage_sampling_mode = config[CONF_VOLTAGE_SAMPLING_MODE]
    if voltage_sampling_mode == "transformer":
        cg.add_build_flag("-DBL0906_VOLTAGE_SAMPLING_TRANSFORMER")
    elif voltage_sampling_mode == "resistor_divider":
        cg.add_build_flag("-DBL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER")
    
    # 处理CHGN参考传感器配置
    if "chgn_reference_sensor" in config:
        reference_sensor = await cg.get_variable(config["chgn_reference_sensor"])
        cg.add(var.set_chgn_reference_sensor(reference_sensor))
    
    # 处理初始校准值
    if CONF_INITIAL_CALIBRATION in config:
        # 首先收集所有的初始值到一个字典
        initial_values = {}
        for item in config[CONF_INITIAL_CALIBRATION]:
            register = item[CONF_REGISTER]
            value = item[CONF_VALUE]
            initial_values[register] = value
        
        # 然后通过代码生成设置这些值
        for register, value in initial_values.items():
            cg.add(cg.RawExpression(f"{var}->initial_calibration_values_[0x{register:02X}] = {value}"))

    # 处理电量统计功能配置
    energy_statistics_enabled = config.get(CONF_ENERGY_STATISTICS, False)
    cg.add(var.set_energy_statistics_enabled(energy_statistics_enabled))
    
    # 处理时间组件配置（用于电量统计功能）
    if CONF_TIME_ID in config:
        time_component = await cg.get_variable(config[CONF_TIME_ID])
        cg.add(var.set_time_component(time_component))

    # 简化的校准配置处理
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        if calib_config.get("enabled", True):
            cg.add_define("BL0906_CALIBRATION_ENABLED")

# 现代化平台模式定义
PLATFORM_SCHEMA = cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
}) 