import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import number

# 从统一配置映射模块导入
from .config_mappings import (
    DEFAULT_CHANNEL_COUNT,
    CHIP_MODELS,
    CALIB_TYPES,
    NUMBER_CONFIGS,
    get_calib_type_enum_expression,
)
# ✅ 已移除 get_calib_register_expression - 避免无意义包装函数
from . import BL0906Factory, BL0906Number, CONF_BL0906_FACTORY_ID, CONF_CHIP_MODEL

DEPENDENCIES = ["bl0906_factory"]

# 使用统一配置映射构建配置模式
def build_number_config_schema():
    """动态构建Number配置模式"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加所有Number配置
    for key, config in NUMBER_CONFIGS.items():
        schema_dict[cv.Optional(key)] = number.number_schema(
            class_=BL0906Number,
            icon=config["icon"],
            unit_of_measurement=config["unit"],
        )

    return cv.Schema(schema_dict)

CONFIG_SCHEMA = build_number_config_schema()

async def to_code(config):
    bl0906_var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])
    
    # 添加条件编译保护，确保Number组件只在校准模式下才被注册
    cg.add(cg.RawExpression("""
    #ifndef BL0906_CALIBRATION_MODE
    #error "Number组件只能在校准模式下使用。请在bl0906_factory组件中设置 calibration_mode: true"
    #endif
    """))
    
    # 使用最大可能的通道数，让C++层面根据实际芯片型号进行验证
    # 这样可以支持所有芯片型号的配置，而无需在Python层面获取芯片型号信息
    max_channels = max(chip_info["max_channels"] for chip_info in CHIP_MODELS.values())

    # 使用统一配置映射进行Number注册，支持所有可能的通道（C++层面会进行芯片型号验证）
    for number_key, number_config in NUMBER_CONFIGS.items():
        if number_key in config:
            channel = number_config["channel"]
            conf = config[number_key]
            num = await number.new_number(
                conf,
                min_value=number_config["min_value"],
                max_value=number_config["max_value"],
                step=number_config["step"],
            )

            # 使用统一的枚举生成函数
            calib_type = number_config["type"]
            calib_type_enum = cg.RawExpression(get_calib_type_enum_expression(calib_type))
            
            # 使用现有的set_calib_number方法，C++层面会根据芯片型号验证通道有效性
            # 寄存器地址将通过运行时get_register_addr函数动态计算
            cg.add(bl0906_var.set_calib_number(calib_type_enum, num, channel))