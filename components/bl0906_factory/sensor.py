import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import sensor

# 从统一配置映射模块导入
from .config_mappings import (
    DEFAULT_CHANNEL_COUNT,
    CHIP_MODELS,
    GLOBAL_SENSOR_CONFIGS,
    CHANNEL_SENSOR_TEMPLATES,
    SENSOR_TYPES,
    STATISTICS_SENSOR_TYPES,
    is_statistics_sensor,
    get_sensor_enum_expression,
)
from . import BL0906Factory, CONF_BL0906_FACTORY_ID, CONF_CHIP_MODEL, StatisticsSensorType

DEPENDENCIES = ["bl0906_factory"]

def build_channel_sensor_schema():
    """构建单个通道的传感器配置模式"""
    schema_dict = {}
    for sensor_key, config in CHANNEL_SENSOR_TEMPLATES.items():
        schema_dict[cv.Optional(sensor_key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit"],
            accuracy_decimals=config["accuracy"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )
    return cv.Schema(schema_dict)

def build_config_schema():
    """构建嵌套配置模式，支持动态通道数量"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加全局传感器配置
    for key, config in GLOBAL_SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit"],
            accuracy_decimals=config["accuracy"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )

    # 添加通道配置，支持最大通道数（BL0910的10个通道）
    channel_schema = build_channel_sensor_schema()
    max_channels = max(chip_info["max_channels"] for chip_info in CHIP_MODELS.values())
    for i in range(1, max_channels + 1):
        schema_dict[cv.Optional(f"ch{i}")] = channel_schema

    return cv.Schema(schema_dict)

CONFIG_SCHEMA = build_config_schema()

async def to_code(config):
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])
    
    # 使用最大可能的通道数，让C++层面根据实际芯片型号进行验证
    # 这样可以支持所有芯片型号的配置，而无需在Python层面获取芯片型号信息
    max_channels = max(chip_info["max_channels"] for chip_info in CHIP_MODELS.values())

    # 注册全局传感器
    for sensor_key, sensor_config in GLOBAL_SENSOR_CONFIGS.items():
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            sensor_type = sensor_config["type"]
            
            # 使用统一的枚举生成函数
            is_stats = is_statistics_sensor(sensor_type)
            enum_expr = cg.RawExpression(get_sensor_enum_expression(sensor_type, is_stats))
            
            if is_stats:
                cg.add(var.set_statistics_sensor(enum_expr, sens, 0))
            else:
                cg.add(var.set_sensor(enum_expr, sens, 0))

    # 注册通道传感器，支持所有可能的通道数（C++层面会根据芯片型号进行验证）
    for i in range(1, max_channels + 1):
        channel_key = f"ch{i}"
        if channel_key in config:
            channel_config = config[channel_key]
            channel_index = i - 1  # 内部使用从0开始的索引
            
            for sensor_key, sensor_config in CHANNEL_SENSOR_TEMPLATES.items():
                if sensor_key in channel_config:
                    sens = await sensor.new_sensor(channel_config[sensor_key])
                    sensor_type = sensor_config["type"]
                    
                    # 使用统一的枚举生成函数
                    is_stats = is_statistics_sensor(sensor_type)
                    enum_expr = cg.RawExpression(get_sensor_enum_expression(sensor_type, is_stats))
                    
                    # C++层面的set_sensor和set_statistics_sensor方法会验证通道有效性
                    if is_stats:
                        cg.add(var.set_statistics_sensor(enum_expr, sens, channel_index))
                    else:
                        cg.add(var.set_sensor(enum_expr, sens, channel_index))
