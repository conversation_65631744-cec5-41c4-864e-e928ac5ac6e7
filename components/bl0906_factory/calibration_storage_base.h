#pragma once
#include "calibration_storage_interface.h"
#include "esphome/core/log.h"
#include <algorithm>
#include <cstring>

namespace esphome {
namespace bl0906_factory {

// 存储操作结果
enum class StorageResult {
    SUCCESS,
    INSTANCE_NOT_FOUND,
    STORAGE_FULL,
    INVALID_DATA,
    IO_ERROR,
    VERIFICATION_FAILED
};

// 通用存储基类，抽象重复逻辑
class CalibrationStorageBase : public CalibrationStorageInterface {
public:
    virtual ~CalibrationStorageBase() = default;
    
    // 实现接口中的纯虚函数
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;

protected:
    // 通用的数据验证方法
    bool validate_instance_id(uint32_t instance_id) const;
    bool validate_entries(const std::vector<CalibrationEntry>& entries) const;
    StorageResult validate_entries_count(size_t count, size_t max_count) const;
    
    // 通用的数据序列化/反序列化方法
    size_t serialize_entries(const std::vector<CalibrationEntry>& entries, uint8_t* buffer, size_t buffer_size) const;
    size_t serialize_entries_with_instance_id(uint32_t instance_id, const std::vector<CalibrationEntry>& entries, uint8_t* buffer, size_t buffer_size) const;
    bool deserialize_entries(const uint8_t* buffer, size_t buffer_size, std::vector<CalibrationEntry>& entries) const;
    
    // 通用的错误日志方法
    void log_instance_operation(const char* operation, uint32_t instance_id, bool success, const char* details = nullptr) const;
    void log_storage_error(const char* operation, StorageResult result, const char* details = nullptr) const;
    void log_data_validation_error(const char* field, uint32_t value, uint32_t max_value) const;
    
    // 通用的实例列表操作
    bool add_to_instance_list(std::vector<uint32_t>& instance_list, uint32_t instance_id);
    bool remove_from_instance_list(std::vector<uint32_t>& instance_list, uint32_t instance_id);
    
    // 通用的数据格式检查
    bool check_data_integrity(const uint8_t* data, size_t size) const;
    
    // 子类需要实现的具体存储操作
    virtual StorageResult read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) = 0;
    virtual StorageResult write_raw_data(uint32_t instance_id, const uint8_t* buffer, size_t buffer_size) = 0;
    virtual StorageResult delete_raw_data(uint32_t instance_id) = 0;
    
    // 子类可以重写的配置方法
    virtual size_t get_max_entries_per_instance() const { return 64; }
    virtual const char* get_log_tag() const { return "calibration_storage"; }
    
private:
    // 数据格式常量
    static constexpr size_t ENTRY_SERIALIZED_SIZE = 3; // 1字节地址 + 2字节值
    static constexpr uint32_t MIN_VALID_INSTANCE_ID = 0x00000001;
    static constexpr uint32_t MAX_VALID_INSTANCE_ID = 0xFFFFFFFE;
};

}  // namespace bl0906_factory
}  // namespace esphome 