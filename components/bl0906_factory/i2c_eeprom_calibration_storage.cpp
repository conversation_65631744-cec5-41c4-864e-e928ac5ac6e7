// 只有在启用I2C EEPROM校准存储时才编译此文件
#ifdef USE_I2C_EEPROM_CALIBRATION

#include "i2c_eeprom_calibration_storage.h"
#include "esphome/core/hal.h"

namespace esphome {
namespace bl0906_factory {

// 定义静态成员变量
const char *const I2CEEPROMCalibrationStorage::TAG = "bl0906.i2c_eeprom_storage";

I2CEEPROMCalibrationStorage::I2CEEPROMCalibrationStorage(i2c::I2CBus *i2c, EEPROMType type, uint8_t address)
    : i2c_(i2c), address_(address), eeprom_type_(type) {
    calculate_layout();
}

void I2CEEPROMCalibrationStorage::calculate_layout() {
    // 根据EEPROM型号计算容量和布局
    switch (eeprom_type_) {
        case EEPROMType::TYPE_24C02:
            eeprom_size_ = 256;
            max_instances_ = 1;
            entries_per_instance_ = 77;
            break;
        case EEPROMType::TYPE_24C04:
            eeprom_size_ = 512;
            max_instances_ = 3;
            entries_per_instance_ = 80;
            break;
        case EEPROMType::TYPE_24C08:
            eeprom_size_ = 1024;
            max_instances_ = 6;
            entries_per_instance_ = 82;
            break;
        case EEPROMType::TYPE_24C16:
            eeprom_size_ = 2048;
            max_instances_ = 12;
            entries_per_instance_ = 82;
            break;
    }

    // 验证布局是否合理，防止地址溢出
    size_t header_size = sizeof(EEPROMHeader);
    size_t index_size = max_instances_ * 4;
    size_t instance_data_size = 4 + entries_per_instance_ * 3;
    size_t total_required = header_size + index_size + max_instances_ * instance_data_size;

    if (total_required > eeprom_size_) {
        ESP_LOGE(TAG, "EEPROM布局错误: 需要%d字节 > 容量%d字节", total_required, eeprom_size_);

        // 自动调整entries_per_instance_以适应容量
        size_t available_for_instances = eeprom_size_ - header_size - index_size;
        size_t max_instance_size = available_for_instances / max_instances_;
        entries_per_instance_ = (max_instance_size - 4) / 3;

        ESP_LOGW(TAG, "自动调整每实例条目数为: %d", entries_per_instance_);

        // 重新计算验证
        total_required = header_size + index_size + max_instances_ * (4 + entries_per_instance_ * 3);
    }

    ESP_LOGD(TAG, "EEPROM布局: 容量=%d字节, 最大实例数=%d, 每实例条目数=%d",
             eeprom_size_, max_instances_, entries_per_instance_);
    ESP_LOGD(TAG, "内存分配: 头部=%d, 索引=%d, 实例数据=%d, 总需求=%d",
             header_size, index_size, max_instances_ * (4 + entries_per_instance_ * 3), total_required);
}

bool I2CEEPROMCalibrationStorage::init() {
    ESP_LOGD(TAG, "初始化I2C EEPROM校准存储 (型号: %02X, 地址: 0x%02X)",
             static_cast<uint8_t>(eeprom_type_), address_);

    // 测试I2C连接 - 尝试读取第一个字节
    uint8_t test_data;
    if (!read_bytes(0, &test_data, 1)) {
        ESP_LOGE(TAG, "I2C EEPROM连接测试失败");
        return false;
    }

    ESP_LOGD(TAG, "I2C EEPROM连接测试成功，地址0的值: 0x%02X", test_data);
    
    // 读取头部，如果不存在或无效则初始化
    EEPROMHeader header;
    bool header_valid = false;

    if (read_header(header)) {
        // 验证头部是否有效
        uint32_t expected_magic = get_magic_for_type(eeprom_type_);
        if (header.magic == expected_magic && header.version == 3) {
            header_valid = true;
            ESP_LOGD(TAG, "EEPROM头部有效，魔数: 0x%08X, 版本: %d, 实例数: %d",
                     header.magic, header.version, header.instance_count);
        } else {
            ESP_LOGD(TAG, "EEPROM头部无效，魔数: 0x%08X (期望: 0x%08X), 版本: %d",
                     header.magic, expected_magic, header.version);
        }
    } else {
        ESP_LOGD(TAG, "EEPROM头部读取失败");
    }

    if (!header_valid) {
        ESP_LOGD(TAG, "EEPROM未初始化或头部损坏，创建新头部");

        // 创建新头部
        memset(&header, 0, sizeof(header));
        header.magic = get_magic_for_type(eeprom_type_);
        header.version = 3;
        header.eeprom_type = static_cast<uint8_t>(eeprom_type_);
        header.max_instances = max_instances_;
        header.instance_count = 0;
        header.timestamp = millis();

        if (!write_header(header)) {
            ESP_LOGE(TAG, "写入EEPROM头部失败");
            return false;
        }

        ESP_LOGD(TAG, "新头部创建成功，魔数: 0x%08X", header.magic);
    }
    
    return true;
}

uint32_t I2CEEPROMCalibrationStorage::get_magic_for_type(EEPROMType type) {
    uint32_t base = 0x24C0CA10;  // 修复字面量错误
    return base | static_cast<uint8_t>(type);
}

bool I2CEEPROMCalibrationStorage::read_bytes(uint16_t addr, uint8_t *data, size_t len) {
    if (addr + len > eeprom_size_) {
        ESP_LOGE(TAG, "读取地址超出EEPROM范围: %d + %d > %d", addr, len, eeprom_size_);
        return false;
    }
    
    // 先写入地址，然后读取数据
    if (eeprom_type_ == EEPROMType::TYPE_24C02) {
        // 24C02: 256字节，8位地址，设备地址固定0x50
        uint8_t addr_byte = static_cast<uint8_t>(addr);
        auto err = i2c_->write(address_, &addr_byte, 1, false);  // 不发送STOP
        if (err != i2c::ERROR_OK) {
            ESP_LOGE(TAG, "设置EEPROM地址%d失败，I2C错误: %d", addr, err);
            return false;
        }

        // 添加小延迟确保地址设置生效，特别是对于地址0
        delayMicroseconds(100);

        err = i2c_->read(address_, data, len);
        if (err != i2c::ERROR_OK) {
            ESP_LOGE(TAG, "从EEPROM地址%d读取%d字节失败，I2C错误: %d", addr, len, err);
            return false;
        }
        return true;
    } else if (eeprom_type_ == EEPROMType::TYPE_24C04) {
        // 24C04: 512字节，需要9位地址
        // 第9位通过设备地址的最低位表示：0x50(低256字节) 或 0x51(高256字节)
        uint8_t device_addr = address_ | ((addr >> 8) & 0x01);
        uint8_t mem_addr = static_cast<uint8_t>(addr & 0xFF);

        ESP_LOGV(TAG, "24C04读取: 地址0x%04X -> 设备0x%02X, 内存0x%02X",
                 addr, device_addr, mem_addr);

        auto err = i2c_->write(device_addr, &mem_addr, 1, false);  // 不发送STOP
        if (err != i2c::ERROR_OK) {
            ESP_LOGE(TAG, "设置EEPROM地址%d失败，I2C错误: %d", addr, err);
            return false;
        }

        // 添加小延迟确保地址设置生效
        delayMicroseconds(100);

        err = i2c_->read(device_addr, data, len);
        if (err != i2c::ERROR_OK) {
            ESP_LOGE(TAG, "从EEPROM地址%d读取%d字节失败，I2C错误: %d", addr, len, err);
            return false;
        }
        return true;
    } else {
        // 24C08和24C16: 使用2字节地址
        uint8_t addr_bytes[2] = {static_cast<uint8_t>(addr >> 8), static_cast<uint8_t>(addr & 0xFF)};
        auto err = i2c_->write(address_, addr_bytes, 2, false);  // 不发送STOP
        if (err != i2c::ERROR_OK) {
            ESP_LOGE(TAG, "设置EEPROM地址%d失败", addr);
            return false;
        }

        // 添加小延迟确保地址设置生效
        delayMicroseconds(100);

        err = i2c_->read(address_, data, len);
        if (err != i2c::ERROR_OK) {
            ESP_LOGE(TAG, "从EEPROM地址%d读取%d字节失败", addr, len);
            return false;
        }
        return true;
    }
}

bool I2CEEPROMCalibrationStorage::write_bytes(uint16_t addr, const uint8_t *data, size_t len) {
    if (addr + len > eeprom_size_) {
        ESP_LOGE(TAG, "写入地址超出EEPROM范围: %d + %d > %d", addr, len, eeprom_size_);
        return false;
    }
    
    // 按页写入 - 24C02页大小8字节，24C04/24C08/24C16页大小16字节
    size_t page_size = (eeprom_type_ == EEPROMType::TYPE_24C02) ? 8 : 16;
    size_t offset = 0;
    
    while (offset < len) {
        uint16_t page_addr = addr + offset;
        size_t page_offset = page_addr % page_size;
        size_t write_len = std::min(len - offset, page_size - page_offset);
        
        if (!write_page(page_addr, data + offset, write_len)) {
            return false;
        }
        
        offset += write_len;
        delay(5); // 等待写入完成
    }
    
    return true;
}

bool I2CEEPROMCalibrationStorage::write_page(uint16_t addr, const uint8_t *data, size_t len) {
    // 写入EEPROM时需要先发送地址，然后发送数据
    if (eeprom_type_ == EEPROMType::TYPE_24C02) {
        // 24C02: 256字节，8位地址，设备地址固定0x50
        uint8_t buffer[17];  // 1字节地址 + 16字节数据
        buffer[0] = static_cast<uint8_t>(addr);
        memcpy(buffer + 1, data, len);
        return i2c_->write(address_, buffer, len + 1, true) == i2c::ERROR_OK;
    } else if (eeprom_type_ == EEPROMType::TYPE_24C04) {
        // 24C04: 512字节，需要9位地址
        // 第9位通过设备地址的最低位表示：0x50(低256字节) 或 0x51(高256字节)
        uint8_t device_addr = address_ | ((addr >> 8) & 0x01);
        uint8_t mem_addr = static_cast<uint8_t>(addr & 0xFF);

        ESP_LOGV(TAG, "24C04写入: 地址0x%04X -> 设备0x%02X, 内存0x%02X",
                 addr, device_addr, mem_addr);

        uint8_t buffer[17];  // 1字节地址 + 16字节数据
        buffer[0] = mem_addr;
        memcpy(buffer + 1, data, len);
        return i2c_->write(device_addr, buffer, len + 1, true) == i2c::ERROR_OK;
    } else {
        // 24C08和24C16: 使用2字节地址
        uint8_t buffer[18]; // 2字节地址 + 16字节数据（最大页大小）
        buffer[0] = static_cast<uint8_t>(addr >> 8);
        buffer[1] = static_cast<uint8_t>(addr & 0xFF);
        memcpy(buffer + 2, data, len);
        return i2c_->write(address_, buffer, len + 2, true) == i2c::ERROR_OK;
    }
}

bool I2CEEPROMCalibrationStorage::read_header(EEPROMHeader& header) {
    return read_bytes(0, reinterpret_cast<uint8_t*>(&header), sizeof(header));
}

bool I2CEEPROMCalibrationStorage::write_header(const EEPROMHeader& header) {
    return write_bytes(0, reinterpret_cast<const uint8_t*>(&header), sizeof(header));
}

uint16_t I2CEEPROMCalibrationStorage::get_instance_offset(int instance_index) {
    size_t header_size = sizeof(EEPROMHeader);
    size_t index_size = max_instances_ * 4; // 每个实例ID 4字节
    size_t instance_data_size = 4 + entries_per_instance_ * 3; // 4字节ID + 条目数据

    uint16_t offset = header_size + index_size + instance_index * instance_data_size;

    // 边界检查，防止地址溢出
    if (offset + instance_data_size > eeprom_size_) {
        ESP_LOGE(TAG, "实例%d偏移地址溢出: %d + %d > %d",
                 instance_index, offset, instance_data_size, eeprom_size_);
        return 0; // 返回无效偏移
    }

    ESP_LOGV(TAG, "实例%d偏移地址: 0x%04X (数据大小: %d)",
             instance_index, offset, instance_data_size);

    return offset;
}

int I2CEEPROMCalibrationStorage::find_instance_index(uint32_t instance_id) {
    size_t header_size = sizeof(EEPROMHeader);
    
    for (size_t i = 0; i < max_instances_; i++) {
        uint32_t stored_id;
        if (!read_bytes(header_size + i * 4, reinterpret_cast<uint8_t*>(&stored_id), 4)) {
            return -1;
        }
        
        if (stored_id == instance_id) {
            return i;
        }
        if (stored_id == 0) {
            return -1; // 空槽位
        }
    }
    
    return -1;
}

// 实现基类的原始数据读取接口
StorageResult I2CEEPROMCalibrationStorage::read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) {
    int index = find_instance_index(instance_id);
    if (index < 0) {
        return StorageResult::INSTANCE_NOT_FOUND;
    }

    uint16_t offset = get_instance_offset(index);
    if (offset == 0) {
        return StorageResult::IO_ERROR;
    }

    // 统一格式：返回 [实例ID(4字节)] + [条目数据]
    // 基类现在期望这种统一格式

    size_t eeprom_data_size = 4 + entries_per_instance_ * 3; // 4字节实例ID + 条目数据

    if (buffer_size < eeprom_data_size) {
        ESP_LOGE(TAG, "缓冲区太小: 需要%d字节, 提供%d字节", eeprom_data_size, buffer_size);
        return StorageResult::INVALID_DATA;
    }

    if (!read_bytes(offset, buffer, eeprom_data_size)) {
        return StorageResult::IO_ERROR;
    }

    // 验证实例ID是否匹配
    uint32_t stored_instance_id;
    memcpy(&stored_instance_id, buffer, 4);
    if (stored_instance_id != instance_id) {
        ESP_LOGE(TAG, "实例ID不匹配: 期望0x%08X, 实际0x%08X", instance_id, stored_instance_id);
        return StorageResult::INVALID_DATA;
    }

    // 返回完整数据（包含实例ID）
    buffer_size = eeprom_data_size;

    return StorageResult::SUCCESS;
}

StorageResult I2CEEPROMCalibrationStorage::write_raw_data(uint32_t instance_id, const uint8_t* buffer, size_t buffer_size) {
    // 查找现有实例或空槽位
    int index = find_instance_index(instance_id);
    ESP_LOGD(TAG, "查找实例 0x%08X，索引: %d", instance_id, index);

    if (index < 0) {
        // 查找空槽位
        EEPROMHeader header;
        if (!read_header(header)) {
            ESP_LOGE(TAG, "读取EEPROM头部失败");
            return StorageResult::IO_ERROR;
        }

        ESP_LOGD(TAG, "当前头部信息: magic=0x%08X, version=%d, instance_count=%d, max_instances=%d",
                 header.magic, header.version, header.instance_count, header.max_instances);

        if (header.instance_count >= max_instances_) {
            ESP_LOGE(TAG, "存储空间已满: %d >= %d", header.instance_count, max_instances_);
            return StorageResult::STORAGE_FULL;
        }

        index = header.instance_count;
        header.instance_count++;

        ESP_LOGD(TAG, "分配新索引 %d 给实例 0x%08X", index, instance_id);

        // 写入实例ID到索引区
        size_t header_size = sizeof(EEPROMHeader);
        uint16_t index_addr = header_size + index * 4;
        ESP_LOGD(TAG, "写入实例ID到地址 0x%04X", index_addr);

        if (!write_bytes(index_addr, reinterpret_cast<const uint8_t*>(&instance_id), 4)) {
            ESP_LOGE(TAG, "写入实例ID到索引区失败");
            return StorageResult::IO_ERROR;
        }

        // 更新头部
        header.timestamp = millis();
        ESP_LOGD(TAG, "更新头部，新的实例数量: %d", header.instance_count);

        if (!write_header(header)) {
            ESP_LOGE(TAG, "更新EEPROM头部失败");
            return StorageResult::IO_ERROR;
        }

        ESP_LOGD(TAG, "成功创建新实例，索引: %d", index);
    } else {
        ESP_LOGD(TAG, "找到现有实例，索引: %d", index);
    }
    
    // 写入到EEPROM
    uint16_t offset = get_instance_offset(index);
    if (offset == 0) {
        return StorageResult::IO_ERROR;
    }

    // EEPROM存储格式：[实例ID(4字节)] + [条目数据]
    // 基类现在传递统一格式：[条目数据]（不包含条目数量）

    size_t total_data_size = 4 + buffer_size; // 4字节实例ID + 条目数据
    size_t max_data_size = 4 + entries_per_instance_ * 3; // 最大允许的数据大小

    if (total_data_size > max_data_size) {
        ESP_LOGE(TAG, "数据太大: %d字节 > %d字节", total_data_size, max_data_size);
        return StorageResult::INVALID_DATA;
    }

    // 准备写入缓冲区：先写实例ID，再写条目数据
    uint8_t write_buffer[256];
    memset(write_buffer, 0, sizeof(write_buffer));

    // 写入实例ID到缓冲区开头
    memcpy(write_buffer, &instance_id, 4);

    // 直接写入条目数据
    memcpy(write_buffer + 4, buffer, buffer_size);

    // 如果数据小于最大大小，剩余部分已经被清零
    size_t write_size = std::max(total_data_size, max_data_size);

    ESP_LOGD(TAG, "写入EEPROM: 实例ID=0x%08X, 条目数据大小=%d, 总写入大小=%d",
             instance_id, buffer_size, write_size);

    if (!write_bytes(offset, write_buffer, write_size)) {
        return StorageResult::IO_ERROR;
    }
    
    return StorageResult::SUCCESS;
}

StorageResult I2CEEPROMCalibrationStorage::delete_raw_data(uint32_t instance_id) {
    int index = find_instance_index(instance_id);
    if (index < 0) {
        return StorageResult::INSTANCE_NOT_FOUND;
    }

    // 清除实例数据
    uint16_t offset = get_instance_offset(index);
    if (offset == 0) {
        return StorageResult::IO_ERROR;
    }

    size_t clear_size = 4 + entries_per_instance_ * 3;
    
    uint8_t zero_data[256] = {0};
    if (!write_bytes(offset, zero_data, clear_size)) {
        return StorageResult::IO_ERROR;
    }
    
    // 清除索引区的实例ID
    size_t header_size = sizeof(EEPROMHeader);
    uint32_t zero_id = 0;
    if (!write_bytes(header_size + index * 4, reinterpret_cast<const uint8_t*>(&zero_id), 4)) {
        return StorageResult::IO_ERROR;
    }
    
    return StorageResult::SUCCESS;
}

bool I2CEEPROMCalibrationStorage::verify() {
    EEPROMHeader header;
    if (!read_header(header)) {
        return false;
    }
    
    // 验证魔数和版本
    uint32_t expected_magic = get_magic_for_type(eeprom_type_);
    if (header.magic != expected_magic) {
        ESP_LOGE(TAG, "EEPROM魔数不匹配: 0x%08X != 0x%08X", header.magic, expected_magic);
        return false;
    }
    
    if (header.version != 3) {
        ESP_LOGE(TAG, "EEPROM版本不匹配: %d != 3", header.version);
        return false;
    }
    
    return true;
}

bool I2CEEPROMCalibrationStorage::erase() {
    // 清除整个EEPROM
    uint8_t zero_data[64] = {0};
    
    for (size_t addr = 0; addr < eeprom_size_; addr += sizeof(zero_data)) {
        size_t write_len = std::min(sizeof(zero_data), eeprom_size_ - addr);
        if (!write_bytes(addr, zero_data, write_len)) {
            return false;
        }
    }
    
    ESP_LOGD(TAG, "清除EEPROM完成");
    return init(); // 重新初始化
}

std::vector<uint32_t> I2CEEPROMCalibrationStorage::get_instance_list() {
    std::vector<uint32_t> instances;
    size_t header_size = sizeof(EEPROMHeader);
    
    for (size_t i = 0; i < max_instances_; i++) {
        uint32_t instance_id;
        if (read_bytes(header_size + i * 4, reinterpret_cast<uint8_t*>(&instance_id), 4)) {
            if (instance_id != 0) {
                instances.push_back(instance_id);
            }
        }
    }
    
    return instances;
}

size_t I2CEEPROMCalibrationStorage::get_max_instances() {
    return max_instances_;
}

uint16_t I2CEEPROMCalibrationStorage::calculate_crc(const uint8_t* data, size_t len) {
    uint16_t crc = 0xFFFF;
    for (size_t i = 0; i < len; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

}  // namespace bl0906_factory
}  // namespace esphome

#endif  // USE_I2C_EEPROM_CALIBRATION
 