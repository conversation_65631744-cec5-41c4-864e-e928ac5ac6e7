# BL0906 校准数据持久化存储说明文档

## 目录
1. [系统概述](#系统概述)
2. [存储架构](#存储架构)
3. [数据格式规范](#数据格式规范)
4. [存储实现类型](#存储实现类型)
5. [电量数据持久化](#电量数据持久化)
6. [配置与使用](#配置与使用)
7. [故障排除](#故障排除)
8. [最佳实践](#最佳实践)

## 系统概述

BL0906 校准数据持久化存储系统提供了完整的校准数据和电量数据存储解决方案，支持多种存储后端，确保数据在设备重启后能够正确恢复。

### 主要特性

- **多存储后端支持**：支持 ESP32 NVS (Preferences) 和 I2C EEPROM 存储
- **多实例管理**：支持多个设备实例的独立数据存储
- **数据完整性保护**：内置 CRC 校验和数据验证机制
- **自动故障恢复**：检测并修复损坏的持久化数据
- **统一接口设计**：抽象存储实现细节，提供一致的操作接口

## 存储架构

### 接口层次结构

```
CalibrationStorageInterface (抽象接口)
    ├── CalibrationStorageBase (通用基类)
    │   ├── PreferenceCalibrationStorage (NVS存储)
    │   └── I2CEEPROMCalibrationStorage (EEPROM存储)
```

### 核心接口定义

```cpp
class CalibrationStorageInterface {
public:
    virtual bool init() = 0;
    virtual bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) = 0;
    virtual bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) = 0;
    virtual bool delete_instance(uint32_t instance_id) = 0;
    virtual bool verify() = 0;
    virtual bool erase() = 0;
    
    virtual std::vector<uint32_t> get_instance_list() = 0;
    virtual size_t get_max_instances() = 0;
    virtual std::string get_storage_type() const = 0;
};
```

## 数据格式规范

### 校准条目结构

```cpp
struct CalibrationEntry {
    uint8_t register_addr;  // 寄存器地址
    int16_t value;          // 校准值 (有符号16位)
} __attribute__((packed));
```

### 实例ID规范

- **有效范围**：`0x00000001` 到 `0xFFFFFFFE`
- **特殊值**：`0x00000000` 和 `0xFFFFFFFF` 为无效值
- **建议格式**：`0x906BXXXX` (BL0906) 或 `0x910CXXXX` (BL0910CA)

### 数据序列化格式

每个校准条目序列化为 3 字节：
- 字节 0：寄存器地址 (8位)
- 字节 1：校准值高字节 (8位)
- 字节 2：校准值低字节 (8位)

### 24位寄存器存储机制

#### 寄存器类型说明

BL0906/BL0910 芯片中的寄存器按位宽分为两类：
- **16位校准寄存器**：CHGN、CHOS、RMSGN、WATTGN、WATTOS 等
- **24位校准寄存器**：RMSOS（有效值偏置）等

```cpp
// 寄存器类型判断
inline bool is_16bit_register(uint8_t address) {
  return (address >= 0x6C && address <= 0x80) ||  // RMSGN/RMSOS 范围
         (address >= 0xA0 && address <= 0xC9) ||  // CHGN/CHOS/WATTGN/WATTOS 范围
         (address == 0xAA) || (address == 0xB5);  // 电压校准寄存器
}

inline bool is_24bit_register(uint8_t address) {
  return address == MODE2_ADDR;  // 只有MODE2是真正的24位寄存器
}
```

#### 信息丢失分析

**⚠️ 重要提示：24位寄存器转换为16位存储时存在信息丢失**

**数值范围对比：**
```
24位有符号整数: -8,388,608 到 +8,388,607  (范围：2^24)
16位有符号整数: -32,768 到 +32,767       (范围：2^16)

信息丢失程度：
- 高8位信息完全丢失
- 数值范围缩减256倍 (2^8 = 256)
- 精度损失：约99.6%的数值范围无法表示
```

**符号扩展机制：**

当16位值写入24位寄存器时，系统自动进行符号扩展：

```cpp
// UART适配器中的符号扩展
int32_t value_24bit = ((int32_t)(value << 8)) >> 8;  // 符号扩展技巧

// SPI适配器中的符号扩展  
int32_t extended_value = (static_cast<int32_t>(value) << 8) >> 8;

// 符号扩展示例：
// 16位值 -1000 (0xFC18) → 24位: 0xFFFC18 (-1000)
// 16位值 +1000 (0x03E8) → 24位: 0x0003E8 (+1000)
```

**范围限制处理：**

```cpp
// 校准值自动截断到16位范围
if (rmsos_value_temp > 32767) {
    rmsos_value_temp = 32767;
} else if (rmsos_value_temp < -32768) {
    rmsos_value_temp = -32768;
}
```

#### 设计权衡分析

**优势：**
- **存储效率**：每个校准条目节省1字节（从4字节减少到3字节）
- **统一格式**：所有校准寄存器使用相同的16位存储格式
- **实用性**：16位范围（±32,767）满足大多数校准应用需求
- **简化实现**：统一的序列化/反序列化逻辑

**劣势：**
- **信息丢失**：高8位信息永久丢失
- **范围限制**：极端校准值（超过±32,767）无法存储
- **数据不一致**：存储值可能与芯片实际值不同
- **调试困难**：需要直接读取芯片寄存器获取真实值

#### 验证机制

系统包含写入验证机制来检测数值截断问题：

```cpp
// 写入后验证
int32_t read_value = send_read_command_and_receive(address, &read_success);
if (read_value == value) {
    ESP_LOGI(TAG, "寄存器写入验证成功");
} else {
    ESP_LOGE(TAG, "寄存器写入验证失败: 写入=%d, 读回=%d", value, read_value);
    // 可能是因为值超出16位范围被截断
}
```

#### 使用建议

1. **监控校准范围**：确保校准值在 -32,768 到 +32,767 范围内
2. **添加边界检查**：在设置校准值前检查是否超出16位范围
3. **使用芯片读取**：对于关键校准参数，直接从芯片读取验证
4. **文档说明**：向用户明确说明此限制

```yaml
# YAML配置示例 - 注意值的范围限制
bl0906_factory:
  initial_calibration:
    - register: 0x78  # RMSOS_1 (24位寄存器)
      value: 1000     # ✅ 在16位范围内
    - register: 0x79  # RMSOS_2  
      value: -30000   # ✅ 在16位范围内
    # value: 100000   # ❌ 超出16位范围，将被截断为32767
```

## 存储实现类型

### 1. NVS 存储 (PreferenceCalibrationStorage)

**适用场景**：ESP32 内置 Flash 存储，适合大多数应用场景

**特点**：
- 存储容量：支持最多 16 个实例
- 数据持久性：断电保持
- 写入寿命：Flash 擦写次数限制 (~10,000 次)
- 访问速度：快速读写

**存储格式**：
```
Key: "bl0906_cal_XXXXXXXX" (实例ID的16进制表示)
Data: [实例ID(4字节)] + [条目数据(N*3字节)]
Size Key: "bl0906_cal_XXXXXXXX_size"
```

**配置示例**：
```yaml
bl0906_factory:
  storage_type: "preference"
  instance_id: 0x906B0001
```

### 2. I2C EEPROM 存储 (I2CEEPROMCalibrationStorage)

**适用场景**：外置 EEPROM 芯片，适合需要更高可靠性的应用

**支持型号**：
- 24C02 (256字节) - 1个实例，77个条目/实例
- 24C04 (512字节) - 3个实例，80个条目/实例
- 24C08 (1024字节) - 6个实例，82个条目/实例
- 24C16 (2048字节) - 12个实例，82个条目/实例

**存储布局**：
```
[头部(20字节)] + [实例索引(4*N字节)] + [实例数据...]
```

**头部结构**：
```cpp
struct EEPROMHeader {
    uint32_t magic;           // 魔数: 0x24CXCAL
    uint16_t version;         // 版本号: 3
    uint8_t eeprom_type;      // 型号: 02/04/08/16
    uint8_t max_instances;    // 最大实例数
    uint8_t instance_count;   // 当前实例数
    uint16_t header_crc;      // 头部CRC
    uint32_t data_crc;        // 数据区CRC
    uint32_t timestamp;       // 最后更新时间戳
    uint8_t reserved;         // 保留字节
} __attribute__((packed));
```

**配置示例**：
```yaml
bl0906_factory:
  storage_type: "eeprom"
  eeprom_type: TYPE_24C08
  i2c_address: 0x50
  i2c_id: bus_a
```

## 电量数据持久化

### 数据结构

```cpp
struct EnergyPersistenceData {
    uint32_t persistent_cf_count[11];    // 各通道累计CF计数
    uint32_t last_cf_count[11];          // 上次读取的硬件CF计数
    uint32_t save_count;                 // 存储计数器
    uint32_t checksum;                   // 数据校验和
};
```

### 持久化机制

1. **CF计数累计**：`persistent_cf_count += (current_count - last_cf_count)`
2. **芯片重启检测**：当 `current_count < last_cf_count` 时检测到重启
3. **数据完整性**：使用校验和验证数据有效性
4. **自动修复**：检测到数据损坏时自动修复

### 配置选项

```yaml
bl0906_factory:
  energy_persistence_enabled: true    # 启用电量持久化
  energy_statistics_enabled: true     # 启用电量统计
```

## 配置与使用

### 基本配置

```yaml
# 使用 NVS 存储
bl0906_factory:
  id: my_bl0906
  storage_type: "preference"
  instance_id: 0x906B0001
  energy_persistence_enabled: true
  
  # 初始校准值（可选）
  initial_calibration:
    - register: 0x6D  # CHGN_1
      value: 0x8000
    - register: 0x6E  # CHGN_2  
      value: 0x8000
```

```yaml
# 使用 I2C EEPROM 存储
i2c:
  - id: bus_a
    sda: 21
    scl: 22
    scan: true

bl0906_factory:
  id: my_bl0906
  storage_type: "eeprom"
  eeprom_type: TYPE_24C08
  i2c_address: 0x50
  i2c_id: bus_a
  instance_id: 0x906B0001
```

### 操作按钮配置

```yaml
button:
  # 保存校准数据
  - platform: template
    name: "保存校准数据"
    on_press:
      - lambda: 'id(my_bl0906).save_all_calibration_to_flash();'

  # 加载校准数据
  - platform: template
    name: "加载校准数据"
    on_press:
      - lambda: 'id(my_bl0906).load_calibration_from_flash();'

  # 显示存储状态
  - platform: template
    name: "显示存储状态"
    on_press:
      - lambda: 'id(my_bl0906).show_storage_status();'

  # 清除所有校准数据
  - platform: template
    name: "清除校准数据"
    on_press:
      - lambda: 'id(my_bl0906).clear_calibration_storage();'

  # 电量数据操作
  - platform: template
    name: "保存电量数据"
    on_press:
      - lambda: 'id(my_bl0906).force_save_energy_data();'

  - platform: template
    name: "重置电量数据"
    on_press:
      - lambda: 'id(my_bl0906).reset_energy_data();'
```

### 诊断传感器

```yaml
sensor:
  - platform: template
    name: "存储计数"
    lambda: 'return id(my_bl0906).get_save_count();'
    update_interval: 60s

text_sensor:
  - platform: template
    name: "存储类型"
    lambda: 'return id(my_bl0906).create_storage_instance()->get_storage_type();'
```

## 故障排除

### 常见问题

#### 1. 校准数据丢失

**症状**：设备重启后校准值恢复为默认值

**排查步骤**：
```yaml
button:
  - platform: template
    name: "诊断校准存储"
    on_press:
      - lambda: |
          auto storage = id(my_bl0906).create_storage_instance();
          ESP_LOGI("debug", "存储类型: %s", storage->get_storage_type().c_str());
          ESP_LOGI("debug", "最大实例数: %d", storage->get_max_instances());
          auto instances = storage->get_instance_list();
          ESP_LOGI("debug", "已存储实例数: %d", instances.size());
```

**解决方案**：
- 检查实例ID是否正确设置
- 验证存储类型配置
- 检查 I2C 连接（EEPROM 存储）

#### 2. 电量数据异常

**症状**：电量传感器显示异常大的值

**排查步骤**：
```yaml
button:
  - platform: template
    name: "诊断电量持久化"
    on_press:
      - lambda: 'id(my_bl0906).diagnose_energy_persistence();'
```

**解决方案**：
- 执行数据修复：`detect_and_repair_corrupted_data()`
- 重置电量数据：`reset_energy_data()`

#### 3. I2C EEPROM 通信失败

**症状**：日志显示 "I2C EEPROM连接测试失败"

**排查步骤**：
1. 检查 I2C 接线
2. 验证 I2C 地址
3. 测试 I2C 扫描

```yaml
button:
  - platform: template
    name: "I2C扫描"
    on_press:
      - lambda: |
          Wire.begin();
          for (uint8_t addr = 0x08; addr < 0x78; addr++) {
            Wire.beginTransmission(addr);
            if (Wire.endTransmission() == 0) {
              ESP_LOGI("i2c_scan", "找到设备: 0x%02X", addr);
            }
          }
```

#### 4. 24位寄存器校准值异常

**症状**：
- 校准值设置后读回值不一致
- 校准效果不如预期
- 日志显示"寄存器写入验证失败"

**常见原因**：
- 校准值超出16位存储范围（±32,767）
- 24位寄存器值被截断到16位范围

**排查步骤**：
```yaml
button:
  - platform: template
    name: "诊断24位寄存器问题"
    on_press:
      - lambda: |
          ESP_LOGI("diag", "=== 24位寄存器诊断 ===");
          
          // 检查所有RMSOS寄存器
          uint8_t rmsos_addrs[] = {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F};
          
          for (int i = 0; i < 6; i++) {
            bool success = false;
            int32_t chip_value = id(my_bl0906).send_read_command_and_receive(rmsos_addrs[i], &success);
            
            if (success) {
              bool in_16bit_range = (chip_value >= -32768 && chip_value <= 32767);
              
              ESP_LOGI("diag", "RMSOS_%d [0x%02X]:", i+1, rmsos_addrs[i]);
              ESP_LOGI("diag", "  芯片实际值: %d", chip_value);
              ESP_LOGI("diag", "  16位范围内: %s", in_16bit_range ? "是" : "否");
              
              if (!in_16bit_range) {
                int16_t truncated = (chip_value > 32767) ? 32767 : 
                                   (chip_value < -32768) ? -32768 : chip_value;
                ESP_LOGW("diag", "  存储时将截断为: %d", truncated);
                ESP_LOGW("diag", "  信息丢失: %d", chip_value - truncated);
              }
            } else {
              ESP_LOGE("diag", "RMSOS_%d [0x%02X]: 读取失败", i+1, rmsos_addrs[i]);
            }
          }
          
          ESP_LOGI("diag", "诊断完成");
```

**解决方案**：
1. **调整校准算法**：确保计算出的校准值在16位范围内
2. **分段校准**：使用多个较小的校准值组合
3. **直接芯片操作**：跳过持久化存储，直接设置芯片寄存器
4. **升级存储格式**：考虑支持24位存储格式（需要修改代码）

```yaml
# 解决方案示例
button:
  - platform: template
    name: "修复超范围校准值"
    on_press:
      - lambda: |
          // 读取当前芯片中的校准值
          uint8_t rmsos_addrs[] = {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F};
          
          for (int i = 0; i < 6; i++) {
            bool success = false;
            int32_t current_value = id(my_bl0906).send_read_command_and_receive(rmsos_addrs[i], &success);
            
            if (success && (current_value > 32767 || current_value < -32768)) {
              ESP_LOGW("fix", "RMSOS_%d 值 %d 超出范围，重置为0", i+1, current_value);
              
              // 重置为安全值
              if (id(my_bl0906).write_register_value(rmsos_addrs[i], 0)) {
                ESP_LOGI("fix", "RMSOS_%d 已重置为0", i+1);
              }
            }
          }
```

### 数据恢复

#### 强制恢复校准数据

```cpp
// 在严重数据损坏时使用
id(my_bl0906).force_recover_calibration_data();
```

#### 完全清除并重新初始化

```cpp
// 清除所有存储数据
id(my_bl0906).completely_clear_all_calibration_data();

// 重新设置初始校准值
id(my_bl0906).apply_calibration_values();
```

## 最佳实践

### 1. 实例ID管理

- 为每个设备分配唯一的实例ID
- 使用有意义的ID格式，如：`0x906B0001`, `0x906B0002`
- 避免使用 `0x00000000` 或 `0xFFFFFFFF`

### 2. 存储类型选择

**选择 NVS 存储的情况**：
- 单一设备应用
- 对成本敏感的项目
- 校准频率较低的应用

**选择 EEPROM 存储的情况**：
- 多设备共享校准数据
- 需要更高数据可靠性
- 频繁校准的应用场景

### 3. 数据备份策略

```yaml
# 定期备份校准数据
interval:
  - interval: 24h
    then:
      - lambda: |
          // 显示当前校准数据
          id(my_bl0906).read_and_display_calibration_data();
          // 保存到日志以便恢复
```

### 4. 监控存储健康状态

```yaml
sensor:
  - platform: template
    name: "存储写入次数"
    lambda: 'return id(my_bl0906).get_save_count();'
    update_interval: 60s
    
  - platform: template
    name: "芯片重启次数"
    lambda: 'return id(my_bl0906).get_chip_restart_count();'
    update_interval: 60s
```

### 5. 24位寄存器校准值管理

#### 校准值范围检查

```yaml
# 添加校准值范围检查传感器
sensor:
  - platform: template
    name: "RMSOS_1校准值"
    lambda: |
      // 直接从芯片读取24位真实值
      bool success = false;
      int32_t value = id(my_bl0906).send_read_command_and_receive(0x78, &success);
      if (success) {
        // 检查是否超出16位存储范围
        if (value > 32767 || value < -32768) {
          ESP_LOGW("calib", "RMSOS_1值 %d 超出16位存储范围", value);
        }
        return value;
      }
      return NAN;
    update_interval: 60s

text_sensor:
  - platform: template
    name: "校准值状态"
    lambda: |
      // 检查所有RMSOS寄存器的范围状态
      std::string status = "正常";
      bool has_overflow = false;
      
      for (int i = 0; i < 6; i++) {  // 检查6个通道
        bool success = false;
        uint8_t addr = 0x78 + i;  // RMSOS寄存器地址
        int32_t value = id(my_bl0906).send_read_command_and_receive(addr, &success);
        
        if (success && (value > 32767 || value < -32768)) {
          has_overflow = true;
          break;
        }
      }
      
      if (has_overflow) {
        status = "警告：存在超出16位范围的校准值";
      }
      
      return status;
    update_interval: 300s
```

#### 校准值设置最佳实践

```yaml
# 安全的校准值设置按钮
button:
  - platform: template
    name: "安全设置RMSOS"
    on_press:
      - lambda: |
          // 设置前检查范围
          int16_t target_value = -1000;  // 目标校准值
          
          if (target_value > 32767 || target_value < -32768) {
            ESP_LOGE("calib", "校准值 %d 超出16位范围，操作取消", target_value);
            return;
          }
          
          // 安全设置
          if (id(my_bl0906).write_register_value(0x78, target_value)) {
            ESP_LOGI("calib", "RMSOS_1设置成功: %d", target_value);
            
            // 验证设置结果
            bool success = false;
            int32_t read_back = id(my_bl0906).send_read_command_and_receive(0x78, &success);
            if (success && read_back == target_value) {
              ESP_LOGI("calib", "校准值验证成功");
            } else {
              ESP_LOGW("calib", "校准值验证失败: 设置=%d, 读回=%d", target_value, read_back);
            }
          }

  - platform: template
    name: "校准值边界测试"
    on_press:
      - lambda: |
          // 测试16位边界值
          std::vector<int16_t> test_values = {-32768, -1000, 0, 1000, 32767};
          
          for (int16_t test_val : test_values) {
            ESP_LOGI("test", "测试校准值: %d", test_val);
            
            if (id(my_bl0906).write_register_value(0x78, test_val)) {
              bool success = false;
              int32_t read_val = id(my_bl0906).send_read_command_and_receive(0x78, &success);
              
              if (success) {
                ESP_LOGI("test", "  写入: %d, 读回: %d, 匹配: %s", 
                        test_val, read_val, (read_val == test_val) ? "是" : "否");
              }
            }
            delay(100);
          }
```

#### 校准数据完整性检查

```yaml
button:
  - platform: template
    name: "校准数据完整性检查"
    on_press:
      - lambda: |
          ESP_LOGI("check", "开始校准数据完整性检查...");
          
          // 1. 读取存储中的校准数据
          id(my_bl0906).read_and_display_calibration_data();
          
          // 2. 直接从芯片读取当前校准寄存器值
          id(my_bl0906).read_current_calibration_registers();
          
          // 3. 检查RMSOS寄存器的24位真实值
          ESP_LOGI("check", "=== 24位寄存器真实值检查 ===");
          for (int i = 0; i < 6; i++) {
            uint8_t addr = 0x78 + i;
            bool success = false;
            int32_t real_value = id(my_bl0906).send_read_command_and_receive(addr, &success);
            
            if (success) {
              bool in_range = (real_value >= -32768 && real_value <= 32767);
              ESP_LOGI("check", "RMSOS_%d [0x%02X]: %d (16位范围: %s)", 
                      i+1, addr, real_value, in_range ? "✅" : "❌");
              
              if (!in_range) {
                ESP_LOGW("check", "  警告: 此值超出16位存储范围，持久化时将丢失信息");
              }
            } else {
              ESP_LOGE("check", "RMSOS_%d [0x%02X]: 读取失败", i+1, addr);
            }
          }
          
          ESP_LOGI("check", "校准数据完整性检查完成");
```

### 6. 错误处理

```cpp
// 在关键操作前检查存储状态
auto storage = id(my_bl0906).create_storage_instance();
if (!storage->verify()) {
    ESP_LOGE("storage", "存储验证失败，尝试修复");
    // 执行修复操作
}
```

## 技术规格

### 存储容量对比

| 存储类型 | 最大实例数 | 每实例最大条目数 | 总存储容量 | 写入寿命 |
|---------|-----------|-----------------|-----------|---------|
| NVS     | 16        | 64              | ~4KB      | ~10K次  |
| 24C02   | 1         | 77              | 256B      | >1M次   |
| 24C04   | 3         | 80              | 512B      | >1M次   |
| 24C08   | 6         | 82              | 1KB       | >1M次   |
| 24C16   | 12        | 82              | 2KB       | >1M次   |

### 性能指标

- **读取延迟**：< 10ms (NVS), < 50ms (EEPROM)
- **写入延迟**：< 100ms (NVS), < 200ms (EEPROM)
- **数据完整性**：CRC16 校验 (EEPROM), 硬件ECC (NVS)
- **并发安全**：互斥锁保护

### 24位寄存器技术规格

| 项目 | 芯片实际 | 存储格式 | 信息丢失 |
|------|---------|---------|---------|
| **数据位宽** | 24位有符号 | 16位有符号 | 高8位丢失 |
| **数值范围** | -8,388,608 ~ +8,388,607 | -32,768 ~ +32,767 | 99.6% |
| **存储大小** | 4字节 | 3字节 | 节省25% |
| **符号扩展** | 自动 | 写入时扩展 | 保持符号 |
| **验证机制** | 写后读验证 | 检测截断 | 日志警告 |

**寄存器分类：**
```
16位校准寄存器：
- CHGN (0xA1-0xA8): 电流增益
- CHOS (0xAC-0xB3): 电流偏置  
- RMSGN (0x6D-0x74): 有效值增益
- WATTGN (0xB7-0xBE): 功率增益
- WATTOS (0xC1-0xC8): 功率偏置

24位校准寄存器：
- RMSOS (0x78-0x7F): 有效值偏置 ⚠️
- MODE2 (0x97): 工作模式寄存器
```

**数据转换示例：**
```
原始24位值: 0x123456 (1,193,046)
存储截断为: 0x3456 (13,398)  
信息丢失: 1,179,648 (99.4%)

原始24位值: 0xFFF000 (-4,096)
符号扩展为: 0xFFF000 (-4,096) ✅
16位存储: 0xF000 (-4,096) ✅
```

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**适用版本**：BL0906 Factory v2.0+ 