# BL0906_Factory组件兼容BL0910芯片技术方案（条件编译版本）

## 1. 方案概述

在现有BL0906_Factory组件基础上，通过条件编译实现对BL0910芯片的兼容支持。编译时根据配置确定芯片型号，生成对应的二进制代码，避免运行时开销。

### 1.1 核心优势
- **编译时确定**：无运行时开销，性能最优
- **代码简洁**：避免动态选择的复杂性
- **二进制优化**：只包含需要的代码，体积更小
- **维护简化**：一套源码，条件编译适配
- **向后兼容**：现有BL0906用户无需修改配置

### 1.2 技术可行性
- ✅ **条件编译**：通过预处理器宏选择芯片配置
- ✅ **Python生成**：配置阶段生成对应的宏定义
- ✅ **地址映射**：编译时确定寄存器地址常量
- ✅ **数组大小**：编译时确定通道数和数组大小

## 2. 实现策略

### 2.1 芯片配置方案

#### 用户配置
```yaml
bl0906_factory:
  chip_model: "bl0910"  # 可选: "bl0906"（默认）, "bl0910"
  communication: uart
  uart_id: uart_bus
  # 其他配置保持不变
```

#### Python配置生成宏定义
```cpp
// 由Python配置系统自动生成
#define BL0906_FACTORY_CHIP_MODEL_BL0910  // 或 BL0906_FACTORY_CHIP_MODEL_BL0906
```

### 2.2 条件编译实现

#### 芯片参数定义 (`bl0906_chip_params.h`)
```cpp
#pragma once

// 根据芯片型号条件编译
#ifdef BL0906_FACTORY_CHIP_MODEL_BL0910
  // BL0910 参数
  #define CHIP_NAME "BL0910"
  #define CHANNEL_COUNT 10
  #define I_RMS_ADDRESSES {0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15}
  #define WATT_ADDRESSES  {0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B}
  #define CF_CNT_ADDRESSES {0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38}
  #define RMSGN_ADDRESSES {0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76}
  #define CHGN_ADDRESSES  {0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74}
  
  // BL0910 特有寄存器地址
  #define V_RMS_ADDR      0x16
  #define FREQUENCY_ADDR  0x4E
  #define TEMPERATURE_ADDR 0x5E
  
#else
  // BL0906 参数（默认）
  #define CHIP_NAME "BL0906"
  #define CHANNEL_COUNT 6
  #define I_RMS_ADDRESSES {0x0D, 0x0E, 0x0F, 0x10, 0x13, 0x14}
  #define WATT_ADDRESSES  {0x23, 0x24, 0x25, 0x26, 0x29, 0x2A}
  #define CF_CNT_ADDRESSES {0x30, 0x31, 0x32, 0x33, 0x36, 0x37}
  #define RMSGN_ADDRESSES {0x6E, 0x6F, 0x70, 0x71, 0x74, 0x75}
  #define CHGN_ADDRESSES  {0x6C, 0x6D, 0x6E, 0x6F, 0x72, 0x73}
  
  // BL0906 寄存器地址
  #define V_RMS_ADDR      0x15
  #define FREQUENCY_ADDR  0x4F
  #define TEMPERATURE_ADDR 0x5F
#endif

// 编译时常量数组
namespace esphome {
namespace bl0906_factory {

static constexpr uint8_t I_RMS_ADDR[CHANNEL_COUNT] = I_RMS_ADDRESSES;
static constexpr uint8_t WATT_ADDR[CHANNEL_COUNT] = WATT_ADDRESSES;
static constexpr uint8_t CF_CNT_ADDR[CHANNEL_COUNT] = CF_CNT_ADDRESSES;
static constexpr uint8_t RMSGN_ADDR[CHANNEL_COUNT] = RMSGN_ADDRESSES;
static constexpr uint8_t CHGN_ADDR[CHANNEL_COUNT] = CHGN_ADDRESSES;

static constexpr int MAX_CHANNELS = CHANNEL_COUNT;
static constexpr const char* CHIP_MODEL_NAME = CHIP_NAME;

} // namespace bl0906_factory
} // namespace esphome
```

### 2.3 数据结构条件编译

#### 固定大小数组定义
```cpp
// bl0906_factory.h
struct RawSensorData {
    uint32_t temperature_raw;
    uint32_t frequency_raw;
    uint32_t voltage_raw;
    
    // 编译时确定大小的数组
    struct {
        uint32_t current_raw;
        int32_t power_raw;
        uint32_t energy_raw;
        bool is_valid;
    } channels[CHANNEL_COUNT];  // 编译时常量
    
    int32_t power_sum_raw;
    uint32_t energy_sum_raw;
    uint32_t timestamp;
    bool read_complete;
};
```

#### 传感器数组定义
```cpp
class BL0906Factory : public PollingComponent {
private:
    // 编译时确定大小的数组
    sensor::Sensor* current_sensors_[CHANNEL_COUNT];
    sensor::Sensor* power_sensors_[CHANNEL_COUNT];
    sensor::Sensor* energy_sensors_[CHANNEL_COUNT];
    number::Number* calib_current_numbers_[CHANNEL_COUNT];
    number::Number* calib_power_numbers_[CHANNEL_COUNT];
    
public:
    // 静态内联函数，编译时常量
    static constexpr int get_max_channels() { return CHANNEL_COUNT; }
    static constexpr const char* get_chip_name() { return CHIP_MODEL_NAME; }
    
    // 传感器设置接口（带编译时检查）
    void set_current_sensor(int channel, sensor::Sensor *sensor) {
        if (channel >= 0 && channel < CHANNEL_COUNT) {
            current_sensors_[channel] = sensor;
        }
    }
};
```

### 2.4 寄存器操作简化

#### 直接地址访问
```cpp
bool BL0906Factory::read_all_channels_data() {
    // 编译时循环展开优化
    for (int channel = 0; channel < CHANNEL_COUNT; channel++) {
        // 直接使用编译时常量数组
        raw_data_.channels[channel].current_raw = read_register(I_RMS_ADDR[channel]);
        raw_data_.channels[channel].power_raw = read_register(WATT_ADDR[channel]);
        raw_data_.channels[channel].energy_raw = read_register(CF_CNT_ADDR[channel]);
    }
    
    // 公共寄存器读取
    raw_data_.voltage_raw = read_register(V_RMS_ADDR);
    raw_data_.frequency_raw = read_register(FREQUENCY_ADDR);
    raw_data_.temperature_raw = read_register(TEMPERATURE_ADDR);
    
    return true;
}
```

## 3. Python配置系统实现

### 3.1 配置验证和宏生成

#### 配置模式定义 (`__init__.py`)
```python
CONF_CHIP_MODEL = "chip_model"

CHIP_MODELS = {
    "bl0906": {
        "max_channels": 6,
        "macro": "BL0906_FACTORY_CHIP_MODEL_BL0906"
    },
    "bl0910": {
        "max_channels": 10,
        "macro": "BL0906_FACTORY_CHIP_MODEL_BL0910"
    }
}

CONFIG_SCHEMA = cv.All(
    cv.Schema({
        cv.GenerateID(): cv.declare_id(BL0906Factory),
        cv.Optional(CONF_CHIP_MODEL, default="bl0906"): cv.enum(CHIP_MODELS, lower=True),
        # ... 其他配置
    }),
    validate_chip_configuration
)

def validate_chip_configuration(config):
    """验证芯片配置的合理性"""
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    chip_info = CHIP_MODELS[chip_model]
    max_channels = chip_info["max_channels"]
    
    # 检查所有传感器配置是否在芯片支持范围内
    def check_sensors(sensor_list, sensor_type):
        if sensor_list:
            for sensor_config in sensor_list:
                channel = sensor_config.get(CONF_CHANNEL)
                if channel and channel > max_channels:
                    raise cv.Invalid(
                        f"{sensor_type} channel {channel} exceeds {chip_model} "
                        f"maximum channels ({max_channels})"
                    )
    
    # 验证各类传感器配置
    check_sensors(config.get("current"), "Current")
    check_sensors(config.get("power"), "Power") 
    check_sensors(config.get("energy"), "Energy")
    
    return config
```

#### 代码生成逻辑
```python
async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    
    # 生成芯片型号宏定义
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    chip_info = CHIP_MODELS[chip_model]
    
    # 添加预处理器宏定义
    cg.add_define(chip_info["macro"])
    
    # 可选：添加调试信息
    cg.add(cg.RawStatement(f'ESP_LOGI("bl0906_factory", "Compiled for {chip_model.upper()}");'))
    
    # 其他配置处理...
    await setup_communication(var, config)
    await setup_sensors(var, config)
```

### 3.2 传感器配置适配

#### 动态通道范围验证
```python
# sensor.py 修改
def create_sensor_schema(sensor_type):
    """创建传感器配置模式，支持动态通道范围"""
    return cv.Schema({
        cv.Required(CONF_CHANNEL): cv.All(
            cv.int_, 
            cv.Range(min=1, max=10)  # 允许最大10通道，在验证阶段检查实际限制
        ),
        cv.Optional(CONF_NAME): cv.string,
        # ... 其他传感器配置
    })

# 在父配置的validate_chip_configuration中进行实际的通道数检查
```

## 4. 配置示例

### 4.1 BL0906配置（默认，无需修改）
```yaml
bl0906_factory:
  # chip_model: bl0906  # 默认值，可省略
  communication: uart
  uart_id: uart_bus
  
sensor:
  - platform: bl0906_factory
    current:
      channel: 1
      name: "Current 1"
    power:
      channel: 1
      name: "Power 1"
  # 最多支持6个通道 (1-6)
```

### 4.2 BL0910配置
```yaml
bl0906_factory:
  chip_model: bl0910  # 指定使用BL0910
  communication: uart
  uart_id: uart_bus
  
sensor:
  - platform: bl0906_factory
    current:
      channel: 7  # BL0910支持1-10通道
      name: "Current 7"
    power:
      channel: 7
      name: "Power 7"
  # 最多支持10个通道 (1-10)
```

## 5. 条件编译的优势

### 5.1 性能优势
- **零运行时开销**：没有动态选择的if-else分支
- **编译器优化**：循环展开、常量折叠等优化
- **内存优化**：只分配需要的数组空间
- **代码缓存友好**：更好的指令局部性

### 5.2 代码质量优势
- **类型安全**：编译时检查数组边界
- **代码简洁**：没有复杂的动态配置逻辑
- **易于调试**：编译时错误更容易定位
- **二进制大小**：只包含使用的代码路径

### 5.3 维护优势
- **单一源码**：一套代码维护两种芯片
- **清晰的条件**：通过宏定义明确芯片差异
- **测试简化**：可以分别编译测试两种配置

## 6. 实施计划

### 6.1 第一阶段：条件编译基础架构
- [ ] 创建 `bl0906_chip_params.h` 头文件
- [ ] 修改 `bl0906_factory.h` 使用条件编译
- [ ] 更新数据结构定义

### 6.2 第二阶段：Python配置系统
- [ ] 修改 `__init__.py` 支持芯片型号配置
- [ ] 实现配置验证逻辑
- [ ] 添加宏定义生成

### 6.3 第三阶段：功能测试
- [ ] 创建BL0906测试配置
- [ ] 创建BL0910测试配置
- [ ] 验证编译和运行结果

## 7. 测试方案

### 7.1 编译时测试
```bash
# 测试BL0906编译
pio run -e bl0906_test

# 测试BL0910编译
pio run -e bl0910_test

# 检查二进制大小差异
size .pio/build/bl0906_test/firmware.elf
size .pio/build/bl0910_test/firmware.elf
```

### 7.2 静态检查
```cpp
// 编译时断言验证
static_assert(CHANNEL_COUNT == 6 || CHANNEL_COUNT == 10, 
              "Invalid channel count");

static_assert(sizeof(I_RMS_ADDR) / sizeof(I_RMS_ADDR[0]) == CHANNEL_COUNT,
              "I_RMS address array size mismatch");
```

## 8. 总结

基于条件编译的兼容性方案具有以下优势：

1. **性能最优**：编译时确定，无运行时开销
2. **代码简洁**：避免复杂的动态配置逻辑  
3. **类型安全**：编译时检查，减少运行时错误
4. **易于维护**：清晰的条件编译边界
5. **向后兼容**：现有用户配置无需修改

这种方案充分利用了C++的编译时特性，在保持代码复用的同时，实现了最优的性能和最简洁的实现。对于嵌入式系统来说，这是一个非常合适的解决方案。 