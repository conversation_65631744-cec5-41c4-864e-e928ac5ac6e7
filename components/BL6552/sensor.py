import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import sensor, uart
from esphome.const import (
    UNIT_PERCENT,
    CONF_ID,
)

DEPENDENCIES = ["uart"]
AUTO_LOAD = ["BL6552"]

BL6552_ns = cg.esphome_ns.namespace("BL6552")
BL6552 = BL6552_ns.class_(
    "BL6552", cg.PollingComponent, uart.UARTDevice
)

CONFIG_SCHEMA = (
    cv.Schema(
        {
            cv.GenerateID(): cv.declare_id(BL6552),
            # A相传感器
            cv.Optional("va"): sensor.sensor_schema(
                accuracy_decimals=1,
                device_class="voltage",
                unit_of_measurement="V"
            ),
            cv.Optional("ia"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="current",
                unit_of_measurement="A"
            ),
            cv.Optional("watta"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power",
                unit_of_measurement="W"
            ),
            cv.Optional("pfa"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power_factor",
                unit_of_measurement="%"
            ),
            cv.Optional("energy_a"): sensor.sensor_schema(
                accuracy_decimals=2,
                device_class="energy",
                state_class="total",
                unit_of_measurement="kWh"
            ),
            # B相传感器
            cv.Optional("vb"): sensor.sensor_schema(
                accuracy_decimals=1,
                device_class="voltage",
                unit_of_measurement="V"
            ),
            cv.Optional("ib"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="current",
                unit_of_measurement="A"
            ),
            cv.Optional("wattb"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power",
                unit_of_measurement="W"
            ),
            cv.Optional("pfb"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power_factor",
                unit_of_measurement="%"
            ),
            cv.Optional("energy_b"): sensor.sensor_schema(
                accuracy_decimals=2,
                device_class="energy",
                state_class="total",
                unit_of_measurement="kWh"
            ),
            # C相传感器
            cv.Optional("vc"): sensor.sensor_schema(
                accuracy_decimals=1,
                device_class="voltage",
                unit_of_measurement="V"
            ),
            cv.Optional("ic"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="current",
                unit_of_measurement="A"
            ),
            cv.Optional("wattc"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power",
                unit_of_measurement="W"
            ),
            cv.Optional("pfc"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power_factor",
                unit_of_measurement="%"
            ),
            cv.Optional("energy_c"): sensor.sensor_schema(
                accuracy_decimals=2,
                device_class="energy",
                state_class="total",
                unit_of_measurement="kWh"
            ),
            # 总测量值传感器
            cv.Optional("total_power"): sensor.sensor_schema(
                accuracy_decimals=3,
                device_class="power",
                unit_of_measurement="W"
            ),
            cv.Optional("total_energy"): sensor.sensor_schema(
                accuracy_decimals=2,
                device_class="energy",
                state_class="total",
                unit_of_measurement="kWh"
            ),
            # 其他传感器
            cv.Optional("frequency"): sensor.sensor_schema(
                accuracy_decimals=1,
                device_class="frequency",
                unit_of_measurement="Hz"
            ),
            cv.Optional("temperature"): sensor.sensor_schema(
                accuracy_decimals=1,
                device_class="temperature",
                unit_of_measurement="℃"
            ),
        }
    )
    .extend(uart.UART_DEVICE_SCHEMA)
    .extend(cv.polling_component_schema("60s"))
)

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    await uart.register_uart_device(var, config)

    # A相传感器
    if "va" in config:
        sens = await sensor.new_sensor(config["va"])
        cg.add(var.set_va_sensor(sens))
    if "ia" in config:
        sens = await sensor.new_sensor(config["ia"])
        cg.add(var.set_ia_sensor(sens))
    if "watta" in config:
        sens = await sensor.new_sensor(config["watta"])
        cg.add(var.set_watta_sensor(sens))
    if "pfa" in config:
        sens = await sensor.new_sensor(config["pfa"])
        cg.add(var.set_pfa_sensor(sens))
    if "energy_a" in config:
        sens = await sensor.new_sensor(config["energy_a"])
        cg.add(var.set_energy_a_sensor(sens))

    # B相传感器
    if "vb" in config:
        sens = await sensor.new_sensor(config["vb"])
        cg.add(var.set_vb_sensor(sens))
    if "ib" in config:
        sens = await sensor.new_sensor(config["ib"])
        cg.add(var.set_ib_sensor(sens))
    if "wattb" in config:
        sens = await sensor.new_sensor(config["wattb"])
        cg.add(var.set_wattb_sensor(sens))
    if "pfb" in config:
        sens = await sensor.new_sensor(config["pfb"])
        cg.add(var.set_pfb_sensor(sens))
    if "energy_b" in config:
        sens = await sensor.new_sensor(config["energy_b"])
        cg.add(var.set_energy_b_sensor(sens))

    # C相传感器
    if "vc" in config:
        sens = await sensor.new_sensor(config["vc"])
        cg.add(var.set_vc_sensor(sens))
    if "ic" in config:
        sens = await sensor.new_sensor(config["ic"])
        cg.add(var.set_ic_sensor(sens))
    if "wattc" in config:
        sens = await sensor.new_sensor(config["wattc"])
        cg.add(var.set_wattc_sensor(sens))
    if "pfc" in config:
        sens = await sensor.new_sensor(config["pfc"])
        cg.add(var.set_pfc_sensor(sens))
    if "energy_c" in config:
        sens = await sensor.new_sensor(config["energy_c"])
        cg.add(var.set_energy_c_sensor(sens))

    # 总测量值传感器
    if "total_power" in config:
        sens = await sensor.new_sensor(config["total_power"])
        cg.add(var.set_total_power_sensor(sens))
    if "total_energy" in config:
        sens = await sensor.new_sensor(config["total_energy"])
        cg.add(var.set_total_energy_sensor(sens))

    # 其他传感器
    if "frequency" in config:
        sens = await sensor.new_sensor(config["frequency"])
        cg.add(var.set_frequency_sensor(sens))
    if "temperature" in config:
        sens = await sensor.new_sensor(config["temperature"])
        cg.add(var.set_temperature_sensor(sens)) 