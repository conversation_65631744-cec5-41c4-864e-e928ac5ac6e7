#pragma once

#include "esphome/core/component.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/sensor/sensor.h"

namespace esphome {
namespace BL6552 {
// 硬件资料
// https://www.belling.com.cn/media/file_object/bel_product/BL6552/datasheet/BL6552_V1.2_cn.pdf
// https://www.belling.com.cn/media/file_object/bel_product/BL6552/guide/BL6552%20APP%20Note_V1.1.pdf
// 添加TAG声明
extern const char *TAG;

using namespace esphome::uart;

// BL6552寄存器地址定义
static const uint8_t BL6552_READ_COMMAND = 0x35;  // 读操作命令
static const uint8_t BL6552_WRITE_COMMAND = 0xCA;  // 写操作命令

// A相测量值寄存器地址
static const uint8_t BL6552_VA_RMS = 0x13;     // A相电压有效值寄存器
static const uint8_t BL6552_IA_RMS = 0x0F;     // A相电流有效值寄存器
static const uint8_t BL6552_WATTA = 0x22;      // A相有功功率寄存器
static const uint8_t BL6552_PFA = 0x47;        // A相功率因数寄存器
static const uint8_t BL6552_CFA_CNT = 0x2F;    // A相有功脉冲计数寄存器

// B相测量值寄存器地址
static const uint8_t BL6552_VB_RMS = 0x14;     // B相电压有效值寄存器
static const uint8_t BL6552_IB_RMS = 0x0E;     // B相电流有效值寄存器
static const uint8_t BL6552_WATTB = 0x23;      // B相有功功率寄存器
static const uint8_t BL6552_PFB = 0x48;        // B相功率因数寄存器
static const uint8_t BL6552_CFB_CNT = 0x30;    // B相有功脉冲计数寄存器

// C相测量值寄存器地址
static const uint8_t BL6552_VC_RMS = 0x15;     // C相电压有效值寄存器
static const uint8_t BL6552_IC_RMS = 0x0D;     // C相电流有效值寄存器
static const uint8_t BL6552_WATTC = 0x24;      // C相有功功率寄存器
static const uint8_t BL6552_PFC = 0x49;        // C相功率因数寄存器
static const uint8_t BL6552_CFC_CNT = 0x31;    // C相有功脉冲计数寄存器

// 总测量值寄存器地址
static const uint8_t BL6552_WATT_SUM = 0x25;   // 总有功功率寄存器
static const uint8_t BL6552_CF_SUM_CNT = 0x32; // 总有功脉冲计数寄存器
static const uint8_t BL6552_FREQUENCY = 0x2E;  // 频率寄存器
static const uint8_t BL6552_PF_SUM = 0x4A;     // 合相功率因数寄存器（新增）
static const uint8_t BL6552_TEMPERATURE = 0x5E; // 温度寄存器

// 校准寄存器地址修正（根据用户提供的最新数据）
static const uint8_t BL6552_IA_RMSGN = 0x6F;    // A相电流有效值增益调整寄存器（原0x6C → 修正为0x6F）
static const uint8_t BL6552_IB_RMSGN = 0x6E;    // B相电流有效值增益调整寄存器（原0x6D → 修正为0x6E）
static const uint8_t BL6552_IC_RMSGN = 0x6D;    // C相电流有效值增益调整寄存器（原0x6E → 修正为0x6D）

static const uint8_t BL6552_VA_RMSGN = 0x73;    // A相电压有效值增益调整寄存器（新增）
static const uint8_t BL6552_VB_RMSGN = 0x74;    // B相电压有效值增益调整寄存器（新增）
static const uint8_t BL6552_VC_RMSGN = 0x75;    // C相电压有效值增益调整寄存器（新增）

static const uint8_t BL6552_IA_RMSOS = 0x7A;    // A相电流有效值偏置校正寄存器（原0x77 → 修正为0x7A）
static const uint8_t BL6552_IB_RMSOS = 0x79;    // B相电流有效值偏置校正寄存器（原0x78 → 修正为0x79）
static const uint8_t BL6552_IC_RMSOS = 0x78;    // C相电流有效值偏置校正寄存器（原0x79 → 修正为0x78）

static const uint8_t BL6552_VA_RMSOS = 0x7E;    // A相电压有效值偏置校正寄存器（新增）
static const uint8_t BL6552_VB_RMSOS = 0x7F;    // B相电压有效值偏置校正寄存器（新增）
static const uint8_t BL6552_VC_RMSOS = 0x80;    // C相电压有效值偏置校正寄存器（新增）

// 写保护寄存器地址
static const uint8_t BL6552_WRPROT = 0x9E;    // 写保护寄存器

// 写保护命令
static const uint8_t BL6552_WRPROT_WRITABLE[6] = {0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7};  // 解除写保护
static const uint8_t BL6552_WRPROT_READONLY[6] = {0xCA, 0x9E, 0x00, 0x00, 0x00, 0x61};   // 启用写保护

// 数据包结构
#pragma pack(push, 1)
struct DataPacket {
  uint8_t l;
  uint8_t m;
  uint8_t h;
  uint8_t checksum;
};
#pragma pack(pop)

struct ube24_t { 
  uint8_t l, m, h;
};

struct sbe24_t {
  uint8_t l, m;
  int8_t h;
};

// 有功功率增益寄存器（新增）
static const uint8_t BL6552_WATTGN_A = 0xB6;   // A相有功功率增益调整
static const uint8_t BL6552_WATTGN_B = 0xB7;   // B相有功功率增益调整
static const uint8_t BL6552_WATTGN_C = 0xB8;   // C相有功功率增益调整

// 有功功率偏置寄存器（新增）
static const uint8_t BL6552_WATTOS_A = 0xC2;   // A相有功功率偏置调整
static const uint8_t BL6552_WATTOS_B = 0xC3;   // B相有功功率偏置调整
static const uint8_t BL6552_WATTOS_C = 0xC4;   // C相有功功率偏置调整

// 电流通道偏置调整寄存器（新增）
static const uint8_t BL6552_IA_CHOS = 0xAE;    // A相电流通道偏置调整
static const uint8_t BL6552_IB_CHOS = 0xAD;    // B相电流通道偏置调整
static const uint8_t BL6552_IC_CHOS = 0xAC;    // C相电流通道偏置调整
static const uint8_t BL6552_IN_CHOS = 0xAF;    // N相电流通道偏置调整（新增）

// 电压通道偏置调整寄存器（新增）
static const uint8_t BL6552_VA_CHOS = 0xB0;    // A相电压通道偏置调整
static const uint8_t BL6552_VB_CHOS = 0xB1;    // B相电压通道偏置调整
static const uint8_t BL6552_VC_CHOS = 0xB2;    // C相电压通道偏置调整

// 电流通道增益调整寄存器（新增）
static const uint8_t BL6552_IA_CHGN = 0xA3;    // A相电流通道增益调整（16位补码）
static const uint8_t BL6552_IB_CHGN = 0xA2;    // B相电流通道增益调整
static const uint8_t BL6552_IC_CHGN = 0xA1;    // C相电流通道增益调整
static const uint8_t BL6552_IN_CHGN = 0xA4;    // N相电流通道增益调整

// 电压通道增益调整寄存器（新增）
static const uint8_t BL6552_VA_CHGN = 0xA7;    // A相电压通道增益调整
static const uint8_t BL6552_VB_CHGN = 0xA8;    // B相电压通道增益调整
static const uint8_t BL6552_VC_CHGN = 0xA9;    // C相电压通道增益调整

/**
 * @brief BL6552三相电能计量芯片驱动组件
 */
class BL6552 : public PollingComponent, public uart::UARTDevice {
 public:
  explicit BL6552(uint32_t update_interval = 1000)
      : PollingComponent(update_interval) {}
  
  // A相传感器配置接口
  void set_va_sensor(sensor::Sensor *sensor) { va_sensor_ = sensor; }
  void set_ia_sensor(sensor::Sensor *sensor) { ia_sensor_ = sensor; }
  void set_watta_sensor(sensor::Sensor *sensor) { watta_sensor_ = sensor; }
  void set_pfa_sensor(sensor::Sensor *sensor) { pfa_sensor_ = sensor; }
  void set_energy_a_sensor(sensor::Sensor *sensor) { energy_a_sensor_ = sensor; }

  // B相传感器配置接口
  void set_vb_sensor(sensor::Sensor *sensor) { vb_sensor_ = sensor; }
  void set_ib_sensor(sensor::Sensor *sensor) { ib_sensor_ = sensor; }
  void set_wattb_sensor(sensor::Sensor *sensor) { wattb_sensor_ = sensor; }
  void set_pfb_sensor(sensor::Sensor *sensor) { pfb_sensor_ = sensor; }
  void set_energy_b_sensor(sensor::Sensor *sensor) { energy_b_sensor_ = sensor; }

  // C相传感器配置接口
  void set_vc_sensor(sensor::Sensor *sensor) { vc_sensor_ = sensor; }
  void set_ic_sensor(sensor::Sensor *sensor) { ic_sensor_ = sensor; }
  void set_wattc_sensor(sensor::Sensor *sensor) { wattc_sensor_ = sensor; }
  void set_pfc_sensor(sensor::Sensor *sensor) { pfc_sensor_ = sensor; }
  void set_energy_c_sensor(sensor::Sensor *sensor) { energy_c_sensor_ = sensor; }

  // 总测量值传感器配置接口
  void set_total_power_sensor(sensor::Sensor *sensor) { total_power_sensor_ = sensor; }
  void set_total_energy_sensor(sensor::Sensor *sensor) { total_energy_sensor_ = sensor; }
  void set_frequency_sensor(sensor::Sensor *sensor) { frequency_sensor_ = sensor; }
  void set_temperature_sensor(sensor::Sensor *sensor) { temperature_sensor_ = sensor; }

  // 新增合相功率因数传感器配置接口
  void set_total_pf_sensor(sensor::Sensor *sensor) { total_pf_sensor_ = sensor; }

  void loop() override;
  void setup() override;
  void update() override;

 protected:
  bool is_updating_ = false;
  // A相传感器指针
  sensor::Sensor *va_sensor_{nullptr};
  sensor::Sensor *ia_sensor_{nullptr};
  sensor::Sensor *watta_sensor_{nullptr};
  sensor::Sensor *pfa_sensor_{nullptr};
  sensor::Sensor *energy_a_sensor_{nullptr};

  // B相传感器指针
  sensor::Sensor *vb_sensor_{nullptr};
  sensor::Sensor *ib_sensor_{nullptr};
  sensor::Sensor *wattb_sensor_{nullptr};
  sensor::Sensor *pfb_sensor_{nullptr};
  sensor::Sensor *energy_b_sensor_{nullptr};

  // C相传感器指针
  sensor::Sensor *vc_sensor_{nullptr};
  sensor::Sensor *ic_sensor_{nullptr};
  sensor::Sensor *wattc_sensor_{nullptr};
  sensor::Sensor *pfc_sensor_{nullptr};
  sensor::Sensor *energy_c_sensor_{nullptr};

  // 总测量值传感器指针
  sensor::Sensor *total_power_sensor_{nullptr};
  sensor::Sensor *total_energy_sensor_{nullptr};
  sensor::Sensor *frequency_sensor_{nullptr};
  sensor::Sensor *temperature_sensor_{nullptr};

  // 新增合相功率因数传感器指针
  sensor::Sensor *total_pf_sensor_{nullptr};

  // 状态管理
  int current_state_{0};
  struct {
    uint32_t timeout_errors{0};
    uint32_t checksum_errors{0};
  } stats_;

  // 核心方法
  void read_data_(uint8_t address, float reference, sensor::Sensor *sensor);
  void bias_correction(uint8_t address, float measurements, float correction);
  
  // 数据转换
  static uint32_t to_uint32_t(ube24_t input);
  static int32_t to_int32_t(sbe24_t input);

 private:
  bool read_array_with_timeout(uint8_t *data, size_t len, uint32_t timeout_ms = 50);
  bool wait_until_available(size_t len, uint32_t timeout_ms);
};

static uint8_t BL6552_checksum(const uint8_t address, const DataPacket *data);
}  // namespace BL6552
}  // namespace esphome 