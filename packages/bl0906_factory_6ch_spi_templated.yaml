# BL0906 Factory 6通道SPI配置 - 模板化版本
# 使用模板化的packages来减少重复配置

# 电量持久化存储配置
preferences:
  flash_write_interval: 1min

# 外部组件配置
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s

# 通信接口配置
i2c:
  sda: 10
  scl: 18
  scan: true
  frequency: 400kHz
  id: i2c_bus

spi:
  - id: spi_bus
    mosi_pin: 6
    miso_pin: 7
    clk_pin: 19

# BL0906Factory主配置
bl0906_factory:
    communication: spi
    spi_id: spi_bus
    cs_pin: 3
    id: sensor_bl0906
    update_interval: 60s
    instance_id: 0x906B0001
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: eeprom
      eeprom_type: 24c02
    i2c_id: i2c_bus
    address: 0x50
    chgn_reference_sensor: chgn_reference_value

# 使用模板化packages
packages:
  # 全局传感器（电压、频率、温度、总和传感器）
  global_sensors: !include templates/bl0906_global_sensors.yaml
  
  # 6个通道的传感器配置
  ch1_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 1
      channel_name: "${ch1}"
  
  ch2_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 2
      channel_name: "${ch2}"
      
  ch3_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 3
      channel_name: "${ch3}"
      
  ch4_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 4
      channel_name: "${ch4}"
      
  ch5_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 5
      channel_name: "${ch5}"
      
  ch6_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 6
      channel_name: "${ch6}"
      
  # 6个通道的校准参数配置
  ch1_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 1
      channel_name: "CH_1"
      
  ch2_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 2
      channel_name: "CH_2"
      
  ch3_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 3
      channel_name: "CH_3"
      
  ch4_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 4
      channel_name: "CH_4"
      
  ch5_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 5
      channel_name: "CH_5"
      
  ch6_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 6
      channel_name: "CH_6"
      
  # 调试按钮
  debug_buttons: !include templates/bl0906_debug_buttons.yaml
  
  # 系统监控
  system_monitoring: !include templates/system_monitoring.yaml 