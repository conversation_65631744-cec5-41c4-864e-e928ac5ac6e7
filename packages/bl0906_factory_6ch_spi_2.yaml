# 电量持久化存储配置 - 替换原有的globals系统
# 新的持久化系统直接在BL0906Factory组件中实现，无需globals

# 启用preferences组件用于数据持久化
preferences:
  flash_write_interval: 1min  # 每分钟写入一次flash，平衡数据安全和flash寿命

# Time component for energy statistics
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
i2c:
  sda: 10
  scl: 18
  scan: true
  frequency: 400kHz
  id: i2c_bus

spi:
  - id: spi_bus
    mosi_pin: 6
    miso_pin: 7
    clk_pin: 19
bl0906_factory:
    communication: spi
    spi_id: spi_bus
    cs_pin: 3
    id: sensor_bl0906
    update_interval: 60s
    instance_id: 0x906B0001  # 手动指定实例ID
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: eeprom
      eeprom_type: 24c02
    # EEPROM的I2C配置（独立于BL0906的SPI通信）
    i2c_id: i2c_bus
    address: 0x50
    chgn_reference_sensor: chgn_reference_value
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    
    # 全局传感器
    frequency:
      name: 'BL0906 Frequency'
      icon: "mdi:sine-wave"
    temperature:
      name: 'BL0906 Temperature'
      icon: "mdi:thermometer"
    voltage:
      name: 'BL0906 Voltage'
      id: bl0906_voltage
      icon: "mdi:lightning-bolt-outline"
      accuracy_decimals: 3
      filters: 
        - sliding_window_moving_average: 
            window_size: 10
            send_every: 1
            send_first_at: 1
    
    # 总和传感器
    power_sum:
      name: "6-ch sum power"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: power
    energy_sum:
      name: "6-chs sum energy"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: energy
      accuracy_decimals: 6
      unit_of_measurement: kWh
    total_energy_sum:
      name: "6-ch Sum Total Energy"
      icon: "mdi:sigma"
      unit_of_measurement: kWh
      device_class: energy
      state_class: total_increasing
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy

    # 总电量统计传感器
    today_total_energy:
      name: "Today Total Energy"
      icon: "mdi:calendar-today"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    yesterday_total_energy:
      name: "Yesterday Total Energy"
      icon: "mdi:calendar-minus"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    week_total_energy:
      name: "Week Total Energy"
      icon: "mdi:calendar-week"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    month_total_energy:
      name: "Month Total Energy"
      icon: "mdi:calendar-month"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    year_total_energy:
      name: "Year Total Energy"
      icon: "mdi:calendar"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats

    # 通道1配置
    ch1:
      current:
        name: "${ch1} current"
        id: ch1_current
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
        filters: 
          - sliding_window_moving_average: 
              window_size: 10
              send_every: 1
              send_first_at: 1
      power:
        name: "${ch1} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch1_energy
        name: "${ch1} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch1} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH1 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH1 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH1 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH1 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH1 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道2配置
    ch2:
      current:
        name: "${ch2} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch2} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch2_energy
        name: "${ch2} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch2} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH2 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH2 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH2 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH2 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH2 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道3配置
    ch3:
      current:
        name: "${ch3} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch3} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch3_energy
        name: "${ch3} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch3} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH3 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH3 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH3 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH3 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH3 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道4配置
    ch4:
      current:
        name: "${ch4} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch4} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch4_energy
        name: "${ch4} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch4} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH4 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH4 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH4 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH4 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH4 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道5配置
    ch5:
      current:
        name: "${ch5} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch5} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch5_energy
        name: "${ch5} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch5} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH5 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH5 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH5 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH5 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH5 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道6配置
    ch6:
      current:
        name: "${ch6} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch6} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch6_energy
        name: "${ch6} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch6} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH6 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH6 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH6 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH6 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH6 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

button:
  - platform: template
    name: "读取校正值"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->refresh_all_calib_numbers();

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
      
  - platform: template
    name: "CALCULATE RMSOS"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Verify Calibration Data"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0906->refresh_all_calib_numbers();
        }
  - platform: template
    name: "Read Calibration Data from Flash"
    icon: "mdi:database-search"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->read_and_display_calibration_data();
            }

  - platform: template
    name: "Show All Instance Calibration Data"
    icon: "mdi:database-outline"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->show_all_instances_calibration_data();
            }

  - platform: template
    name: "Clear Storage (Fix Full Storage)"
    icon: "mdi:delete-sweep"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->clear_calibration_storage();
            }

  - platform: template
    name: "Show Storage Status"
    icon: "mdi:information"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              bl0906->show_storage_status();
            }

  # 批量设置所有CHGN值
  - platform: template
    name: "批量更新所有CHGN值"
    id: update_all_chgn_button
    on_press:
      - lambda: |-
          // 调用BL0906Factory的批量修改方法
          id(sensor_bl0906)->update_all_chgn_values_from_sensor(); 
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "CHGN 0"
    on_press:
      - lambda: id(sensor_bl0906)->reset_all_chgn_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
  # 批量清零RMSOS值
  - platform: template
    name: "RMSOS 0"
    on_press:
      - lambda: id(sensor_bl0906)->reset_all_rmsos_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "CH_1 Current Gain"
      id: chgn_1_adjust
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_2:
      name: "CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_3:
      name: "CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_4:
      name: "CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_5:
      name: "CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_6:
      name: "CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_v_decimal:
      name: "Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: power

    chos_decimal_1:
      name: "CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_2:
      name: "CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_3:
      name: "CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_4:
      name: "CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_5:
      name: "CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_6:
      name: "CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_v_decimal:
      name: "Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: power
    # 添加十进制形式的RMS校准值
    rmsgn_decimal_1:
      name: "CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_2:
      name: "CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_3:
      name: "CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_4:
      name: "CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_5:
      name: "CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_6:
      name: "CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_1:
      name: "CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_2:
      name: "CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_3:
      name: "CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_4:
      name: "CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_5:
      name: "CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_6:
      name: "CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current


switch:
  # 电量持久化开关
  - platform: template
    name: "Energy Persistence"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "电量持久化存储已禁用");
