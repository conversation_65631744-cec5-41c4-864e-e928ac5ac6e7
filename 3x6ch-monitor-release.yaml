# 3×6通道监控配置 - 模板化版本
# 使用模板化的packages来减少重复配置，从1486行减少到约200行

substitutions:
  name: "3x6-ch-monitor"
  friendly_name: "3x6-ch-monitor"
  chA1: "ch_A1"
  chA2: "ch_A2"
  chA3: "ch_A3"
  chA4: "ch_A4"
  chA5: "ch_A5"
  chA6: "ch_A6"
  chB1: "ch_B1"
  chB2: "ch_B2"
  chB3: "ch_B3"
  chB4: "ch_B4"
  chB5: "ch_B5"
  chB6: "ch_B6"
  chC1: "ch_C1"
  chC2: "ch_C2"
  chC3: "ch_C3"
  chC4: "ch_C4"
  chC5: "ch_C5"
  chC6: "ch_C6"

esphome:
  name: "${name}"
  name_add_mac_suffix: true
  friendly_name: "${friendly_name}"

preferences:
  flash_write_interval: 2min

esp32:
  board: esp32dev
  framework:
    type: arduino

# 基础配置
logger:
  baud_rate: 0
  level: debug
  logs:
    sensor: warn

api:
ota:
  - platform: esphome

wifi:
  ap:
    ssid: "3X6Ch-Monitor"
    password: ""

captive_portal:

web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: calibrate
      name: "Calibration"
      sorting_weight: 100
    - id: voltage_status
      name: "Voltage Status"
      sorting_weight: 1
    - id: power_status
      name: "Power Status"
      sorting_weight: 10
    - id: currentA
      name: "CurrentA"
      sorting_weight: 20
    - id: powerA
      name: "PowerA"
      sorting_weight: 30
    - id: energyA
      name: "EnergyA"
      sorting_weight: 40
    - id: currentB
      name: "CurrentB"
      sorting_weight: 21
    - id: powerB
      name: "PowerB"
      sorting_weight: 31
    - id: energyB
      name: "EnergyB"
      sorting_weight: 41
    - id: currentC
      name: "CurrentC"
      sorting_weight: 22
    - id: powerC
      name: "PowerC"
      sorting_weight: 32
    - id: energyC
      name: "EnergyC"
      sorting_weight: 42
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 50
    - id: diagnostic
      name: "Diagnostic"
      sorting_weight: 60

external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s

status_led:
  pin: GPIO4

# UART配置
uart:
  - id: uart_bus_a
    rx_pin: 25
    tx_pin: 14
    baud_rate: 19200
  - id: uart_bus_b
    rx_pin: 33
    tx_pin: 27
    baud_rate: 19200
  - id: uart_bus_c
    rx_pin: 32
    tx_pin: 26
    baud_rate: 19200

# 使用模板化packages
packages:
  # A相配置
  phase_a_instance: !include
    file: templates/bl0906_phase_instance_template_release.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"
      instance_id: "0x906B0001"
      uart_id: "uart_bus_a"
      ch1_name: "${chA1}"
      ch2_name: "${chA2}"
      ch3_name: "${chA3}"
      ch4_name: "${chA4}"
      ch5_name: "${chA5}"
      ch6_name: "${chA6}"
      current_group: "currentA"
      power_group: "powerA"
      energy_group: "energyA"

  phase_a_debug: !include
    file: templates/bl0906_phase_debug_buttons_release.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"

  # B相配置
  phase_b_instance: !include
    file: templates/bl0906_phase_instance_template_release.yaml
    vars:
      phase_name: "B"
      phase_lower: "b"
      instance_id: "0x906B0002"
      uart_id: "uart_bus_b"
      ch1_name: "${chB1}"
      ch2_name: "${chB2}"
      ch3_name: "${chB3}"
      ch4_name: "${chB4}"
      ch5_name: "${chB5}"
      ch6_name: "${chB6}"
      current_group: "currentB"
      power_group: "powerB"
      energy_group: "energyB"

  phase_b_debug: !include
    file: templates/bl0906_phase_debug_buttons_release.yaml
    vars:
      phase_name: "B"
      phase_lower: "b"

  # C相配置
  phase_c_instance: !include
    file: templates/bl0906_phase_instance_template_release.yaml
    vars:
      phase_name: "C"
      phase_lower: "c"
      instance_id: "0x906B0003"
      uart_id: "uart_bus_c"
      ch1_name: "${chC1}"
      ch2_name: "${chC2}"
      ch3_name: "${chC3}"
      ch4_name: "${chC4}"
      ch5_name: "${chC5}"
      ch6_name: "${chC6}"
      current_group: "currentC"
      power_group: "powerC"
      energy_group: "energyC"

  phase_c_debug: !include
    file: templates/bl0906_phase_debug_buttons_release.yaml
    vars:
      phase_name: "C"
      phase_lower: "c"

  # 系统监控
  system_monitoring: !include templates/system_monitoring_3phase.yaml 