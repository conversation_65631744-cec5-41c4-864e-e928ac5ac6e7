# BL0906 Factory 版本管理指南

## 概述

BL0906 Factory 组件采用**双版本管理策略**，通过独立文件管理实现生产版和发布版的功能隔离。

## 版本类型

### 发布版（Release Version）
- **文件**: `__init__.py`（默认配置）
- **目标用户**: 最终用户
- **特点**: 
  - ✅ 用户友好的简化配置
  - ✅ 移除校准调试功能（无 number 组件）
  - ✅ 容错运行模式
  - ✅ 只读校准数据
  - ✅ 核心算法保护

### 生产版（Production Version）
- **文件**: `__init_production.py`（完整功能）
- **目标用户**: 开发人员和生产制造
- **特点**:
  - ✅ 完整的校准调试功能
  - ✅ 包含 number 组件
  - ✅ 初始校准值配置
  - ✅ 校准模式开关
  - ✅ 开发调试友好

## 版本差异对比

| 特性 | 发布版 | 生产版 |
|------|--------|--------|
| **校准功能** | 只读模式 | 完整调试功能 |
| **number组件** | ❌ 不包含 | ✅ 包含 |
| **配置复杂度** | 简化配置 | 完整配置 |
| **错误处理** | 容错运行 | 开发调试模式 |
| **使用场景** | 最终用户部署 | 开发制造过程 |
| **算法保护** | 预编译库保护 | 完整源码 |

## 快速使用

### 方法一：使用部署脚本（推荐）

```bash
# 构建发布版（默认）
./deploy.sh

# 构建发布版（显式指定）
./deploy.sh release

# 构建生产版
./deploy.sh production

# 查看帮助
./deploy.sh help
```

### 方法二：使用单独脚本

```bash
# 发布版构建
./build_release.sh

# 生产版构建
./build_production.sh

# 恢复发布版（从生产版切换回来）
./restore_release.sh
```

## 文件结构

```
components/bl0906_factory/
├── __init__.py                 # 发布版配置（默认）
├── __init_production.py        # 生产版配置（完整功能）
├── deploy.sh                   # 通用部署脚本
├── build_release.sh           # 发布版构建脚本
├── build_production.sh        # 生产版构建脚本
├── restore_release.sh         # 恢复发布版脚本
├── VERSION_MANAGEMENT.md      # 本文档
└── ...                        # 其他组件文件
```

## 配置示例

### 发布版配置

```yaml
# 发布版 - 简化且用户友好
external_components:
  - source: github://your-company/bl0906-factory
    components: [bl0906_factory]

bl0906_factory:
  chip_model: bl0906
  communication: uart
  uart_id: uart_bus
  instance_id: 0x12345678
  update_interval: 60s
  
  # 发布版特点：
  # - 不支持 number 组件
  # - 不支持 calibration_mode
  # - 不支持 initial_calibration
  # - 简化的存储配置
```

### 生产版配置

```yaml
# 生产版 - 完整功能
external_components:
  - source: github://your-company/bl0906-factory
    components: [bl0906_factory]

bl0906_factory:
  chip_model: bl0906
  communication: uart
  uart_id: uart_bus
  instance_id: 0x12345678
  update_interval: 60s
  
  # 生产版特有功能：
  calibration_mode: true
  calibration:
    storage_type: preferences
    enabled: true
  
  initial_calibration:
    - register: 0x6D
      value: 16484
    - register: 0x6E
      value: 16484

# 生产版支持 number 组件
number:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_factory_id
    # ... number 组件配置
```

## 安全机制

### 信息隔离
- 发布版配置完全隐藏生产版功能
- 外部用户无法获知生产版特性
- 默认安全：不配置变量即为发布版

### 构建保护
- 自动化脚本处理文件切换
- 构建后自动恢复发布版配置
- 防止误用生产版配置

### 算法保护
- 核心芯片交互算法通过预编译库保护
- ESPHome 集成和存储操作保持开源
- 分层保护策略

## 工作流程

### 开发流程
1. 默认使用发布版配置开发
2. 需要调试时切换到生产版
3. 调试完成后恢复发布版
4. 提交代码时确保为发布版

### 发布流程
1. 确认当前为发布版配置
2. 运行 `./build_release.sh` 验证
3. 构建和测试
4. 发布到用户

### 生产制造流程
1. 切换到生产版：`./build_production.sh`
2. 进行校准和测试
3. 完成后恢复：`./restore_release.sh`
4. 确保不提交生产版配置

## 故障排除

### 常见问题

**Q: 如何确认当前是哪个版本？**
A: 检查 `__init__.py` 文件头部注释：
- 发布版：`"""BL0906 Factory - 发布版电能计量组件"""`
- 生产版：`"""BL0906 Factory - 生产版电能计量组件（完整功能）"""`

**Q: 生产版构建后忘记恢复怎么办？**
A: 运行 `./restore_release.sh` 或手动执行：
```bash
git checkout __init__.py
```

**Q: 脚本执行权限问题？**
A: 添加执行权限：
```bash
chmod +x *.sh
```

**Q: 如何验证版本功能？**
A: 检查 AUTO_LOAD 配置：
- 发布版：`AUTO_LOAD = ["sensor"]`
- 生产版：`AUTO_LOAD = ["sensor", "number"]`

### 错误恢复

如果版本配置出现问题，可以通过以下方式恢复：

```bash
# 恢复发布版配置
git checkout __init__.py

# 如果需要重新创建生产版配置
git show HEAD:components/bl0906_factory/__init__.py > __init_production.py
```

## 最佳实践

1. **默认使用发布版** - 除非明确需要调试功能
2. **及时恢复** - 生产版使用完毕后立即恢复发布版
3. **脚本优先** - 使用自动化脚本而非手动切换
4. **版本验证** - 构建前确认当前版本类型
5. **文档同步** - 修改功能时同步更新两个版本

## 维护指南

### 添加新功能
1. 首先在生产版中实现和测试
2. 评估是否应包含在发布版中
3. 如果是用户功能，同步到发布版
4. 如果是调试功能，仅保留在生产版

### 修复Bug
1. 在对应版本中修复
2. 评估是否影响另一版本
3. 必要时同步修复到两个版本

### 版本同步
定期检查两个版本的一致性，确保：
- 核心功能保持同步
- 发布版不包含调试功能
- 配置验证逻辑一致 