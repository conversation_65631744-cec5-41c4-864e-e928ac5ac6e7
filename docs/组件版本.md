# BL0906 Factory 组件版本管理策略

## 概述

### 问题描述
当前 bl0906_factory 组件为生产版本，包含完整的校准调试功能。需要基于此版本创建发布版本，用于最终用户部署。

### 目标要求
1. **移除校准修改功能** - 去掉 number 组件支持，禁用运行时校准
2. **容错运行模式** - 只从持久化存储读取校准数据，缺失时提示用户但继续运行
3. **算法保护** - 预编译库 + 薄包装层，防止核心算法泄露
4. **简化移植** - 提供快速的版本转换方案

### 当前架构基础
基于已完成的重构成果，bl0906_factory 组件具备：
- **运行时芯片型号支持** - 完全移除条件编译，支持 BL0906/BL0910 混合使用
- **统一配置映射** - 通过 config_mappings.py 消除约 225 行重复代码
- **抽象存储接口** - CalibrationStorageBase 提供统一的 Preferences/EEPROM 存储
- **模块化通信** - UART/SPI 适配器支持不同通信方式

## 版本差异对比

| 特性 | 生产版本 | 发布版本 |
|------|----------|----------|
| **校准功能** | 完整的运行时校准调试 | 只读校准模式 |
| **数据验证** | 宽松验证，便于调试 | 容错运行，缺失时提示 |
| **代码保护** | 完整源码，内部使用 | 预编译库，算法保护 |
| **用户体验** | 开发调试友好 | 简化配置，用户友好 |
| **使用场景** | 生产制造过程校准测试 | 最终用户部署使用 |
| **配置复杂度** | 支持各种校准参数调整 | 简化配置，只保留必要功能 |

## 技术方案

### 推荐方案：独立文件管理

```
组件目录结构：
components/bl0906_factory/
├── __init__.py              # 发布版配置（默认）
├── __init_production.py     # 生产版配置（完整功能）
├── bl0906_factory.cpp       # 共享核心实现
├── bl0906_factory.h         # 共享核心头文件
└── ...
```

**优势**：
- 完全信息隔离，发布版无法获知生产版功能
- 默认安全，不配置变量即为发布版
- 构建自动化，避免人为错误
- 清晰的版本边界

### 实现策略

#### 1. 独立文件管理（推荐）
```
components/bl0906_factory/
├── __init__.py              # 发布版配置（默认）
├── __init_production.py     # 生产版配置（完整功能）
├── bl0906_factory.cpp       # 共享核心实现
├── bl0906_factory.h         # 共享核心头文件
└── ...
```

**文件切换机制**：
```bash
# 默认为发布版
# 无需任何配置，__init__.py 即为发布版

# 切换到生产版
export BL0906_PRODUCTION_MODE=1
# 或在构建脚本中自动切换文件
```

#### 2. 发布版 __init__.py（精简版）
```python
# __init__.py - 发布版配置
"""BL0906 Factory - 发布版电能计量组件"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, i2c, spi, sensor
from esphome import pins

# 发布版只支持传感器
AUTO_LOAD = ["sensor"]  # 不包含number组件
DEPENDENCIES = []
CODEOWNERS = ["@carrot8848"]
MULTI_CONF = True

# 发布版类声明 - 不包含number组件
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Factory = bl0906_factory_ns.class_("BL0906Factory", cg.PollingComponent)

# 发布版配置 - 简化的配置项
CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Optional("chip_model", default="bl0906"): cv.enum({"bl0906": "bl0906", "bl0910": "bl0910"}),
    cv.Required("communication"): cv.enum({"uart": "uart", "spi": "spi"}),
    cv.Optional("update_interval", default="60s"): cv.update_interval,
    cv.Required("instance_id"): cv.hex_uint32_t,
    # 只支持传感器配置相关选项
    # 不包含校准调试相关配置
}).extend(cv.polling_component_schema("60s"))

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    
    # 发布版标识
    cg.add_define("BL0906_PRODUCTION_BUILD")
    cg.add_define("BL0906_READONLY_CALIBRATION")
    cg.add_define("BL0906_FAULT_TOLERANT")
    
    # 设置基础配置
    cg.add(var.set_chip_model(config["chip_model"]))
    cg.add(var.set_instance_id(config["instance_id"]))
    
    # 通信配置（简化版）
    # ... 基础通信配置
    
    # 持久化存储配置（薄包装层实现）
    # 注意：校准数据的持久化存储在薄包装层实现
    # 预编译库只负责与BL0906芯片的直接交互
    if "calibration_storage" in config:
        storage_type = config["calibration_storage"].get("type", "preferences")
        cg.add(var.set_calibration_storage_type(storage_type))
```

#### 3. 生产版 __init_production.py（完整版）
```python
# __init_production.py - 生产版配置
"""BL0906 Factory - 生产版电能计量组件"""
# 完整的功能配置，包含number组件和所有调试功能
AUTO_LOAD = ["sensor", "number"]

# 完整的类声明
BL0906Number = bl0906_factory_ns.class_("BL0906Number", number.Number, cg.Component)

# 完整的配置架构 - 包含所有调试和校准功能
CONFIG_SCHEMA = cv.Schema({
    # 包含所有生产版功能
    cv.Optional("calibration_mode", default=False): cv.boolean,
    cv.Optional("calibration"): CALIBRATION_SCHEMA,
    cv.Optional("initial_calibration"): INITIAL_CALIBRATION_SCHEMA,
    # ... 完整的生产版配置
})
```

#### 4. 构建脚本自动切换
```bash
# build_release.sh - 发布版构建
#!/bin/bash
echo "构建发布版..."
# 使用默认的 __init__.py（发布版）
cp __init__.py __init_active.py
pio run --environment release

# build_production.sh - 生产版构建  
#!/bin/bash
echo "构建生产版..."
# 切换到生产版配置
cp __init_production.py __init__.py
pio run --environment production
# 构建完成后恢复
git checkout __init__.py
```

### 预编译库架构

```
发布版组件架构：
┌─────────────────────────────────────┐
│  ESPHome YAML 配置层                │
├─────────────────────────────────────┤
│  薄层包装 (开源)                    │
│  - __init__.py                      │
│  - bl0906_release.h/cpp             │
│  - sensor.py (简化版)               │
├─────────────────────────────────────┤
│  预编译库 (闭源保护)                │
│  - libbl0906_core.a                 │
│  - bl0906_core_api.h                │
└─────────────────────────────────────┘
```

#### 核心API接口设计
```cpp
// bl0906_core_api.h - 预编译库接口
typedef struct {
    bool success;
    const char* error_message;
} bl0906_result_t;

// 核心API函数 - 直接与BL0906芯片交互
bl0906_result_t bl0906_init_component(void);
bl0906_result_t bl0906_read_sensor_data(uint8_t channel, float* voltage, float* current, float* power);
bl0906_result_t bl0906_convert_raw_to_value(uint8_t address, int32_t raw_value, float* result);

// BL0906寄存器操作
bl0906_result_t bl0906_read_register(uint8_t reg, int32_t* value);
bl0906_result_t bl0906_write_register(uint8_t reg, int16_t value);
bl0906_result_t bl0906_write_calibration_register(uint8_t reg, int16_t value);

// 校准算法计算
bl0906_result_t bl0906_calculate_calibration_coefficient(uint32_t raw_value, float reference, float* coefficient);
bl0906_result_t bl0906_apply_calibration_to_chip(uint8_t reg, float coefficient);

// 注意：预编译库不包含持久化存储相关API
// 持久化存储由薄包装层通过ESPHome的Preferences/EEPROM实现
```

## 保护机制

### 基础保护策略

#### 预编译库保护

**包含的核心算法**：
- BL0906芯片寄存器读写操作
- 校准寄存器写入算法
- 原始数据转换算法
- 校准系数计算算法
- 芯片通信协议实现

**不包含的ESPHome功能**：
- 持久化存储操作（Preferences/EEPROM）
- ESPHome组件生命周期管理
- YAML配置解析
- ESPHome特定的UI逻辑

**保护边界**：
- 薄包装层开源，负责ESPHome集成
- 预编译库闭源，保护芯片交互算法

#### 简化部署
- 无需复杂授权验证
- 专注于核心功能保护
- 用户友好的配置体验

## 用户指南

### 版本使用说明

#### 发布版使用（默认）
发布版是默认配置，无需任何特殊设置：

```yaml
# 发布版配置 - 简化且受限
external_components:
  - source: github://your-company/bl0906-factory
    components: [bl0906_factory]

bl0906_factory:
  chip_model: bl0906
  communication: uart
  instance_id: 0x12345678
  
  # 发布版只支持传感器配置
  # 不包含number组件（校准调试功能）
```

#### 生产版使用（内部）
生产版需要特殊构建流程：

```bash
# 构建生产版
./build_production.sh

# 或者手动切换
cp __init_production.py __init__.py
pio run --environment production
git checkout __init__.py  # 构建后恢复
```

### 发布版配置示例

```yaml
# 发布版 - 完整配置示例
external_components:
  - source: github://your-company/bl0906-factory
    components: [bl0906_factory]

bl0906_factory:
  chip_model: bl0906
  communication: uart
  uart_id: uart_bus
  instance_id: 0x12345678
  update_interval: 60s
  
  # 注意：发布版不支持以下功能
  # - number组件（校准调试）
  # - calibration_mode
  # - initial_calibration
  # - 其他生产版专用功能
```

### 关键特性

1. **完全去除number组件** - 发布版不包含任何校准调整功能
2. **容错运行模式** - 无校准数据时使用默认参数继续工作
3. **实时提示机制** - 每次发布传感器数据时提醒用户"未找到校准数据，传感器精度较低"
4. **分层算法保护** - 核心芯片交互算法在预编译库中保护
5. **持久化存储开源** - ESPHome的Preferences/EEPROM操作在薄包装层开源
6. **配置简化** - 无需授权密钥，配置更简洁
7. **用户友好** - 即使未校准也能正常使用，只是精度降低

### 职责分工

**预编译库（闭源保护）**：
- BL0906芯片寄存器读写
- 校准寄存器写入算法
- 原始数据转换算法
- 校准系数计算算法

**薄包装层（开源）**：
- ESPHome组件集成
- 持久化存储操作
- 配置解析和验证
- 错误处理和日志

### 接口调用示例

```cpp
// 薄包装层 - 校准数据加载示例
void BL0906Factory::load_calibration_data() {
    // 1. 从ESPHome存储读取校准数据（开源代码）
    std::vector<CalibrationEntry> entries;
    if (calibration_storage_->read_instance(instance_id_, entries)) {
        
        // 2. 调用预编译库写入校准寄存器（闭源保护）
        for (const auto& entry : entries) {
            bl0906_result_t result = bl0906_write_calibration_register(
                entry.register_addr, entry.value);
            
            if (!result.success) {
                ESP_LOGW(TAG, "写入校准寄存器失败: %s", result.error_message);
            }
        }
    } else {
        ESP_LOGW(TAG, "未找到校准数据，传感器精度较低");
    }
}

// 薄包装层 - 数据读取示例
void BL0906Factory::publish_sensor_data() {
    float voltage, current, power;
    
    // 调用预编译库读取和转换数据（闭源保护）
    bl0906_result_t result = bl0906_read_sensor_data(0, &voltage, &current, &power);
    
    if (result.success) {
        // ESPHome传感器发布（开源代码）
        if (voltage_sensor_) voltage_sensor_->publish_state(voltage);
        if (current_sensor_) current_sensor_->publish_state(current);
        if (power_sensor_) power_sensor_->publish_state(power);
    } else {
        ESP_LOGW(TAG, "读取传感器数据失败: %s", result.error_message);
    }
}
```

### 生产版vs发布版配置对比

| 配置项 | 生产版 | 发布版 |
|--------|--------|--------|
| **校准组件** | 支持 number 组件 | 不支持 |
| **配置复杂度** | 复杂，支持各种参数 | 简化，只保留必要项 |
| **错误处理** | 宽松，便于调试 | 容错，用户友好提示 |

## 实施计划

### 阶段性开发路线图

#### 第一阶段：文件版本控制
- [ ] 创建发布版 `__init__.py`（默认，精简功能）
- [ ] 创建生产版 `__init_production.py`（完整功能）
- [ ] 实现文件自动切换机制
- [ ] 移除发布版的 number 组件支持
- [ ] 实现容错的校准数据处理

#### 第二阶段：核心算法保护
- [ ] 提取核心算法到独立模块
- [ ] 创建预编译库构建脚本
- [ ] 实现薄包装层接口
- [ ] 测试发布版功能完整性

#### 第三阶段：构建自动化
- [ ] 创建发布版构建脚本
- [ ] 创建生产版构建脚本
- [ ] 实现自动化部署脚本
- [ ] 完善错误提示信息
- [ ] 编写用户使用文档

### 构建自动化

#### 发布版构建（默认）
```bash
# build_release.sh
#!/bin/bash
echo "构建 BL0906 发布版..."

# 确保使用发布版配置
if [ ! -f "__init__.py" ] || [ -f "__init_production.py" ]; then
    echo "使用默认发布版配置"
fi

# 设置构建环境
export BUILD_TYPE=release
export CPPFLAGS="-DBL0906_PRODUCTION_BUILD -DBL0906_READONLY_CALIBRATION -DBL0906_FAULT_TOLERANT"

# 构建预编译库
cd core && make clean && make release
cp libbl0906_core.a ../release/

# 构建发布版包
cd ../release && python3 build_package.py

echo "发布版构建完成"
```

#### 生产版构建
```bash
# build_production.sh
#!/bin/bash
echo "构建 BL0906 生产版..."

# 备份当前配置
cp __init__.py __init_backup.py

# 切换到生产版配置
if [ -f "__init_production.py" ]; then
    cp __init_production.py __init__.py
    echo "已切换到生产版配置"
else
    echo "错误：未找到生产版配置文件"
    exit 1
fi

# 设置构建环境
export BUILD_TYPE=production
export CPPFLAGS="-DBL0906_DEVELOPMENT_BUILD"

# 构建完整功能版本
pio run --environment production

# 恢复发布版配置
cp __init_backup.py __init__.py
rm __init_backup.py

echo "生产版构建完成，已恢复发布版配置"
```

#### 自动化部署脚本
```bash
# deploy.sh
#!/bin/bash
VERSION_TYPE=${1:-release}

case $VERSION_TYPE in
    "release")
        ./build_release.sh
        echo "发布版构建完成，配置已隐藏生产版功能"
        ;;
    "production")
        ./build_production.sh
        echo "生产版构建完成，包含完整调试功能"
        ;;
    *)
        echo "用法: ./deploy.sh [release|production]"
        echo "默认构建发布版"
        ./build_release.sh
        ;;
esac
```

### 维护策略

1. **文件版本管理** - 独立文件隔离版本功能，默认发布版隐藏生产版信息
2. **构建自动化** - 使用构建脚本自动切换配置文件和恢复
3. **测试覆盖** - 单元测试覆盖生产版和发布版逻辑
4. **文档同步** - 版本差异说明文档
5. **持续集成** - 自动化构建和测试流程

#### 文件管理规则
- **默认状态**：仓库中只包含发布版 `__init__.py`
- **生产版配置**：`__init_production.py` 包含完整功能
- **构建隔离**：构建脚本自动处理文件切换和恢复
- **信息隐藏**：发布版配置不包含任何生产版功能提示

## 总结

通过**独立文件管理**和**预编译库保护**的组合方案，可以实现"一套代码，多种版本"的目标，既满足生产版的开发调试需求，又完全隐藏发布版的敏感信息。该方案具有以下特点：

### 核心优势

1. **完全信息隔离** - 发布版配置文件完全隐藏生产版功能，外部用户无法获知生产版特性
2. **容错运行** - 校准数据缺失时继续工作，每次发布传感器数据时提示用户
3. **默认安全** - 不配置任何变量即为发布版，降低误用风险
4. **构建自动化** - 构建脚本自动处理文件切换和恢复，避免人为错误
5. **用户友好** - 简化配置，无需复杂授权验证
6. **分层保护** - BL0906芯片交互算法通过预编译库保护，ESPHome集成开源

### 文件管理架构

```
发布版（默认）：
- __init__.py：精简配置，只包含传感器功能
- 用户看不到任何生产版功能提示
- 自动启用容错模式

生产版（内部）：
- __init_production.py：完整配置，包含所有调试功能
- 构建时自动切换，构建后自动恢复
- 完整的校准和调试能力
```

### 安全保护

- **信息隐藏**：发布版配置不暴露生产版功能
- **默认安全**：不配置变量即为最安全的发布版
- **构建隔离**：生产版构建后自动恢复发布版配置
- **分层保护**：BL0906芯片交互算法通过预编译库保护
- **开放兼容**：ESPHome集成和存储操作保持开源，便于用户集成
