# BL0906 Factory 预编译库集成指南

## 概述

本文档详细说明了如何将 `bl0906_chip_params.h` 和 `bl0906_calibration.h` 的核心功能集成到预编译库中，实现更完整的IP保护和更简洁的薄包装层设计。

## 🎯 集成目标

### 1. **IP保护增强**
- 保护寄存器地址映射算法
- 保护校准系数计算公式
- 保护芯片差异处理逻辑
- 保护电压采样模式算法

### 2. **薄包装层简化**
- 移除复杂的芯片参数管理
- 移除校准系数计算逻辑
- 统一的预编译库接口
- 更清晰的代码结构

## 📋 集成内容

### A. 从 `bl0906_chip_params.h` 迁移的功能

#### 1. **寄存器地址映射**
```cpp
// 预编译库内部实现（隐藏）
static const uint8_t BL0906_I_RMS_ADDRS[] = {0x0D, 0x0E, 0x0F, 0x10, 0x13, 0x14};
static const uint8_t BL0906_WATT_ADDRS[] = {0x23, 0x24, 0x25, 0x26, 0x29, 0x2A};
// ... 完整的地址映射表

// 公开API
bl0906_result_t bl0906_core_get_register_address(
    bl0906_chip_model_t chip_model,
    bl0906_register_type_t reg_type,
    int channel,
    uint8_t* address
);
```

#### 2. **芯片参数查询**
```cpp
// 预编译库API
bl0906_result_t bl0906_core_get_chip_info(
    bl0906_chip_model_t chip_model, 
    bl0906_chip_info_t* info
);

bl0906_result_t bl0906_core_validate_register_address(
    bl0906_chip_model_t chip_model,
    uint8_t address,
    bool* is_valid
);
```

#### 3. **寄存器类型检查**
```cpp
// 预编译库API
bl0906_result_t bl0906_core_is_16bit_register(uint8_t address, bool* is_16bit);
bl0906_result_t bl0906_core_is_unsigned_register(uint8_t address, bool* is_unsigned);
```

### B. 从 `bl0906_calibration.h` 迁移的功能

#### 1. **校准系数计算**
```cpp
// 预编译库内部实现（隐藏）
static void calculate_calibration_coefficients_internal(
    bl0906_voltage_sampling_mode_t sampling_mode,
    const bl0906_reference_params_t* ref_params,
    bl0906_calibration_coefficients_t* coefficients
) {
    if (sampling_mode == BL0906_VOLTAGE_SAMPLING_TRANSFORMER) {
        // 电压互感器采样方式（保护的算法）
        coefficients->Ki = (12875.0f * ref_params->Gain_I * /* 复杂公式 */);
        coefficients->Kv = (13162.0f * ref_params->Gain_V * /* 复杂公式 */);
        // ...
    } else {
        // 电阻分压采样方式（保护的算法）
        coefficients->Ki = 12875.0f * ref_params->Gain_I * /* 复杂公式 */;
        coefficients->Kv = 13162.0f * rv * 1000.0f * /* 复杂公式 */;
        // ...
    }
}

// 公开API
bl0906_result_t bl0906_core_calculate_calibration_coefficients(
    bl0906_voltage_sampling_mode_t sampling_mode,
    const bl0906_reference_params_t* ref_params,
    bl0906_calibration_coefficients_t* coefficients
);
```

#### 2. **默认参数管理**
```cpp
// 预编译库API
bl0906_result_t bl0906_core_get_default_reference_params(
    bl0906_voltage_sampling_mode_t sampling_mode,
    bl0906_reference_params_t* ref_params
);
```

## 🔧 薄包装层更新

### 1. **简化的头文件**
```cpp
// bl0906_wrapper.h (更新后)
#pragma once

#include "esphome/core/component.h"
#include "esphome/components/sensor/sensor.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/spi/spi.h"
#include "bl0906_core_api.h"  // 唯一的预编译库接口

class BL0906FactoryComponent : public Component {
 public:
  // 芯片配置
  void set_chip_model(ChipModel model);
  void set_voltage_sampling_mode(VoltageSamplingMode mode);
  void set_calibration_params(const CalibrationParams &params);
  
  // 校准功能（使用预编译库）
  bool calculate_calibration_coefficients();
  bool apply_calibration_to_chip();
  
  // 工具函数（使用预编译库）
  bool get_register_address(const std::string &reg_type, int channel, uint8_t *address);
  bool validate_register_address(uint8_t address);
  
 private:
  // 移除了复杂的芯片参数管理
  // 移除了校准系数计算逻辑
  // 只保留必要的配置和接口
};
```

### 2. **简化的实现**
```cpp
// bl0906_wrapper.cpp (关键部分)
bool BL0906FactoryComponent::calculate_calibration_coefficients() {
  // 映射参数到预编译库格式
  bl0906_reference_params_t ref_params;
  ref_params.Vref = calib_params_.vref;
  ref_params.Gain_V = calib_params_.gain_v;
  ref_params.Gain_I = calib_params_.gain_i;
  ref_params.RL = calib_params_.rl;
  ref_params.Rt = calib_params_.rt;
  ref_params.Rf = calib_params_.rf;
  ref_params.R46 = calib_params_.r46;
  ref_params.Rv = calib_params_.rv;
  
  // 调用预编译库计算（算法被保护）
  bl0906_calibration_coefficients_t coeffs;
  bl0906_result_t result = bl0906_core_calculate_calibration_coefficients(
      map_voltage_sampling_mode(voltage_sampling_mode_),
      &ref_params,
      &coeffs
  );
  
  if (result == BL0906_SUCCESS) {
    // 保存结果
    calib_coeffs_.ki = coeffs.Ki;
    calib_coeffs_.kv = coeffs.Kv;
    calib_coeffs_.kp = coeffs.Kp;
    calib_coeffs_.ke = coeffs.Ke;
    calib_coeffs_.kp_sum = coeffs.Kp_sum;
    calib_coeffs_.ke_sum = coeffs.Ke_sum;
    return true;
  }
  
  set_error("校准系数计算失败: " + std::string(bl0906_core_get_error_string(result)));
  return false;
}

bool BL0906FactoryComponent::get_register_address(const std::string &reg_type, int channel, uint8_t *address) {
  bl0906_register_type_t type = map_register_type(reg_type);
  bl0906_result_t result = bl0906_core_get_register_address(
      map_chip_model(chip_model_),
      type,
      channel,
      address
  );
  
  return result == BL0906_SUCCESS;
}
```

## 🏗️ 构建流程

### 1. **预编译库构建**
```bash
# 执行扩展的构建脚本
./build_precompiled_lib.sh

# 输出文件
release/
├── libbl0906_core.a          # 预编译库（包含芯片参数和校准算法）
├── bl0906_core_api.h         # 扩展的API接口
├── VERSION                   # 版本信息
└── README.md                 # 使用说明
```

### 2. **薄包装层构建**
```bash
# 薄包装层只需要链接预编译库
# 不再需要编译 bl0906_chip_params.h 和 bl0906_calibration.h
```

## 📊 对比分析

### 集成前 vs 集成后

| 方面 | 集成前 | 集成后 |
|------|--------|--------|
| **IP保护** | 部分保护 | 完全保护 |
| **代码复杂度** | 高（3个头文件） | 低（1个API接口） |
| **编译时间** | 长 | 短 |
| **维护难度** | 高 | 低 |
| **功能完整性** | 完整 | 完整 |
| **性能** | 编译时计算 | 运行时计算 |

### 文件大小对比

| 文件 | 集成前 | 集成后 |
|------|--------|--------|
| **源码文件** | 3个头文件 + 实现 | 1个API头文件 |
| **预编译库** | 基础功能 | 完整功能 |
| **薄包装层** | 复杂实现 | 简化实现 |
| **总体大小** | 较大 | 较小 |

## 🎉 集成优势

### 1. **更强的IP保护**
- 寄存器地址映射算法完全隐藏
- 校准系数计算公式完全保护
- 芯片差异处理逻辑完全保护
- 电压采样模式算法完全保护

### 2. **更简洁的薄包装层**
- 移除了约500行复杂的芯片参数管理代码
- 移除了约200行校准系数计算代码
- 统一的预编译库接口，更易维护
- 更清晰的代码结构和职责分离

### 3. **更好的可维护性**
- 核心算法集中在预编译库中
- 薄包装层只负责ESPHome适配
- 版本管理更简单
- 测试更容易

### 4. **更高的性能**
- 预编译库经过优化编译
- 减少了编译时间
- 运行时性能更好

## 📝 使用示例

### 1. **基本使用**
```cpp
// 创建组件实例
auto component = new BL0906FactoryComponent();

// 设置芯片型号
component->set_chip_model(ChipModel::CHIP_BL0906);

// 设置电压采样模式
component->set_voltage_sampling_mode(VoltageSamplingMode::VOLTAGE_SAMPLING_TRANSFORMER);

// 设置校准参数
CalibrationParams params;
params.vref = 1.097f;
params.rt = 2000.0f;
component->set_calibration_params(params);

// 计算校准系数（使用预编译库）
if (component->calculate_calibration_coefficients()) {
    ESP_LOGI(TAG, "校准系数计算成功");
} else {
    ESP_LOGE(TAG, "校准系数计算失败: %s", component->get_last_error().c_str());
}

// 获取寄存器地址（使用预编译库）
uint8_t address;
if (component->get_register_address("I_RMS", 0, &address)) {
    ESP_LOGI(TAG, "通道0电流寄存器地址: 0x%02X", address);
}
```

### 2. **高级功能**
```cpp
// 验证寄存器地址
if (component->validate_register_address(0x0D)) {
    ESP_LOGI(TAG, "寄存器地址0x0D有效");
}

// 应用校准到芯片
if (component->apply_calibration_to_chip()) {
    ESP_LOGI(TAG, "校准应用成功");
}

// 读取传感器数据
float voltage, current, power, energy;
if (component->read_sensor_data(0, &voltage, &current, &power, &energy)) {
    ESP_LOGI(TAG, "通道0: V=%.2f, I=%.3f, P=%.1f, E=%.1f", voltage, current, power, energy);
}
```

## 🚀 部署建议

### 1. **开发阶段**
- 使用完整源码进行开发和调试
- 验证所有功能正常工作
- 完成充分的测试

### 2. **生产部署**
- 构建预编译库 `./build_precompiled_lib.sh`
- 更新薄包装层使用预编译库
- 删除或隐藏 `bl0906_chip_params.h` 和 `bl0906_calibration.h`
- 部署到生产环境

### 3. **版本管理**
- 预编译库版本独立管理
- 薄包装层版本与预编译库版本对应
- 保持API兼容性

## 📋 总结

通过将 `bl0906_chip_params.h` 和 `bl0906_calibration.h` 集成到预编译库中，我们实现了：

1. **完整的IP保护** - 所有核心算法都被预编译保护
2. **简化的薄包装层** - 移除了复杂的芯片参数和校准逻辑
3. **统一的接口** - 通过预编译库API提供所有功能
4. **更好的维护性** - 代码结构更清晰，职责分离更明确
5. **更高的性能** - 预编译库经过优化，运行更高效

这种设计既保护了关键的知识产权，又提供了简洁易用的开发接口，是商业化ESPHome组件的理想架构。 