# ESP32 Brownout问题解决方案

## 🚨 问题描述

设备出现`Brownout detector was triggered`错误，导致重启循环。

## 🔍 问题原因

### 1. **电源供应不足**
- 电源适配器功率不够
- USB线缆质量差，压降大
- 电脑USB端口供电能力有限

### 2. **瞬时功耗过高**
- WiFi连接时功耗峰值
- 多个组件同时初始化
- 串口通信频繁

### 3. **硬件问题**
- 电源滤波电容老化
- 连接线接触不良
- ESP32模块本身问题

## 🔧 立即解决方案

### **步骤1：改善电源供应**

1. **使用外部电源**：
   ```
   推荐：5V 2A或更高功率的电源适配器
   避免：电脑USB供电（通常只有0.5A）
   ```

2. **更换USB线缆**：
   ```
   使用粗一点的USB线缆（AWG 20或更粗）
   避免过长的线缆（建议1米以内）
   检查线缆接头是否松动
   ```

3. **添加电源滤波**：
   ```
   在VCC和GND之间并联470μF电解电容
   在VCC和GND之间并联100nF陶瓷电容
   ```

### **步骤2：使用紧急低功耗配置**

使用提供的`emergency_low_power_config.yaml`：

```bash
# 备份当前配置
cp 16-ch-monitor-test.yaml 16-ch-monitor-test.yaml.backup

# 使用紧急配置
cp emergency_low_power_config.yaml 16-ch-monitor-test.yaml

# 编译并上传
esphome run 16-ch-monitor-test.yaml
```

### **步骤3：逐步恢复功能**

设备稳定启动后，逐步恢复功能：

1. **首先恢复校准数据**：
   - 点击"Emergency Diagnose NVS"按钮
   - 点击"Emergency Force Recovery"按钮
   - 点击"Emergency Reload Calibration"按钮

2. **然后逐步添加传感器**：
   ```yaml
   # 先添加一个通道
   sensor:
     - platform: bl0906_factory
       bl0906_factory_id: sensor_bl0906
       ch1:
         current:
           name: "CH1 Current"
   ```

3. **最后恢复完整配置**：
   ```bash
   # 恢复原始配置
   cp 16-ch-monitor-test.yaml.backup 16-ch-monitor-test.yaml
   esphome run 16-ch-monitor-test.yaml
   ```

## 🔧 长期解决方案

### **硬件改进**

1. **电源模块升级**：
   ```
   使用LM2596或类似的降压模块
   输入：7-12V DC
   输出：5V 3A
   ```

2. **添加电源指示**：
   ```yaml
   sensor:
     - platform: adc
       pin: A0
       name: "Supply Voltage"
       update_interval: 1s
       filters:
         - multiply: 3.3  # 根据分压电阻调整
   ```

3. **电源监控**：
   ```yaml
   binary_sensor:
     - platform: template
       name: "Power Good"
       lambda: |-
         return id(supply_voltage).state > 4.8;  # 低于4.8V报警
   ```

### **软件优化**

1. **功耗管理**：
   ```yaml
   esphome:
     platformio_options:
       board_build.f_cpu: 160000000L  # 降低CPU频率
   
   wifi:
     power_save_mode: HIGH
     output_power: 10dB
   ```

2. **启动延迟**：
   ```yaml
   esphome:
     on_boot:
       priority: 600
       then:
         - delay: 5s  # 启动延迟，让电源稳定
   ```

3. **看门狗优化**：
   ```yaml
   logger:
     level: WARN  # 减少串口输出
   
   api:
     reboot_timeout: 0s  # 禁用API重启超时
   ```

## 📊 诊断工具

### **电压监控**
```yaml
sensor:
  - platform: internal_temperature
    name: "ESP32 Temperature"
  
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s
```

### **重启计数器**
```yaml
sensor:
  - platform: template
    name: "Boot Count"
    lambda: |-
      static int boot_count = 0;
      return ++boot_count;
```

## 🎯 预防措施

1. **定期检查**：
   - 监控电源电压
   - 检查温度变化
   - 观察WiFi信号强度

2. **备用方案**：
   - 准备备用电源
   - 保留紧急配置文件
   - 定期备份校准数据

3. **环境控制**：
   - 避免高温环境
   - 确保良好散热
   - 防止电磁干扰

## 📝 故障排除检查清单

- [ ] 电源适配器功率≥2A
- [ ] USB线缆质量良好
- [ ] 连接稳固无松动
- [ ] 环境温度正常
- [ ] WiFi信号强度良好
- [ ] 代码中无死循环
- [ ] 更新间隔合理
- [ ] 日志级别适当

## 🆘 紧急联系

如果问题持续存在：
1. 检查硬件连接
2. 尝试不同的电源
3. 使用最小化配置
4. 考虑硬件故障可能性
