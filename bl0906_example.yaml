esphome:
  name: bl0906-test
  platform: ESP32
  board: esp32dev

# 配置串口用于日志输出
logger:
  level: INFO

# Wi-Fi配置
wifi:
  ssid: "YourSSID"
  password: "YourPassword"

# 启用Home Assistant API
api:

# 启用OTA
ota:

# 定义UART组件
uart:
  id: uart_bus
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 9600

# BL0906 Factory配置（默认配置）
bl0906_factory:
  # chip_model: bl0906  # 默认值，可省略
  communication: uart
  uart_id: uart_bus
  instance_id: 0x12345678
  update_interval: 30s
  calibration_mode: false
  freq_adapt: auto
  voltage_sampling_mode: transformer
  
  # 校准配置（可选）
  calibration:
    storage_type: preference

# 传感器配置 - BL0906支持1-6通道
sensor:
  - platform: bl0906_factory
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    
    # 电流传感器 - BL0906支持6个通道
    current:
      - channel: 1
        name: "Current Ch1"
      - channel: 2
        name: "Current Ch2"
      - channel: 3
        name: "Current Ch3"
      - channel: 4
        name: "Current Ch4"
      - channel: 5
        name: "Current Ch5"
      - channel: 6
        name: "Current Ch6"
    
    # 功率传感器 - BL0906支持6个通道
    power:
      - channel: 1
        name: "Power Ch1"
      - channel: 2
        name: "Power Ch2"
      - channel: 3
        name: "Power Ch3"
      - channel: 4
        name: "Power Ch4"
      - channel: 5
        name: "Power Ch5"
      - channel: 6
        name: "Power Ch6"
    
    # 电量传感器
    energy:
      - channel: 1
        name: "Energy Ch1"
      - channel: 2
        name: "Energy Ch2"
      - channel: 3
        name: "Energy Ch3"
      - channel: 4
        name: "Energy Ch4"
      - channel: 5
        name: "Energy Ch5"
      - channel: 6
        name: "Energy Ch6"
    
    # 总和传感器
    power_sum:
      name: "Total Power"
    energy_sum:
      name: "Total Energy"

# 校准数字组件（可选）
number:
  - platform: bl0906_factory
    # 各通道电流增益校准
    chgn:
      - channel: 1
        name: "Current Gain Ch1"
      - channel: 2
        name: "Current Gain Ch2"
      - channel: 3
        name: "Current Gain Ch3"
      - channel: 4
        name: "Current Gain Ch4"
      - channel: 5
        name: "Current Gain Ch5"
      - channel: 6
        name: "Current Gain Ch6"
    
    # 电压增益校准
    chgn_v:
      name: "Voltage Gain"

# 诊断信息
text_sensor:
  - platform: template
    name: "Chip Model"
    lambda: |-
      return {"BL0906"};
  - platform: template
    name: "Max Channels"
    lambda: |-
      return {"6"}; 