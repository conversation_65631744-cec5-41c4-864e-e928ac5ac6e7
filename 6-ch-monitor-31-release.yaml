substitutions:
  name: "6-ch-monitor-30-release"
  friendly_name: "6-ch-monitor-30-release"
  ch1: "ch_1"   #可根据需要修改名称
  ch2: "ch_2"   #可根据需要修改名称
  ch3: "ch_3"    #可根据需要修改名称
  ch4: "ch_4"    #可根据需要修改名称
  ch5: "ch_5"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
esphome:
  name: "${name}"
  name_add_mac_suffix: False
  friendly_name: "${friendly_name}"
  project:
    name: carrot8848.6-ch-monitor
    version: "3.1"
esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino
# Enable logging
logger:
  hardware_uart: UART0
  level: verbose
  # 增加SPI相关的调试信息
  # logs:
  #   sensor: warn

# Enable Home Assistant API
api:
  reboot_timeout: 0s
# 启用preferences组件用于数据持久化
preferences:
  flash_write_interval: 1min  # 每分钟写入一次flash，平衡数据安全和flash寿命
ota:
  - platform: esphome
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  power_save_mode: none
  manual_ip:
    static_ip: ************
    gateway: *************
    subnet: *************
  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    # ssid: "${name}"
    password: ""
debug:
  update_interval: 5s

captive_portal:
external_components:
  - source:
      type: local
      path: "./components/release/"
    refresh: 0s
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: current
      name: "Current"
      sorting_weight: 20
    - id: power
      name: "Power"
      sorting_weight: 30
    - id: energy
      name: "Energy"
      sorting_weight: 40
    - id: energy_stats
      name: "Energy Status"
      sorting_weight: 50
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 60


status_led:
  pin: GPIO2
uart:
  # - id: uart_bus2
  #   rx_pin: 5
  #   tx_pin: 4
  #   baud_rate: 115200
  # - id: uart_bus
  #   rx_pin: 6
  #   tx_pin: 7
  #   baud_rate: 19200
  # - id: uart_bus3
  #   rx_pin: 20
  #   tx_pin: 21
  #   baud_rate: 115200
i2c:
  sda: 10
  scl: 18
  scan: true
  frequency: 400kHz
  id: i2c_bus

spi:
  - id: spi_bus
    mosi_pin: 6
    miso_pin: 7
    clk_pin: 19
bl0906_factory:
    communication: spi
    spi_id: spi_bus
    cs_pin: 3
    id: sensor_bl0906
    update_interval: 60s
    instance_id: 0x906B0001  # 手动指定实例ID
    eeprom_type: 24c02
    i2c_id: i2c_bus
    i2c_address: 0x50
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    
    # 全局传感器
    frequency:
      name: 'BL0906 Frequency'
      icon: "mdi:sine-wave"
    temperature:
      name: 'BL0906 Temperature'
      icon: "mdi:thermometer"
    voltage:
      name: 'BL0906 Voltage'
      id: bl0906_voltage
      icon: "mdi:lightning-bolt-outline"
      accuracy_decimals: 3
      filters: 
        - sliding_window_moving_average: 
            window_size: 10
            send_every: 1
            send_first_at: 1
    
    # 总和传感器
    power_sum:
      name: "6-ch sum power"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: power
    energy_sum:
      name: "6-chs sum energy"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: energy
      accuracy_decimals: 6
      unit_of_measurement: kWh

    # 总电量统计传感器
    # today_total_energy:
    #   name: "Today Total Energy"
    #   icon: "mdi:calendar-today"
    #   unit_of_measurement: kWh
    #   device_class: energy
    #   accuracy_decimals: 3
    #   web_server:
    #       sorting_group_id: energy_stats
    # yesterday_total_energy:
    #   name: "Yesterday Total Energy"
    #   icon: "mdi:calendar-minus"
    #   unit_of_measurement: kWh
    #   device_class: energy
    #   accuracy_decimals: 3
    #   web_server:
    #       sorting_group_id: energy_stats
    # week_total_energy:
    #   name: "Week Total Energy"
    #   icon: "mdi:calendar-week"
    #   unit_of_measurement: kWh
    #   device_class: energy
    #   accuracy_decimals: 3
    #   web_server:
    #       sorting_group_id: energy_stats
    # month_total_energy:
    #   name: "Month Total Energy"
    #   icon: "mdi:calendar-month"
    #   unit_of_measurement: kWh
    #   device_class: energy
    #   accuracy_decimals: 3
    #   web_server:
    #       sorting_group_id: energy_stats
    # year_total_energy:
    #   name: "Year Total Energy"
    #   icon: "mdi:calendar"
    #   unit_of_measurement: kWh
    #   device_class: energy
    #   accuracy_decimals: 3
    #   web_server:
    #       sorting_group_id: energy_stats

    # 通道1配置
    ch1:
      current:
        name: "${ch1} current"
        id: ch1_current
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
        filters: 
          - sliding_window_moving_average: 
              window_size: 10
              send_every: 1
              send_first_at: 1
      power:
        name: "${ch1} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch1_energy
        name: "${ch1} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch1} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH1 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH1 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH1 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH1 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH1 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道2配置
    ch2:
      current:
        name: "${ch2} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch2} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch2_energy
        name: "${ch2} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch2} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH2 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH2 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH2 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH2 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH2 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道3配置
    ch3:
      current:
        name: "${ch3} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch3} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch3_energy
        name: "${ch3} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch3} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH3 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH3 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH3 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH3 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH3 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道4配置
    ch4:
      current:
        name: "${ch4} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch4} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch4_energy
        name: "${ch4} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch4} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH4 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH4 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH4 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH4 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH4 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道5配置
    ch5:
      current:
        name: "${ch5} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch5} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch5_energy
        name: "${ch5} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch5} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH5 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH5 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH5 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH5 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH5 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats

    # 通道6配置
    ch6:
      current:
        name: "${ch6} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch6} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch6_energy
        name: "${ch6} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch6} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      # today_energy:
      #   name: "CH6 Today Energy"
      #   icon: "mdi:calendar-today"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # yesterday_energy:
      #   name: "CH6 Yesterday Energy"
      #   icon: "mdi:calendar-minus"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # week_energy:
      #   name: "CH6 Week Energy"
      #   icon: "mdi:calendar-week"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # month_energy:
      #   name: "CH6 Month Energy"
      #   icon: "mdi:calendar-month"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
      # year_energy:
      #   name: "CH6 Year Energy"
      #   icon: "mdi:calendar"
      #   unit_of_measurement: kWh
      #   device_class: energy
      #   accuracy_decimals: 3
      #   web_server:
      #       sorting_group_id: energy_stats
  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"

  - platform: debug
    free:
      name: "Heap Free"
    block:
      name: "Heap Max Block"
    loop_time:
      name: "Loop Time"
    cpu_frequency:
      name: "CPU Frequency"
switch:
  - platform: restart
    name: "${name} controller Restart"
  - platform: factory_reset
    name: Restart with Factory Default Settings

time:
  - platform: sntp
    id: my_time
    servers: ntp.aliyun.com
text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
    ssid:
      name: Energy_meter Connected SSID
    bssid:
      name: Energy_meter Connected BSSID
    mac_address:
      name: Energy_meter Mac Wifi Address
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address 
  - platform: debug
    device:
      name: "Device Info"
    reset_reason:
      name: "Reset Reason"
    
  # 电量持久化开关
  # - platform: template
  #   name: "Energy Persistence"
  #   icon: "mdi:database"
  #   # optimistic: true
  #   # restore_mode: RESTORE_DEFAULT_ON
  #   turn_on_action:
  #     - lambda: |-
  #         auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
  #         bl0906->set_energy_persistence_enabled(true);
  #         ESP_LOGI("main", "电量持久化存储已启用");
  #   turn_off_action:
  #     - lambda: |-
  #         auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
  #         bl0906->set_energy_persistence_enabled(false);
  #         ESP_LOGI("main", "电量持久化存储已禁用");