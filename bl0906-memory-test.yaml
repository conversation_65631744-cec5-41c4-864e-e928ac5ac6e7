esphome:
  name: "bl0906-memory-test"
  friendly_name: "BL0906 Memory Test"

preferences:
  flash_write_interval: 10min

esp32:
  board: esp32dev
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG
  logs:
    sensor: WARN

# Enable Home Assistant API
api:
  encryption:
    key: "c8OIaeVFYiA5olgZlIJnxVjWeIISZb2l5SKs6nUI1fE="

ota:
  - platform: esphome
  
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  ap:
    ssid: "BL0906-Memory-Test"
    password: ""

captive_portal:

external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s

status_led:
  pin: 
    number: GPIO13
    inverted: true

uart:
  - id: uart_bus1
    rx_pin: 33
    tx_pin: 32
    baud_rate: 19200

time:
  - platform: sntp
    id: my_time
    servers:
      - ntp.aliyun.com

bl0906_factory:
  - id: sensor_bl0906
    chip_model: bl0906
    communication: uart
    uart_id: uart_bus1
    update_interval: 10s
    instance_id: 0x906B0001
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: preference
    energy_statistics:
      enabled: true
      time_id: my_time

sensor:
  - platform: debug
    free:
      name: "Heap Free"
    block:
      name: "Heap Max Block"
    loop_time:
      name: "Loop Time"

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    # 基本传感器
    frequency:
      name: 'BL0906 Frequency'
    temperature:
      name: 'BL0906 Temperature'
    voltage:
      name: 'BL0906 Voltage'
    
    # 总和传感器
    power_sum:
      name: "6-ch sum power"
    energy_sum:
      name: "6-chs sum energy"
      accuracy_decimals: 2
      unit_of_measurement: kWh

    # 只配置前3个通道以节省内存
    ch1:
      current:
        name: "CH1 current"
      power:
        name: "CH1 power"
      energy:
        name: "CH1 energy"
        accuracy_decimals: 2
        unit_of_measurement: kWh
        state_class: total_increasing

    ch2:
      current:
        name: "CH2 current"
      power:
        name: "CH2 power"
      energy:
        name: "CH2 energy"
        accuracy_decimals: 2
        unit_of_measurement: kWh

    ch3:
      current:
        name: "CH3 current"
      power:
        name: "CH3 power"
      energy:
        name: "CH3 energy"
        accuracy_decimals: 2
        unit_of_measurement: kWh

debug:
  update_interval: 5s

switch:
  - platform: restart
    name: "Restart"
  - platform: factory_reset
    name: "Factory Reset"

# 校准数字组件（仅配置几个关键的）
number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    current_1_gain:
      name: "通道1电流增益"
      min_value: -32768
      max_value: 32767
      step: 1
    current_1_offset:
      name: "通道1电流偏置"
      min_value: -32768
      max_value: 32767
      step: 1

# 校准保存按钮
button:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    save_calibration:
      name: "保存校准数据"
