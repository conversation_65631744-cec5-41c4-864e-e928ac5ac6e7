---
description:
globs:
alwaysApply: false
---
# ESPHome测试和配置模式

## 测试配置结构
所有测试配置文件位于[tests/](mdc:tests/)目录，遵循特定的命名和组织约定。

## 配置文件类型

### 主要测试配置
- **多通道监控**: `*-ch-monitor-*.yaml` 格式的文件用于不同通道数的监控配置
  - [10-ch-monitor-test3.yaml](mdc:tests/10-ch-monitor-test3.yaml) - 10通道测试配置
  - [16-ch-monitor-home.yaml](mdc:tests/16-ch-monitor-home.yaml) - 16通道家用配置
  - [6-ch-monitor-eeprom.yaml](mdc:tests/6-ch-monitor-eeprom.yaml) - 6通道EEPROM存储配置

### 校准配置
- **校准文件**: `*-calib.yaml` 格式包含校准参数
  - [6-ch-monitor-20-calib.yaml](mdc:tests/6-ch-monitor-20-calib.yaml) - 20A校准配置
  - [6-ch-monitor-30-calib.yaml](mdc:tests/6-ch-monitor-30-calib.yaml) - 30A校准配置

### 包配置系统
[packages/](mdc:tests/packages/)目录包含可重用的配置包:
- [bl0906_factory_6ch_calib.yaml](mdc:tests/packages/bl0906_factory_6ch_calib.yaml) - 6通道工厂校准包
- [bl0906_factory_6ch_spi.yaml](mdc:tests/packages/bl0906_factory_6ch_spi.yaml) - SPI通信配置包
- [bl0910_calib.yaml](mdc:tests/packages/bl0910_calib.yaml) - BL0910校准包

### 模板系统
[templates/](mdc:tests/templates/)目录提供配置模板:
- [bl0906_calibration_template.yaml](mdc:tests/templates/bl0906_calibration_template.yaml) - 校准模板
- [bl0906_channel_template.yaml](mdc:tests/templates/bl0906_channel_template.yaml) - 通道配置模板
- [bl0906_phase_calibration_template.yaml](mdc:tests/templates/bl0906_phase_calibration_template.yaml) - 三相校准模板

## 配置模式

### 通信方式配置
- **UART配置**: 默认通信方式，无需特殊配置
- **SPI配置**: 需要指定CS引脚和SPI总线参数
- **模板化配置**: 使用`!include`和变量替换支持配置复用

### 存储方式配置
- **Preferences存储**: ESPHome内置存储，适用于大多数场景
- **EEPROM存储**: 外部I2C EEPROM，用于需要更大存储空间的场景

### 校准配置模式
- **工厂校准**: 在生产阶段进行的标准校准
- **现场校准**: 安装后根据实际负载进行的精确校准
- **自动校准**: 运行时自动校准功能

## 调试和开发

### 调试配置
- [minimal_debug_config.yaml](mdc:tests/minimal_debug_config.yaml) - 最小调试配置
- [emergency_low_power_config.yaml](mdc:tests/emergency_low_power_config.yaml) - 紧急低功耗配置
- [simplified_test_config.yaml](mdc:tests/simplified_test_config.yaml) - 简化测试配置

### 数据恢复
- [test_data_recovery.yaml](mdc:tests/test_data_recovery.yaml) - 数据恢复测试配置
- [bl0906_data_recovery_button.yaml](mdc:tests/packages/bl0906_data_recovery_button.yaml) - 数据恢复按钮包

## 文档和分析
文档位于[docs/](mdc:tests/docs/)目录:
- [BL0906_Data_Recovery_Analysis.md](mdc:tests/docs/BL0906_Data_Recovery_Analysis.md) - 数据恢复分析
- [Brownout_Problem_Solution.md](mdc:tests/docs/Brownout_Problem_Solution.md) - 掉电问题解决方案
- [Preference_Storage_Simplification.md](mdc:tests/docs/Preference_Storage_Simplification.md) - 存储简化方案

## 测试原则

### 配置验证
- 每个配置文件都应该能够成功编译
- 使用不同的硬件配置测试兼容性
- 包含边界条件和异常情况的测试

### 命名约定
- 文件名应该描述主要功能和配置特点
- 使用一致的命名模式便于识别和组织
- 版本和变体信息包含在文件名中

### 配置管理
- 使用包和模板系统避免重复配置
- 保持配置文件的可读性和可维护性
- 文档化特殊配置的用途和限制
