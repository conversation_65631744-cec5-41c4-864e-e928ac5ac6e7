---
description: 
globs: 
alwaysApply: false
---
# BL0906Factory组件架构指南

## 组件概览
BL0906Factory是一个ESPHome自定义组件，用于BL0906功率监测芯片的集成。组件位于`tests/components/bl0906_factory/`目录。

**组件特点**：
- 总代码量约13万行，包含6个主要子系统
- 支持BL0906/BL0910两种芯片型号
- 实现UART/SPI双通信协议支持
- 提供Preferences/I2C EEPROM双存储方案
- 集成实时能耗统计和校准管理功能

## 完整模块架构说明

### 1. 核心组件层
- **`bl0906_factory.h/cpp`** - 主组件类（708+2269行）
  - 协调所有子系统：通信适配器、存储管理、数据处理
  - 实现ESPHome组件生命周期：setup()、loop()、dump_config()
  - 管理芯片状态机和数据读取流程
  - 提供统一的对外接口

- **`bl0906_chip_params.h`** - 芯片参数定义（437行）
  - BL0906/BL0910芯片寄存器地址映射
  - 校准参数结构定义
  - 通道配置和限制常量
  - 支持运行时芯片型号切换

- **`bl0906_calibration.h`** - 校准数据结构（57行）
  - 校准数据的C++结构体定义
  - 序列化/反序列化接口
  - 校准类型枚举和验证逻辑

### 2. 通信适配器子系统
- **`communication_adapter_interface.h`** - 通信接口（168行）
  - 定义统一的通信协议接口
  - 抽象不同通信方式的差异
  - 提供通用的读写操作接口

- **`communication_adapter_base.h/cpp`** - 通信基类（175+128行）
  - 实现通用的通信逻辑
  - 提供错误处理和重试机制
  - 管理通信状态和性能统计

- **`uart_communication_adapter.h/cpp`** - UART实现（148+389行）
  - UART特定的通信协议实现
  - 波特率配置和数据格式处理
  - UART错误检测和恢复机制

- **`spi_communication_adapter.h/cpp`** - SPI实现（159+514行）
  - SPI特定的通信协议实现
  - CS引脚管理和时序控制
  - SPI传输优化和批量操作

- **`adapter_registry.h/cpp`** - 适配器注册（44+63行）
  - 管理多个通信适配器实例
  - 提供适配器创建和销毁机制
  - 支持动态适配器切换

### 3. 校准存储子系统
- **`calibration_storage_interface.h`** - 存储接口（35行）
  - 定义校准数据存储的统一接口
  - 抽象不同存储介质的差异
  - 提供基本的CRUD操作

- **`calibration_storage_base.h/cpp`** - 存储基类（70+337行）
  - 实现通用的存储逻辑和错误处理
  - 提供数据序列化/反序列化框架
  - 统一错误日志和性能监控

- **`preference_calibration_storage.h/cpp`** - Preferences存储（51+466行）
  - 基于ESPHome Preferences的存储实现
  - 适用于小容量校准数据存储
  - 提供自动备份和完整性检查

- **`i2c_eeprom_calibration_storage.h/cpp`** - I2C EEPROM存储（90+431行）
  - 外部I2C EEPROM的存储实现
  - 支持大容量校准数据存储
  - 实现分页写入和磨损均衡

### 4. 功能扩展模块
- **`energy_statistics_manager.h/cpp`** - 能耗统计（206+596行）
  - 实时能耗计算和统计
  - 历史数据管理和趋势分析
  - 能耗事件检测和告警

- **`bl0906_number.h/cpp`** - 数值控制组件（44+92行）
  - ESPHome Number组件集成
  - 校准参数的运行时调整
  - 数值验证和范围限制

### 5. Python配置子系统
- **`__init__.py`** - 主配置模块（315行）
  - ESPHome组件注册和配置验证
  - C++对象创建和参数设置
  - 依赖关系管理和冲突检测

- **`config_mappings.py`** - 配置映射（380行）
  - 统一的配置映射定义，避免重复
  - 支持模板化配置和继承
  - 配置验证规则和错误提示

- **`sensor.py`** - 传感器配置（98行）
  - 传感器类型定义和配置验证
  - 单位转换和精度设置
  - 传感器状态管理

- **`number.py`** - 数值输入配置（64行）
  - Number组件的配置和验证
  - 数值范围和步长设置
  - 校准参数的用户界面集成

### 6. 文档和分析模块
- **`runtime_chip_support_plan.md`** - 运行时芯片支持（285行）
  - 详细的运行时芯片支持重构计划
  - 代码修改步骤和性能优化策略
  - 测试计划和预期效果分析

- **`performance_analysis.md`** - 性能分析（169行）
  - 组件性能基准测试结果
  - 内存使用情况和优化建议
  - 性能瓶颈识别和解决方案

## 模块依赖关系

### 核心依赖层次
```
bl0906_factory.h/cpp (核心)
├── 通信层：communication_adapter_*
├── 存储层：calibration_storage_*
├── 参数层：bl0906_chip_params.h
├── 数据层：bl0906_calibration.h
├── 统计层：energy_statistics_manager.*
└── 控制层：bl0906_number.*
```

### 配置系统依赖
```
__init__.py (入口)
├── config_mappings.py (核心映射)
├── sensor.py (传感器配置)
└── number.py (数值配置)
```

## 核心架构模式

### 配置映射系统
- **统一配置源**: `config_mappings.py` 集中管理所有配置映射逻辑
- **Python配置文件**: 
  - `__init__.py` - 主要组件配置
  - `sensor.py` - 传感器配置
  - `number.py` - 数值输入配置

### 通信适配器模式
- **基类**: `communication_adapter_base.h/cpp` 提供通用功能
- **接口**: `communication_adapter_interface.h` 定义通信协议
- **具体实现**:
  - `uart_communication_adapter.h/cpp` - UART通信
  - `spi_communication_adapter.h/cpp` - SPI通信
- **注册机制**: `adapter_registry.h/cpp` 管理适配器实例

### 校准存储系统
- **抽象基类**: `calibration_storage_base.h/cpp` 提供通用存储逻辑
- **接口定义**: `calibration_storage_interface.h`
- **具体实现**:
  - `preference_calibration_storage.h/cpp` - Preferences存储
  - `i2c_eeprom_calibration_storage.h/cpp` - I2C EEPROM存储

## 重要设计原则

### 1. 统一配置管理
- 所有配置映射都通过config_mappings.py进行，避免重复定义
- 使用模板化配置系统支持继承和组合模式

### 2. 适配器模式应用
- 通信适配器通过构造函数接受父组件: `UartCommunicationAdapter(uart::UARTComponent *parent)`
- 存储适配器使用模板方法模式，子类只需实现raw_data操作接口
- 统一错误处理通过StorageResult枚举

### 3. 代码复用策略
- 基类抽象通用逻辑，减少重复代码
- 策略模式用于不同的存储和通信方式
- 工厂模式用于适配器创建和管理

### 4. 代码简洁性原则
- 避免包装函数反模式和无意义中间层
- 优先使用直接调用而不是多层封装
- 不保持向后兼容性，可以为了代码简洁性进行激进重构

## 常见修改模式

### 添加新通信方式
1. 继承`CommunicationAdapterBase`
2. 实现`CommunicationAdapterInterface`接口
3. 在`adapter_registry.cpp`中注册新适配器
4. 在`__init__.py`中添加配置支持

### 添加新存储方式
1. 继承`CalibrationStorageBase`
2. 实现纯虚函数`raw_read_data`和`raw_write_data`
3. 在主组件中集成新存储选项

### 扩展传感器类型
1. 在`config_mappings.py`中添加新的传感器映射
2. 更新`sensor.py`中的配置验证器
3. 在C++端添加相应的数据处理逻辑

## 测试和配置文件
- 配置示例在 `tests/` 目录下的各种YAML文件中
- 模板文件位于 `tests/templates/` 目录
- 包配置在 `tests/packages/` 目录

## 性能和分析
- `performance_analysis.md` - 性能分析文档
- `runtime_chip_support_plan.md` - 运行时芯片支持计划









