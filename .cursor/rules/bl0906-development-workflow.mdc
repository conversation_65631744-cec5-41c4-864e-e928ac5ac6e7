---
description: 
globs: 
alwaysApply: false
---
# BL0906组件开发工作流程

## 开发环境设置
这是一个ESPHome自定义组件的开发环境，专门用于BL0906功率监测芯片集成。

## 重构历史和原则
基于之前的重构经验，遵循以下原则:

### 已完成的重构成果
1. **配置映射统一化** - 通过[config_mappings.py](mdc:tests/tests/tests/components/bl0906_factory/config_mappings.py)消除了约225行重复代码
2. **存储接口抽象化** - 通过[calibration_storage_base.h/cpp](mdc:tests/tests/tests/components/bl0906_factory/calibration_storage_base.h)减少了约110行重复代码  
3. **通信适配器修复** - 解决了UART和SPI通信的继承冲突问题

### 代码复用策略
- **DRY原则**: 避免任何形式的代码重复
- **单一职责**: 每个类和模块只负责一个明确的功能
- **开闭原则**: 对扩展开放，对修改关闭
- **依赖注入**: 通过构造函数注入依赖，便于测试和扩展

### 代码简洁性原则
- **避免包装函数**: 禁止创建仅仅return另一个函数的包装函数
- **直接调用**: 优先使用直接调用而不是中间层封装
- **功能合并**: 将相关的小函数合并到调用方，减少不必要的函数分层
- **无冗余代码**: 每行代码都必须有明确的功能价值
- **不保持向后兼容**: 优先代码简洁性，可以破坏向后兼容性进行重构

## 代码组织规范

### C++代码结构
```
组件名称/
├── 接口层/          # *_interface.h 文件
├── 基类层/          # *_base.h/cpp 文件  
├── 实现层/          # 具体实现类
├── 注册管理/        # *_registry.h/cpp 文件
└── 参数配置/        # *_params.h 文件
```

### Python配置结构
- [__init__.py](mdc:tests/tests/tests/components/bl0906_factory/__init__.py) - 主组件配置和验证
- [config_mappings.py](mdc:tests/tests/tests/components/bl0906_factory/config_mappings.py) - 统一配置映射定义
- [sensor.py](mdc:tests/tests/tests/components/bl0906_factory/sensor.py) / [number.py](mdc:tests/tests/tests/components/bl0906_factory/number.py) - 子组件配置

## 开发流程

### 1. 需求分析
- 确定要添加的功能或修复的问题
- 检查现有代码是否有类似实现
- 评估对现有架构的影响

### 2. 设计阶段
- 选择合适的设计模式 (适配器、策略、工厂等)
- 确定接口定义和基类抽象
- 规划配置映射和Python集成

### 3. 实现阶段
- 先实现接口和基类
- 然后实现具体功能类
- 最后集成到主组件和配置系统

### 4. 测试验证
- 创建或更新相应的YAML测试配置
- 验证编译和功能正确性
- 测试向后兼容性

## 常见开发任务

### 添加新传感器类型
1. 在[config_mappings.py](mdc:tests/tests/tests/components/bl0906_factory/config_mappings.py)中添加传感器定义
2. 更新[sensor.py](mdc:tests/tests/tests/components/bl0906_factory/sensor.py)的配置验证
3. 在C++端添加相应的数据处理逻辑
4. 创建测试配置验证功能

### 扩展通信协议
1. 实现[communication_adapter_interface.h](mdc:tests/tests/tests/components/bl0906_factory/communication_adapter_interface.h)接口
2. 继承[communication_adapter_base.h](mdc:tests/tests/tests/components/bl0906_factory/communication_adapter_base.h)基类
3. 在[adapter_registry.cpp](mdc:tests/tests/tests/components/bl0906_factory/adapter_registry.cpp)中注册
4. 在[__init__.py](mdc:tests/tests/tests/components/bl0906_factory/__init__.py)中添加配置支持

### 添加新存储方式
1. 继承[calibration_storage_base.h](mdc:tests/tests/tests/components/bl0906_factory/calibration_storage_base.h)基类
2. 实现`raw_read_data`和`raw_write_data`纯虚函数
3. 处理StorageResult枚举的错误情况
4. 集成到主组件的存储选择逻辑

## 调试和故障排除

### 常见问题模式
- **编译错误**: 通常是头文件包含或命名空间问题
- **运行时错误**: 检查适配器注册和初始化顺序
- **配置错误**: 验证config_mappings.py中的映射定义

### 调试工具和配置
- 使用[minimal_debug_config.yaml](mdc:tests/tests/tests/minimal_debug_config.yaml)进行基础调试
- 启用ESPHome日志级别进行详细跟踪
- 利用[performance_analysis.md](mdc:tests/tests/tests/components/bl0906_factory/performance_analysis.md)进行性能优化

## 版本控制和文档

### 提交规范
- 使用描述性的提交信息
- 将相关的修改grouped在同一个提交中
- 重大重构应该分多个提交进行

### 文档维护
- 重要的架构变更需要更新相关MD文档
- 新功能需要添加配置示例
- 性能优化需要记录在分析文档中

## 测试策略
- **单元测试**: 通过不同的YAML配置验证功能
- **集成测试**: 使用完整的系统配置测试
- **回归测试**: 确保新功能不破坏现有功能
- **性能测试**: 监控内存使用和响应时间


