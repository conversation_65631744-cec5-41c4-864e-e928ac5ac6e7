packages: 
  bl0910: !include packages/bl0910_calib.yaml
  phase_a_CHGN_calc: !include
    file: templates/CHGN_calc.yaml
    vars:
      chip_name: "bl0910"
      chip_id: "sensor_bl0910"
substitutions:
  name: "10-ch-monitor-test3"
  friendly_name: "10-ch-monitor-test3"
  ch1: "ch_1"   #可根据需要修改名称
  ch2: "ch_2"   #可根据需要修改名称
  ch3: "ch_3"    #可根据需要修改名称
  ch4: "ch_4"    #可根据需要修改名称
  ch5: "ch_5"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
  ch7: "ch_7"     #可根据需要修改名称
  ch8: "ch_8"     #可根据需要修改名称
  ch9: "ch_9"     #可根据需要修改名称
  ch10: "ch_10"     #可根据需要修改名称

esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"
  project:
    name: carrot8848.10-ch-monitor
    version: "3.0"
esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG
  logs:
    sensor: WARN
  #   mqtt.component: WARN
  #   mqtt.client: ERROR
# Enable Home Assistant API
api:

ota:
  - platform: esphome
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  manual_ip:
    static_ip: ************
    gateway: *************
    subnet: *************
  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "10-Ch-Electricity-Monitor"
    password: ""

captive_portal:
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    # 校准相关 - 最高优先级
    - id: calibrate
      name: "校准设置"
      sorting_weight: 0
    
    # BL0910 基础传感器
    - id: bl0910_sensors
      name: "BL0910 基础传感器"
      sorting_weight: 10
    
    # BL0910 电流传感器 (CH1-10)
    - id: bl0910_current
      name: "BL0910 电流 (CH1-10)"
      sorting_weight: 20
    
    # BL0910 功率传感器 (CH1-10)
    - id: bl0910_power
      name: "BL0910 功率 (CH1-10)"
      sorting_weight: 30
    
    # BL0910 能耗传感器 (CH1-10)
    - id: bl0910_energy
      name: "BL0910 能耗 (CH1-10)"
      sorting_weight: 40
    
    # BL0910 总能耗传感器 (CH1-10)
    - id: bl0910_total_energy
      name: "BL0910 总能耗 (CH1-10)"
      sorting_weight: 50
    
    # BL0910 上次能耗传感器 (CH1-10)
    - id: bl0910_energy_last
      name: "BL0910 上次能耗 (CH1-10)"
      sorting_weight: 60
    
    # PZEM-004T 传感器 (如果启用)
    - id: pzem_004T
      name: "PZEM-004T 传感器"
      sorting_weight: 70
    
    # 其他杂项传感器
    - id: miscellaneous
      name: "其他传感器"
      sorting_weight: 80
    
    # 系统诊断信息
    - id: diagnostic
      name: "系统诊断"
      sorting_weight: 90
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
  #- source: my_components
  # - source:
  #     type: git
  #     url: https://github.com/carrot8848/ESPHome/
  #     ref: main
  # - source: github://dentra/esphome-components

status_led:
  pin: GPIO1
uart:
  - id: uart_bus
    rx_pin: 4
    tx_pin: 5
    baud_rate: 19200
  - id: uart_bus2
    tx_pin: 6
    rx_pin: 7
    baud_rate: 9600
# modbus:
#   - uart_id: uart_bus2
#     id: mod_bus1

sensor:

  # - platform: pzemac
  #   id: sensor_pzem
  #   update_interval: never
  #   current:
  #     name: "PZEM Current"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   voltage:
  #     name: "PZEM Voltage"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   energy:
  #     name: "PZEM Energy"
  #     id: pzem_energy
  #     filters:
  #       # Multiplication factor from Wh to kWh is 0.001
  #       - multiply: 0.001
  #     unit_of_measurement: kWh
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   power:
  #     name: "PZEM Power"
  #     id: pzemac_power
  #     filters:
  #       # Multiplication factor from W to kW is 0.001
  #      - multiply: 0.001
  #     unit_of_measurement: kW
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   frequency:
  #     name: "PZEM Frequency"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   power_factor:
  #     name: "PZEM Power Factor"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   modbus_id: mod_bus1


  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"
    web_server:
          sorting_group_id: diagnostic

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
    web_server:
          sorting_group_id: diagnostic

switch:
  - platform: restart
    name: "${name} controller Restart"
    web_server:
          sorting_group_id: diagnostic
  - platform: factory_reset
    name: Restart with Factory Default Settings
    web_server:
          sorting_group_id: diagnostic
time:
  - platform: sntp
    id: my_time
    servers: ntp.aliyun.com
text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
    ssid:
      name: Energy_meter Connected SSID
    bssid:
      name: Energy_meter Connected BSSID
    mac_address:
      name: Energy_meter Mac Wifi Address
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address 



