# BL0906 Factory 使用 Preference 存储的配置示例
# 此配置不会编译 I2C EEPROM 相关代码，减少编译时间和二进制大小

esphome:
  name: bl0906-preference-demo
  friendly_name: "BL0906 Preference Storage Demo"

esp32:
  board: esp32dev
  framework:
    type: arduino

# 启用日志
logger:
  level: DEBUG

# 启用 Home Assistant API
api:
  encryption:
    key: "your-encryption-key-here"

ota:
  password: "your-ota-password-here"

wifi:
  ssid: "your-wifi-ssid"
  password: "your-wifi-password"

# UART 配置
uart:
  id: uart_bus
  tx_pin: 17
  rx_pin: 16
  baud_rate: 9600
  stop_bits: 1

# BL0906 Factory 组件配置 - 使用 preference 存储
bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  update_interval: 1s
  instance_id: 0x906B0001
  calibration_mode: true
  
  # 校准配置 - 使用 preference 存储（默认）
  calibration:
    enabled: true
    storage_type: preference    # 使用内置 NVS 存储，不需要外部硬件
    # 注意：不需要配置 eeprom_type，因为使用的是 preference

# 传感器配置
sensor:
  # 基础传感器
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    voltage:
      name: "电压"
      unit_of_measurement: "V"
      accuracy_decimals: 1
    frequency:
      name: "频率"
      unit_of_measurement: "Hz"
      accuracy_decimals: 2
    temperature:
      name: "温度"
      unit_of_measurement: "°C"
      accuracy_decimals: 1
    
    # 通道传感器
    current_1:
      name: "通道1电流"
      unit_of_measurement: "A"
      accuracy_decimals: 3
    power_1:
      name: "通道1功率"
      unit_of_measurement: "W"
      accuracy_decimals: 1
    energy_1:
      name: "通道1电量"
      unit_of_measurement: "kWh"
      accuracy_decimals: 3
    
    current_2:
      name: "通道2电流"
      unit_of_measurement: "A"
      accuracy_decimals: 3
    power_2:
      name: "通道2功率"
      unit_of_measurement: "W"
      accuracy_decimals: 1
    energy_2:
      name: "通道2电量"
      unit_of_measurement: "kWh"
      accuracy_decimals: 3

# 校准数字组件（仅在校准模式下可用）
number:
  # 通道1校准
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    current_1_gain:
      name: "通道1电流增益"
      min_value: -32768
      max_value: 32767
      step: 1
    current_1_offset:
      name: "通道1电流偏置"
      min_value: -32768
      max_value: 32767
      step: 1

# 校准保存按钮（仅在校准模式下可用）
button:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    save_calibration:
      name: "保存校准数据" 