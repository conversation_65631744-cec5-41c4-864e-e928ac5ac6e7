esphome:
  name: bl0906-release-test
  platform: ESP32
  board: esp32dev

# 使用发布版组件
bl0906_factory:
  id: bl0906_release
  chip_model: bl0906
  communication: uart
  uart_id: uart_bus
  voltage_sampling_mode: transformer
  freq_adapt: auto
  energy_persistence: true
  instance_id: 0x12345678

# UART配置
uart:
  id: uart_bus
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 4800
  parity: NONE
  stop_bits: 1

# 传感器配置 - 使用新的嵌套格式
sensor:
  # 全局传感器
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_release
    voltage:
      name: "电压"
      id: voltage_sensor
    frequency:
      name: "频率"  
      id: frequency_sensor
    temperature:
      name: "温度"
      id: temperature_sensor
    power_sum:
      name: "总功率"
      id: power_sum_sensor
    energy_sum:
      name: "总电量"
      id: energy_sum_sensor
    
    # 通道1传感器
    ch1:
      current:
        name: "通道1电流"
        id: ch1_current
      power:
        name: "通道1功率"
        id: ch1_power
      energy:
        name: "通道1电量"
        id: ch1_energy
      total_energy:
        name: "通道1总电量"
        id: ch1_total_energy
    
    # 通道2传感器
    ch2:
      current:
        name: "通道2电流"
        id: ch2_current
      power:
        name: "通道2功率"
        id: ch2_power
      energy:
        name: "通道2电量"
        id: ch2_energy
    
    # 通道3传感器
    ch3:
      current:
        name: "通道3电流"
        id: ch3_current
      power:
        name: "通道3功率"
        id: ch3_power 