# 系统监控模板
# 使用方法:
# packages:
#   system_monitoring: !include templates/system_monitoring.yaml

sensor:
  # 系统状态监控传感器
  - platform: template
    name: "Device Uptime"
    unit_of_measurement: "s"
    accuracy_decimals: 0
    lambda: |-
      return millis() / 1000.0;
    update_interval: 60s

  - platform: template
    name: "Free Heap"
    unit_of_measurement: "bytes"
    accuracy_decimals: 0
    lambda: |-
      return esp_get_free_heap_size();
    update_interval: 60s

text_sensor:
  - platform: template
    name: "System Info"
    icon: "mdi:chip"
    lambda: |-
      return {"Uptime: " + to_string(millis() / 1000) + "s, Free Heap: " + to_string(esp_get_free_heap_size()) + " bytes"};
    update_interval: 60s

switch:
  - platform: gpio
    id: bl0906_nrst
    pin: 1
    name: "BL0906_NRST"
    restore_mode: always_on
    
  - platform: template
    name: "bl0906 update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 update"
      - component.suspend: sensor_bl0906
    turn_on_action:
      - logger.log: "turn on bl0906 update"
      - component.resume: sensor_bl0906

  # 电量持久化开关
  - platform: template
    name: "Energy Persistence"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "电量持久化存储已禁用"); 