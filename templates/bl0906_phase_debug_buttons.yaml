# BL0906相位调试按钮模板
# 用于生成单个相位的所有调试按钮
# 使用方法:
# packages:
#   phase_a_debug: !include
#     file: templates/bl0906_phase_debug_buttons.yaml
#     vars:
#       phase_name: "A"
#       phase_lower: "a"

defaults:
  phase_name: "A"
  phase_lower: "a"

button:
  # 基础操作按钮

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy ${phase_name}"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data ${phase_name}"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data ${phase_name}"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence ${phase_name}"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
      
  - platform: template
    name: "CALCULATE RMSOS ${phase_name}"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906_${phase_lower});
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash ${phase_name}"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Read Calibration Data From Chip ${phase_name}"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          bl0906->refresh_all_calib_numbers();
        }

switch:
  - platform: template
    name: "BL0906 ${phase_name} update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 ${phase_name} update"
      - component.suspend: sensor_bl0906_${phase_lower}
    turn_on_action:
      - logger.log: "turn on bl0906 ${phase_name} update"
      - component.resume: sensor_bl0906_${phase_lower}

  # 电量持久化开关
  - platform: template
    name: "Energy Persistence ${phase_name}"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "${phase_name}相电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "${phase_name}相电量持久化存储已禁用"); 