# BL0906全局传感器模板
# 使用方法:
# packages:
#   global_sensors: !include
#     file: templates/bl0906_global_sensors.yaml
#     vars:
#       bl0906_id: sensor_bl0906

defaults:
  bl0906_id: sensor_bl0906

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: ${bl0906_id}
    
    # 全局传感器
    frequency:
      name: 'BL0906 Frequency'
      icon: "mdi:sine-wave"
    temperature:
      name: 'BL0906 Temperature'
      icon: "mdi:thermometer"
    voltage:
      name: 'BL0906 Voltage'
      id: bl0906_voltage
      icon: "mdi:lightning-bolt-outline"
      accuracy_decimals: 3
      filters: 
        - sliding_window_moving_average: 
            window_size: 10
            send_every: 1
            send_first_at: 1
    
    # 总和传感器
    power_sum:
      name: "6-ch sum power"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: power
    energy_sum:
      name: "6-chs sum energy"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: energy
      accuracy_decimals: 6
      unit_of_measurement: kWh
    total_energy_sum:
      name: "6-ch Sum Total Energy"
      icon: "mdi:sigma"
      unit_of_measurement: kWh
      device_class: energy
      state_class: total_increasing
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy

    # 总电量统计传感器
    today_total_energy:
      name: "Today Total Energy"
      icon: "mdi:calendar-today"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    yesterday_total_energy:
      name: "Yesterday Total Energy"
      icon: "mdi:calendar-minus"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    week_total_energy:
      name: "Week Total Energy"
      icon: "mdi:calendar-week"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    month_total_energy:
      name: "Month Total Energy"
      icon: "mdi:calendar-month"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    year_total_energy:
      name: "Year Total Energy"
      icon: "mdi:calendar"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats

number:
  - platform: bl0906_factory
    bl0906_factory_id: ${bl0906_id}
    
    # 电压校准参数
    chgn_v_decimal:
      name: "Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: power

    chos_v_decimal:
      name: "Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: power 