defaults:
  relay_name: "Relay"
  relay_id: "relay"
  channel_num: "1"
  on_pin: 0
  off_pin: 24
  status_pin: 0
  icon: "mdi:power"
  sorting_group_id: "zone_a_1_8"
  status_group_id: "status_zone_a"

switch:
  # 继电器_开
  - platform: gpio
    name: "${relay_name} ${channel_num} On"
    id: ${relay_id}_${channel_num}_on
    pin: 
      sn74hc595: sn74hc595_hub
      number: ${on_pin}
    interlock: ${relay_id}_${channel_num}_off
    #disabled_by_default: true
    icon: mdi:power-on
    on_turn_on:
      then:
        - delay: 500ms
        - switch.turn_off: ${relay_id}_${channel_num}_on
    web_server:
        sorting_group_id: direct_control
        
  # 继电器_关  
  - platform: gpio
    name: "${relay_name} ${channel_num} Off"    
    id: ${relay_id}_${channel_num}_off
    interlock: ${relay_id}_${channel_num}_on
    pin: 
      sn74hc595: sn74hc595_hub
      number: ${off_pin}
    #disabled_by_default: true
    icon: mdi:power-off
    on_turn_on:
      then:
        - delay: 500ms
        - switch.turn_off: ${relay_id}_${channel_num}_off
    web_server:
        sorting_group_id: direct_control
        
  # 继电器主开关
  - platform: template
    name: "${relay_name} ${channel_num}"
    id: ${relay_id}_${channel_num}
    icon: ${icon}
    lambda: |-
      if (id(${relay_id}_${channel_num}_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ${relay_id}_${channel_num}_on
    turn_off_action:
      - switch.turn_on: ${relay_id}_${channel_num}_off
    web_server:
        sorting_group_id: ${sorting_group_id}

binary_sensor:
  # 继电器状态检测
  - platform: gpio
    pin: 
      sn74hc165: sn74hc165_hub
      number: ${status_pin}
      inverted: true
    id: ${relay_id}_${channel_num}_status
    name: "${relay_name} ${channel_num} Status"
    device_class: power
    icon: mdi:power-plug
    web_server:
      sorting_group_id: ${status_group_id}