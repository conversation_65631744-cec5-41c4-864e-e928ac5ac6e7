# BL0906相位调试按钮模板
# 用于生成单个相位的所有调试按钮
# 使用方法:
# packages:
#   phase_a_debug: !include
#     file: templates/bl0906_phase_debug_buttons.yaml
#     vars:
#       phase_name: "A"
#       phase_lower: "a"

defaults:
  phase_name: "A"
  phase_lower: "a"

button:
  # 基础操作按钮

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy ${phase_name}"
    icon: "mdi:restart"
    web_server:
      sorting_group_id: diagnostic
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data ${phase_name}"
    icon: "mdi:content-save"
    web_server:
      sorting_group_id: diagnostic
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data ${phase_name}"
    icon: "mdi:reload"
    web_server:
      sorting_group_id: diagnostic
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence ${phase_name}"
    icon: "mdi:diagnose"
    web_server:
      sorting_group_id: diagnostic
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
        

  - platform: template
    name: "Read Chip Calibration Registers ${phase_name}"
    icon: "mdi:chip"
    web_server:
      sorting_group_id: diagnostic
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
            if (bl0906 != nullptr) {
              bl0906->read_current_calibration_registers();}
switch:
  # 电量持久化开关
  - platform: template
    name: "Energy Persistence ${phase_name}"
    icon: "mdi:database"
    web_server:
      sorting_group_id: diagnostic
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "${phase_name}相电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_${phase_lower}));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "${phase_name}相电量持久化存储已禁用"); 