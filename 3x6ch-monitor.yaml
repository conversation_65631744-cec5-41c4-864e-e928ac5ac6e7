substitutions:
  name: "3x6-ch-monitor"
  friendly_name: "3x6-ch-monitor"
  chA1: "ch_A1"   #可根据需要修改名称
  chA2: "ch_A2"   #可根据需要修改名称
  chA3: "ch_A3"    #可根据需要修改名称
  chA4: "ch_A4"    #可根据需要修改名称
  chA5: "ch_A5"  #可根据需要修改名称
  chA6: "ch_A6"     #可根据需要修改名称
  chB1: "ch_B1"   #可根据需要修改名称
  chB2: "ch_B2"   #可根据需要修改名称
  chB3: "ch_B3"    #可根据需要修改名称
  chB4: "ch_B4"    #可根据需要修改名称
  chB5: "ch_B5"  #可根据需要修改名称
  chB6: "ch_B6"     #可根据需要修改名称
  chC1: "ch_C1"   #可根据需要修改名称
  chC2: "ch_C2"   #可根据需要修改名称
  chC3: "ch_C3"    #可根据需要修改名称
  chC4: "ch_C4"    #可根据需要修改名称
  chC5: "ch_C5"  #可根据需要修改名称
  chC6: "ch_C6"     #可根据需要修改名称
esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"
preferences:
  flash_write_interval: 30min
esp32:
  board: esp32dev
  framework:
    type: arduino

# Enable logging
logger:
  baud_rate: 0
# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "3X6Ch-Monitor"
    password: ""

captive_portal:
globals:
  - id: chA1_energy_
    type: float
    restore_value: yes
  - id: chA1_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chA2_energy_
    type: float
    restore_value: yes
  - id: chA2_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chA3_energy_
    type: float
    restore_value: yes
  - id: chA3_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chA4_energy_
    type: float
    restore_value: yes
  - id: chA4_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chA5_energy_
    type: float
    restore_value: yes
  - id: chA5_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chA6_energy_
    type: float
    restore_value: yes
  - id: chA6_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chsA_energy_ 
    type: float
    restore_value: yes
  - id: chsA_energy_last   #energy from last power cycle
    type: float
    restore_value: yes
  - id: chB1_energy_
    type: float
    restore_value: yes
  - id: chB1_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chB2_energy_
    type: float
    restore_value: yes
  - id: chB2_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chB3_energy_
    type: float
    restore_value: yes
  - id: chB3_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chB4_energy_
    type: float
    restore_value: yes
  - id: chB4_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chB5_energy_
    type: float
    restore_value: yes
  - id: chB5_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chB6_energy_
    type: float
    restore_value: yes
  - id: chB6_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chsB_energy_ 
    type: float
    restore_value: yes
  - id: chsB_energy_last   #energy from last power cycle
    type: float
    restore_value: yes
  - id: chC1_energy_
    type: float
    restore_value: yes
  - id: chC1_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chC2_energy_
    type: float
    restore_value: yes
  - id: chC2_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chC3_energy_
    type: float
    restore_value: yes
  - id: chC3_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chC4_energy_
    type: float
    restore_value: yes
  - id: chC4_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chC5_energy_
    type: float
    restore_value: yes
  - id: chC5_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chC6_energy_
    type: float
    restore_value: yes
  - id: chC6_energy_last  #energy from last power cycle
    type: float
    restore_value: yes
  - id: chsC_energy_ 
    type: float
    restore_value: yes
  - id: chsC_energy_last   #energy from last power cycle
    type: float
    restore_value: yes
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: power_status
      name: "Power Status"
      sorting_weight: 10
    - id: currentA
      name: "CurrentA"
      sorting_weight: 20
    - id: powerA
      name: "PowerA"
      sorting_weight: 30
    - id: energyA
      name: "EnergyA"
      sorting_weight: 40
    - id: currentB
      name: "CurrentB"
      sorting_weight: 21
    - id: powerB
      name: "PowerB"
      sorting_weight: 31
    - id: energyB
      name: "EnergyB"
      sorting_weight: 41
    - id: currentC
      name: "CurrentC"
      sorting_weight: 22
    - id: powerC
      name: "PowerC"
      sorting_weight: 32
    - id: energyC
      name: "EnergyC"
      sorting_weight: 42
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 50
  
external_components:
  - source: my_components3
  #     type: git
  #     url: https://github.com/carrot8848/ESPHome/
  #     ref: main
  # - source: github://dentra/esphome-components
status_led:
  pin: GPIO4
uart:
  - id: uart_bus_a
    rx_pin: 25
    tx_pin: 14
    baud_rate: 19200
  - id: uart_bus_b
    rx_pin: 33
    tx_pin: 27
    baud_rate: 19200
  - id: uart_bus_c
    rx_pin: 32
    tx_pin: 26
    baud_rate: 19200
bl0906_factory:
  - id: sensor_bl0906_a
    uart_id: uart_bus_a
    update_interval: 10s
    calibration:
    # 通道1校准参数
      channel_1:
        current_gain: -1406       # 电流增益校准值 (CHGN_1)
        rms_offset: -2999
      channel_2:
        current_gain: -1406        # 电流增益校准值 (CHGN_2)
        rms_offset: -2316     # 电流偏置校准值 (CHOS_2)
      
      # 通道3校准参数
      channel_3:
        current_gain: -1406       # 电流增益校准值 (CHGN_3)
        rms_offset: -2159     # 电流偏置校准值 (CHOS_3)
      
      # 通道4校准参数
      channel_4:
        current_gain: -1406        # 电流增益校准值 (CHGN_4)
        rms_offset: -1721     # 电流偏置校准值 (CHOS_4)
      
      # 通道5校准参数
      channel_5:
        current_gain: -1406       # 电流增益校准值 (CHGN_5)
        rms_offset: -1586     # 电流偏置校准值 (CHOS_5)
      
      # 通道6校准参数
      channel_6:
        current_gain: -1586        # 电流增益校准值 (CHGN_6)
        rms_offset: -1586     # 电流偏置校准值 (CHOS_6)
      voltage:
        voltage_gain: -211       # 电压增益校准值 (CHGN_V)

  - id: sensor_bl0906_b
    uart_id: uart_bus_b
    update_interval: 10s
    calibration:
    # 通道1校准参数
      channel_1:
        current_gain: -1139       # 电流增益校准值 (CHGN_1)
        rms_offset: -2820
      channel_2:
        current_gain: -1139        # 电流增益校准值 (CHGN_2)
        rms_offset: -2316     # 电流偏置校准值 (CHOS_2)
      
      # 通道3校准参数
      channel_3:
        current_gain: -1139       # 电流增益校准值 (CHGN_3)
        rms_offset: -2159     # 电流偏置校准值 (CHOS_3)
      
      # 通道4校准参数
      channel_4:
        current_gain: -1139        # 电流增益校准值 (CHGN_4)
        rms_offset: -1586     # 电流偏置校准值 (CHOS_4)
      
      # 通道5校准参数
      channel_5:
        current_gain: -1139       # 电流增益校准值 (CHGN_5)
        rms_offset: -1457     # 电流偏置校准值 (CHOS_5)
      
      # 通道6校准参数
      channel_6:
        current_gain: -1139        # 电流增益校准值 (CHGN_6)
        rms_offset: -1457     # 电流偏置校准值 (CHOS_6)
      voltage:
        voltage_gain: 0       # 电压增益校准值 (CHGN_V)

  - id: sensor_bl0906_c
    uart_id: uart_bus_c
    update_interval: 10s
    calibration:
    # 通道1校准参数
      channel_1:
        current_gain: -1351       # 电流增益校准值 (CHGN_1)
        rms_offset: -2365
      channel_2:
        current_gain: -1351        # 电流增益校准值 (CHGN_2)
        rms_offset: -1000     # 电流偏置校准值 (CHOS_2)
      
      # 通道3校准参数
      channel_3:
        current_gain: -1351       # 电流增益校准值 (CHGN_3)
        rms_offset: -1000     # 电流偏置校准值 (CHOS_3)
      
      # 通道4校准参数
      channel_4:
        current_gain: -1351        # 电流增益校准值 (CHGN_4)
        rms_offset: -600     # 电流偏置校准值 (CHOS_4)
      
      # 通道5校准参数
      channel_5:
        current_gain: -1351       # 电流增益校准值 (CHGN_5)
        rms_offset: -600     # 电流偏置校准值 (CHOS_5)
      
      # 通道6校准参数
      channel_6:
        current_gain: -1351        # 电流增益校准值 (CHGN_6)
        rms_offset: -600     # 电流偏置校准值 (CHOS_6)
      voltage:
        voltage_gain: -211       # 电压增益校准值 (CHGN_V)
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_a
    frequency:
      name: 'A Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
    temperature:
      name: 'A Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
    voltage:
      name: 'A Phase Voltage'
      icon: "mdi:lightning-bolt"
      web_server:
        sorting_group_id: power_status 
    current_1:
      name: "${chA1} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentA
    current_2:
      name: "${chA2} current"   
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentA
    current_3:
      name: "${chA3} current" 
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentA   
    current_4:
      name: "${chA4} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentA
    current_5:
      name: "${chA5} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentA 
    current_6:
      name: "${chA6} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentA
    power_1:
      name: "${chA1} power"
      id: chA1_power
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerA
    power_2:
      name: "${chA2} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerA
    power_3:
      name: "${chA3} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerA
    power_4:
      name: "${chA4} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerA
    power_5:
      name: "${chA5} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerA 
    power_6:
      name: "${chA6} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerA
    power_sum:
      name: "A Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: powerA
    energy_1: 
      id: chA1_energy
      name: "${chA1} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chA1_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chA1_energy_
                  value: !lambda return id(chA1_energy_last) + x;
              else:
                - globals.set: 
                    id: chA1_energy_last
                    value: !lambda return id(chA1_energy_);
    energy_2: 
      id: chA2_energy
      name: "${chA2} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chA2_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chA2_energy_
                  value: !lambda return id(chA2_energy_last) + x;
              else:
                - globals.set: 
                    id: chA2_energy_last
                    value: !lambda return id(chA2_energy_);
    energy_3: 
      id: chA3_energy
      name: "${chA3} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chA3_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chA3_energy_
                  value: !lambda return id(chA3_energy_last) + x;
              else:
                - globals.set: 
                    id: chA3_energy_last
                    value: !lambda return id(chA3_energy_);
    energy_4: 
      id: chA4_energy
      name: "${chA4} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chA4_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chA4_energy_
                  value: !lambda return id(chA4_energy_last) + x;
              else:
                - globals.set: 
                    id: chA4_energy_last
                    value: !lambda return id(chA4_energy_);
    energy_5: 
      id: chA5_energy
      name: "${chA5} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chA5_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chA5_energy_
                  value: !lambda return id(chA5_energy_last) + x;
              else:
                - globals.set: 
                    id: chA5_energy_last
                    value: !lambda return id(chA5_energy_);
    energy_6: 
      id: chA6_energy
      name: "${chA6} energy" 
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chA6_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chA6_energy_
                  value: !lambda return id(chA6_energy_last) + x;
              else:
                - globals.set: 
                    id: chA6_energy_last
                    value: !lambda return id(chA6_energy_);
    energy_sum: 
      id: chsA_energy_sum
      name: "A phase sum energy" 
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chsA_energy_sum
                  above: 0.01
              then: 
                globals.set: 
                  id: chsA_energy_
                  value: !lambda return id(chsA_energy_last) + x;
              else:
                - globals.set: 
                    id: chsA_energy_last
                    value: !lambda return id(chsA_energy_);

  - platform: template
    name: "${chA1} Total Energy"
    id: chA1_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chA1_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA
  - platform: template
    name: "${chA2} Total Energy"
    id: chA2_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chA2_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA
  - platform: template
    name: "${chA3} Total Energy"
    id: chA3_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chA3_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA
  - platform: template
    name: "${chA4} Total Energy"
    id: chA4_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chA4_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA
  - platform: template
    name: "${chA5} Total Energy"
    id: chA5_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chA5_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA
  - platform: template
    name: "${chA6} Total Energy"
    id: chA6_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chA6_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA
  - platform: template
    name: "A Phase Total Energy"
    id: chsA_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:chart-bar"
    lambda: |-
      return id(chsA_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyA

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_b
    frequency:
      name: 'B Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
    temperature:
      name: 'B Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
    voltage:
      name: 'B Phase Voltage'
      icon: "mdi:lightning-bolt"
      web_server:
        sorting_group_id: power_status 
    current_1:
      name: "${chB1} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentB
    current_2:
      name: "${chB2} current"   
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentB
    current_3:
      name: "${chB3} current" 
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentB   
    current_4:
      name: "${chB4} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentB
    current_5:
      name: "${chB5} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentB 
    current_6:
      name: "${chB6} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentB
    power_1:
      name: "${chB1} power"
      id: chB1_power
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerB
    power_2:
      name: "${chB2} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerB
    power_3:
      name: "${chB3} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerB
    power_4:
      name: "${chB4} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerB
    power_5:
      name: "${chB5} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerB 
    power_6:
      name: "${chB6} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerB
    power_sum:
      name: "B Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: powerB
    energy_1: 
      id: chB1_energy
      name: "${chB1} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chB1_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chB1_energy_
                  value: !lambda return id(chB1_energy_last) + x;
              else:
                - globals.set: 
                    id: chB1_energy_last
                    value: !lambda return id(chB1_energy_);
    energy_2: 
      id: chB2_energy
      name: "${chB2} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chB2_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chB2_energy_
                  value: !lambda return id(chB2_energy_last) + x;
              else:
                - globals.set: 
                    id: chB2_energy_last
                    value: !lambda return id(chB2_energy_);
    energy_3: 
      id: chB3_energy
      name: "${chB3} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chB3_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chB3_energy_
                  value: !lambda return id(chB3_energy_last) + x;
              else:
                - globals.set: 
                    id: chB3_energy_last
                    value: !lambda return id(chB3_energy_);
    energy_4: 
      id: chB4_energy
      name: "${chB4} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chB4_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chB4_energy_
                  value: !lambda return id(chB4_energy_last) + x;
              else:
                - globals.set: 
                    id: chB4_energy_last
                    value: !lambda return id(chB4_energy_);
    energy_5: 
      id: chB5_energy
      name: "${chB5} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chB5_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chB5_energy_
                  value: !lambda return id(chB5_energy_last) + x;
              else:
                - globals.set: 
                    id: chB5_energy_last
                    value: !lambda return id(chB5_energy_);
    energy_6: 
      id: chB6_energy
      name: "${chB6} energy" 
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chB6_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chB6_energy_
                  value: !lambda return id(chB6_energy_last) + x;
              else:
                - globals.set: 
                    id: chB6_energy_last
                    value: !lambda return id(chB6_energy_);
    energy_sum: 
      id: chsB_energy_sum
      name: "B phase sum energy" 
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chsB_energy_sum
                  above: 0.01
              then: 
                globals.set: 
                  id: chsB_energy_
                  value: !lambda return id(chsB_energy_last) + x;
              else:
                - globals.set: 
                    id: chsB_energy_last
                    value: !lambda return id(chsB_energy_);

  - platform: template
    name: "${chB1} Total Energy"
    id: chB1_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chB1_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB
  - platform: template
    name: "${chB2} Total Energy"
    id: chB2_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chB2_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB
  - platform: template
    name: "${chB3} Total Energy"
    id: chB3_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chB3_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB
  - platform: template
    name: "${chB4} Total Energy"
    id: chB4_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chB4_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB
  - platform: template
    name: "${chB5} Total Energy"
    id: chB5_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chB5_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB
  - platform: template
    name: "${chB6} Total Energy"
    id: chB6_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chB6_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB
  - platform: template
    name: "B Phase Total Energy"
    id: chsB_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:chart-bar"
    lambda: |-
      return id(chsB_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyB

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_c
    frequency:
      name: 'C Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
    temperature:
      name: 'C Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
    voltage:
      name: 'C Phase Voltage'
      icon: "mdi:lightning-bolt"
      web_server:
        sorting_group_id: power_status 
    current_1:
      name: "${chC1} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentC
    current_2:
      name: "${chC2} current"   
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentC
    current_3:
      name: "${chC3} current" 
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentC   
    current_4:
      name: "${chC4} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentC
    current_5:
      name: "${chC5} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentC 
    current_6:
      name: "${chC6} current"
      icon: "mdi:current-ac"
      web_server:
        sorting_group_id: currentC
    power_1:
      name: "${chC1} power"
      id: chC1_power
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerC
    power_2:
      name: "${chC2} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerC
    power_3:
      name: "${chC3} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerC
    power_4:
      name: "${chC4} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerC
    power_5:
      name: "${chC5} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerC 
    power_6:
      name: "${chC6} power"
      icon: "mdi:power-plug"
      web_server:
        sorting_group_id: powerC
    power_sum:
      name: "C Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: powerC
    energy_1: 
      id: chC1_energy
      name: "${chC1} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chC1_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chC1_energy_
                  value: !lambda return id(chC1_energy_last) + x;
              else:
                - globals.set: 
                    id: chC1_energy_last
                    value: !lambda return id(chC1_energy_);
    energy_2: 
      id: chC2_energy
      name: "${chC2} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chC2_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chC2_energy_
                  value: !lambda return id(chC2_energy_last) + x;
              else:
                - globals.set: 
                    id: chC2_energy_last
                    value: !lambda return id(chC2_energy_);
    energy_3: 
      id: chC3_energy
      name: "${chC3} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chC3_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chC3_energy_
                  value: !lambda return id(chC3_energy_last) + x;
              else:
                - globals.set: 
                    id: chC3_energy_last
                    value: !lambda return id(chC3_energy_);
    energy_4: 
      id: chC4_energy
      name: "${chC4} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chC4_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chC4_energy_
                  value: !lambda return id(chC4_energy_last) + x;
              else:
                - globals.set: 
                    id: chC4_energy_last
                    value: !lambda return id(chC4_energy_);
    energy_5: 
      id: chC5_energy
      name: "${chC5} energy"
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chC5_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chC5_energy_
                  value: !lambda return id(chC5_energy_last) + x;
              else:
                - globals.set: 
                    id: chC5_energy_last
                    value: !lambda return id(chC5_energy_);
    energy_6: 
      id: chC6_energy
      name: "${chC6} energy" 
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chC6_energy
                  above: 0.01
              then: 
                globals.set: 
                  id: chC6_energy_
                  value: !lambda return id(chC6_energy_last) + x;
              else:
                - globals.set: 
                    id: chC6_energy_last
                    value: !lambda return id(chC6_energy_);
    energy_sum: 
      id: chsC_energy_sum
      name: "B phase sum energy" 
      internal: true
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: chsC_energy_sum
                  above: 0.01
              then: 
                globals.set: 
                  id: chsC_energy_
                  value: !lambda return id(chsC_energy_last) + x;
              else:
                - globals.set: 
                    id: chsC_energy_last
                    value: !lambda return id(chsC_energy_);

  - platform: template
    name: "${chC1} Total Energy"
    id: chC1_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chC1_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC
  - platform: template
    name: "${chC2} Total Energy"
    id: chC2_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chC2_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC
  - platform: template
    name: "${chC3} Total Energy"
    id: chC3_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chC3_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC
  - platform: template
    name: "${chC4} Total Energy"
    id: chC4_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chC4_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC
  - platform: template
    name: "${chC5} Total Energy"
    id: chC5_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chC5_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC
  - platform: template
    name: "${chC6} Total Energy"
    id: chC6_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:meter-electric"
    lambda: |-
      return id(chC6_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC
  - platform: template
    name: "C Phase Total Energy"
    id: chsC_total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: "mdi:chart-bar"
    lambda: |-
      return id(chsC_energy_);
    update_interval: 10s
    web_server:
          sorting_group_id: energyC

  - platform: template
    name: "A1+B1+C1 Total Power"
    id: ch1_total_power
    unit_of_measurement: "W"
    accuracy_decimals: 2
    lambda: |-
      return id(chA1_power).state + id(chB1_power).state+id(chC1_power).state;
    update_interval: 10s
    icon: "mdi:lightning-bolt"
    web_server:
        sorting_group_id: power_status 
  - platform: template
    name: "A1+B1+C1 Total Energy"
    id: ch1_total_energy
    unit_of_measurement: kWh
    accuracy_decimals: 2
    lambda: |-
      return id(chA1_total_energy).state + id(chB1_total_energy).state+id(chC1_total_energy).state;
    update_interval: 10s
    device_class: energy
    state_class: total_increasing
    icon: "mdi:home-lightning-bolt"
    web_server:
        sorting_group_id: power_status 

  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"
    icon: "mdi:wifi-strength-2"

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
    icon: "mdi:wifi"
    
switch:
  - platform: restart
    name: "${name} controller Restart"
    icon: "mdi:restart"
  - platform: factory_reset
    name: Restart with Factory Default Settings
    icon: "mdi:restart-alert"

time:
  - platform: sntp
    id: my_time
    servers: *************
text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
      icon: "mdi:ip-network"
    ssid:
      name: Energy_meter Connected SSID
      icon: "mdi:wifi-settings"
    bssid:
      name: Energy_meter Connected BSSID
      icon: "mdi:access-point-network"
    mac_address:
      name: Energy_meter Mac Wifi Address
      icon: "mdi:network"
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address
      icon: "mdi:dns"

# number:
#   - platform: bl0906_factory
#     bl0906_factory_id: sensor_bl0906_b
#     chgn_decimal_1:
#       name: "CH_1 Current Gain"
#       id: chgn_1_adjust
#       icon: "mdi:tune-vertical"
#       mode: box

#     chgn_decimal_2:
#       name: "CH_2 Current Gain"
#       icon: "mdi:tune-vertical"
#       mode: box

#     chgn_decimal_3:
#       name: "CH_3 Current Gain"
#       icon: "mdi:tune-vertical"
#       mode: box

#     chgn_decimal_4:
#       name: "CH_4 Current Gain"
#       icon: "mdi:tune-vertical"
#       mode: box

#     chgn_decimal_5:
#       name: "CH_5 Current Gain"
#       icon: "mdi:tune-vertical"
#       mode: box

#     chgn_decimal_6:
#       name: "CH_6 Current Gain"
#       icon: "mdi:tune-vertical"
#       mode: box

#     chgn_v_decimal:
#       name: "Voltage Gain"
#       icon: "mdi:tune-vertical"
#       mode: box

#     chos_decimal_1:
#       name: "CH_1 Current Offset"
#       icon: "mdi:tune"
#       mode: box

#     chos_decimal_2:
#       name: "CH_2 Current Offset"
#       icon: "mdi:tune"
#       mode: box

#     chos_decimal_3:
#       name: "CH_3 Current Offset"
#       icon: "mdi:tune"
#       mode: box

#     chos_decimal_4:
#       name: "CH_4 Current Offset"
#       icon: "mdi:tune"
#       mode: box

#     chos_decimal_5:
#       name: "CH_5 Current Offset"
#       icon: "mdi:tune"
#       mode: box

#     chos_decimal_6:
#       name: "CH_6 Current Offset"
#       icon: "mdi:tune"
#       mode: box

#     chos_v_decimal:
#       name: "Voltage Offset"
#       icon: "mdi:tune"
#       mode: box
#     # 添加十进制形式的RMS校准值
#     rmsgn_decimal_1:
#       name: "CH_1 RMS Gain"
#       icon: "mdi:chart-bell-curve-cumulative"
#       mode: box
      
#     rmsgn_decimal_2:
#       name: "CH_2 RMS Gain"
#       icon: "mdi:chart-bell-curve-cumulative"
#       mode: box
      
#     rmsgn_decimal_3:
#       name: "CH_3 RMS Gain"
#       icon: "mdi:chart-bell-curve-cumulative"
#       mode: box
      
#     rmsgn_decimal_4:
#       name: "CH_4 RMS Gain"
#       icon: "mdi:chart-bell-curve-cumulative"
#       mode: box
      
#     rmsgn_decimal_5:
#       name: "CH_5 RMS Gain"
#       icon: "mdi:chart-bell-curve-cumulative"
#       mode: box
      
#     rmsgn_decimal_6:
#       name: "CH_6 RMS Gain"
#       icon: "mdi:chart-bell-curve-cumulative"
#       mode: box
      
#     rmsos_decimal_1:
#       name: "CH_1 RMS Offset"
#       icon: "mdi:chart-bell-curve"
#       mode: box
      
#     rmsos_decimal_2:
#       name: "CH_2 RMS Offset"
#       icon: "mdi:chart-bell-curve"
#       mode: box
      
#     rmsos_decimal_3:
#       name: "CH_3 RMS Offset"
#       icon: "mdi:chart-bell-curve"
#       mode: box
      
#     rmsos_decimal_4:
#       name: "CH_4 RMS Offset"
#       icon: "mdi:chart-bell-curve"
#       mode: box
      
#     rmsos_decimal_5:
#       name: "CH_5 RMS Offset"
#       icon: "mdi:chart-bell-curve"
#       mode: box
      
#     rmsos_decimal_6:
#       name: "CH_6 RMS Offset"
#       icon: "mdi:chart-bell-curve"
#       mode: box

# button:
#   - platform: template
#     name: "读取校正值"
#     icon: "mdi:chip"
#     on_press:
#       then:
#         - lambda: |-
#             auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
#             bl0906->refresh_all_calib_numbers();