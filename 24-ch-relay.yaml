substitutions:
  name: "24-ch-relay"
  friendly_name: "24位磁保持继电器模块"
esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"
  on_boot:
    - priority: -100
      then:
        - switch.turn_on: relay_enable

esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino

# Enable logging
logger:
  hardware_uart: UART0
# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  # ssid: !secret wifi_ssid
  # password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "24-Ch-Relay Fallback Hotspot"

captive_portal:
web_server:
  version: 3
  local: true
  sorting_groups:
    - id: "zone_a_1_12"
      name: "Zone A (1-12)"
      sorting_weight: 10
    - id: "zone_b_13_24" 
      name: "Zone B (13-24)"
      sorting_weight: 20
    - id: "status_zone_a"
      name: "Status Zone A"
      sorting_weight: 30
    - id: "status_zone_b"
      name: "Status Zone B"
      sorting_weight: 40
    - id: "system_info"
      name: "System Info"
      sorting_weight: 50
    - id: "direct_control"
      name: "Direct Control"
      sorting_weight: 70
    - id: "system_control"
      name: "System Control"
      sorting_weight: 60
sn74hc595:
  - id: sn74hc595_hub
    data_pin: GPIO10
    clock_pin: GPIO19
    latch_pin: GPIO3
    # oe_pin: GPIO18
    sr_count: 6

sn74hc165:
  - id: sn74hc165_hub
    clock_pin: GPIO6
    data_pin: GPIO5
    load_pin: GPIO7
    sr_count: 3
status_led:
  pin: 
    number: 0
# 24路磁保持继电器配置
switch:
  - platform: gpio
    pin:
      number: GPIO18
      inverted: true
    name: "Relay_Enable"
    id: relay_enable
    restore_mode: always_off
    web_server:
        sorting_group_id: "system_control"
  - platform: restart
    name: "${name} controller Restart"
    web_server:
        sorting_group_id: "system_control"
  - platform: factory_reset
    name: Restart with Factory Default Settings
    web_server:
        sorting_group_id: "system_control"
packages:
  # 继电器 1-12
  relay_ch01: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "01"
      on_pin: 5
      off_pin: 0
      status_pin: 3
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch02: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "02"
      on_pin: 7
      off_pin: 6
      status_pin: 2
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch03: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "03"
      on_pin: 13
      off_pin: 8
      status_pin: 1
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch04: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "04"
      on_pin: 15
      off_pin: 14
      status_pin: 0
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch05: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "05"
      on_pin: 21
      off_pin: 16
      status_pin: 11
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch06: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "06"
      on_pin: 23
      off_pin: 22
      status_pin: 10
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch07: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "07"
      on_pin: 29
      off_pin: 24
      status_pin: 9
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch08: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "08"
      on_pin: 31
      off_pin: 30
      status_pin: 8
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch09: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "09"
      on_pin: 37
      off_pin: 32
      status_pin: 19
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch10: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "10"
      on_pin: 39
      off_pin: 38
      status_pin: 18
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch11: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "11"
      on_pin: 45
      off_pin: 40
      status_pin: 17
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  relay_ch12: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "12"
      on_pin: 47
      off_pin: 46
      status_pin: 16
      sorting_group_id: "zone_a_1_12"
      status_group_id: "status_zone_a"

  # 继电器 13-24

  relay_ch24: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "24"
      on_pin: 43
      off_pin: 44
      status_pin: 23
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch23: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "23"
      on_pin: 41
      off_pin: 42
      status_pin: 22
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch22: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "22"
      on_pin: 35
      off_pin: 36
      status_pin: 21
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch21: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "21"
      on_pin: 33
      off_pin: 34
      status_pin: 20
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch20: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "20"
      on_pin: 27
      off_pin: 28
      status_pin: 15
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch19: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "19"
      on_pin: 25
      off_pin: 26
      status_pin: 14
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch18: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "18"
      on_pin: 19
      off_pin: 20
      status_pin: 13
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch17: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "17"
      on_pin: 17
      off_pin: 18
      status_pin: 12
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch16: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "16"
      on_pin: 11
      off_pin: 12
      status_pin: 7
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch15: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "15"
      on_pin: 9
      off_pin: 10
      status_pin: 6
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch14: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "14"
      on_pin: 3
      off_pin: 4
      status_pin: 5
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"

  relay_ch13: !include
    file: templates/magnetic_latching_relay_template.yaml
    vars:
      relay_name: "Relay"
      relay_id: "relay"
      channel_num: "13"
      on_pin: 1
      off_pin: 2
      status_pin: 4
      sorting_group_id: "zone_b_13_24"
      status_group_id: "status_zone_b"


text_sensor:
  - platform: wifi_info
    ip_address:
      name: ${name} IP Address
      icon: mdi:ip-network
      web_server:
        sorting_group_id: "system_info"
    ssid:
      name: ${name} Connected SSID
      icon: mdi:wifi-settings
      web_server:
        sorting_group_id: "system_info"
    bssid:
      name: ${name} Connected BSSID
      icon: mdi:router-wireless
      web_server:
        sorting_group_id: "system_info"
    mac_address:
      name: ${name} Mac Wifi Address
      icon: mdi:network-outline
      web_server:
        sorting_group_id: "system_info"
    # scan_results:
    #   name: ${name} Latest Scan Results
    dns_address:
      name: ${name} DNS Address
      icon: mdi:dns
      web_server:
        sorting_group_id: "system_info"
  - platform: version
    name: "ESPHome Version"
    icon: mdi:new-box
    web_server:
        sorting_group_id: "system_info"