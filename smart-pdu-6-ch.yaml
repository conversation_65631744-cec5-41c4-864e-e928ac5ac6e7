packages:
  bl0906_factory: !include smart-6ch-pdu/bl0906_factory.yaml
  display: !include smart-6ch-pdu/display.yaml


esphome:
  name: "${name}"
  friendly_name: smart-pdu-6-ch
  platformio_options:
    board_build.flash_mode: dio
    board_build.arduino.memory_type: qio_opi
# esp32:
#   board: esp32-s3-devkitc-1
#   framework:
#     type: arduino
#     version: recommended
esp32:
  board: esp32-s3-devkitc-1
  framework:
    type: esp-idf
    version: recommended
    # Custom sdkconfig options
    sdkconfig_options:
      COMPILER_OPTIMIZATION_SIZE: y
      CONFIG_ESPTOOLPY_OCT_FLASH: n
      CONFIG_SPIRAM_MODE: octal
  flash_size: 16mb

# Enable logging
logger:
  hardware_uart: uart0
  baud_rate: 230400

  level: VERBOSE
  logs:
    lvgl: debug
# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  power_save_mode: none
  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "smart-pdu-6-ch"
    password: ""

captive_portal:
web_server:
  port: 80
  local: True
  version: 3
status_led:
  pin: GPIO20
uart:
  - id: uart_bus
    rx_pin: 9
    tx_pin: 10
    baud_rate: 19200
psram:
  mode: octal
  speed: 80MHz

  # - source: github://dentra/esphome-components
#   - source: github://oxan/esphome-stream-server
# stream_server:
#   uart_id: uart_bus
#   port: 6638

i2c:
  - sda: GPIO42
    scl: GPIO41
    # scan: true
    id: bus_a
  - sda: 5
    scl: 6
    #scan: true
    # timeout: 50ms
    id: bus_b



# binary_sensor:

#   - platform: gpio
#     pin:
#       number: 0
#       inverted: True
#     id: pin0
#     on_press:
#       then:
#         - if:
#             condition: lvgl.is_paused
#             then:
#               - lvgl.resume:
#               - light.turn_on: back_light
#             else:
#               - lvgl.page.next:
#                   animation: OUT_LEFT
#                   time: 300ms


switch:
  - platform: restart
    name: "${name} controller Restart"
  - platform: lvgl
    widget: wg_cinema_led2
    name: cinema_led2
    on_turn_on:
      - homeassistant.action:
          action: light.turn_on
          data:
            entity_id: light.led_controller_workshop_light2
    on_turn_off:
      - homeassistant.action:
          action: light.turn_off
          data:
            entity_id: light.led_controller_workshop_light2
  - platform: lvgl
    widget: wg_cinema_led3
    name: cinema_led3
    on_turn_on:
      - homeassistant.action:
          action: light.turn_on
          data:
            entity_id: light.led_controller_workshop_light3
    on_turn_off:
      - homeassistant.action:
          action: light.turn_off
          data:
            entity_id: light.led_controller_workshop_light3
  - platform: lvgl
    widget: wg_cinema_ac1
    name: cinema_AC
    on_turn_on:
      - homeassistant.action:
          action: climate.turn_on
          data:
            entity_id: climate.zhonghong_hvac_1_3
    on_turn_off:
      - homeassistant.action:
          action: climate.turn_off
          data:
            entity_id: climate.zhonghong_hvac_1_3
  - platform: lvgl
    widget: wg_working_led
    name: working_led
    on_turn_on:
      - homeassistant.action:
          action: switch.turn_on
          data:
            entity_id: switch.smart_plug_power_switch
    on_turn_off:
      - homeassistant.action:
          action: switch.turn_off
          data:
            entity_id: switch.smart_plug_power_switch
  - platform: gpio
    name: ch1_on
    id: ch1_on
    internal: True
    pin:
        pcf8574: pcf8574_device1
        number: 6
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch1_on
  - platform: gpio
    name: ch1_off
    id: ch1_off
    internal: True
    pin:
        pcf8574: pcf8574_device1
        number: 5
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch1_off
  - platform: gpio
    name: ch2_on
    id: ch2_on
    internal: True
    pin:
        pcf8574: pcf8574_device1
        number: 0
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch2_on
  - platform: gpio
    name: ch2_off
    id: ch2_off
    internal: True
    pin:
        pcf8574: pcf8574_device1
        number: 1
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch2_off
  - platform: template
    name: "Ch1 Relay"
    id: relay1
    restore_mode: disabled
    lambda: |-
      if (id(relay1_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ch1_on
    turn_off_action:
      - switch.turn_on: ch1_off

  - platform: template
    name: "Ch2 Relay"
    id: relay2
    restore_mode: disabled
    lambda: |-
      if (id(relay2_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ch2_on
    turn_off_action:
      - switch.turn_on: ch2_off
  - platform: gpio
    name: ch3_on
    id: ch3_on
    internal: True
    pin:
        pcf8574: pcf8574_device2
        number: 6
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch3_on
  - platform: gpio
    name: ch3_off
    id: ch3_off
    internal: True
    pin:
        pcf8574: pcf8574_device2
        number: 5
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch3_off
  - platform: gpio
    name: ch4_on
    id: ch4_on
    internal: True
    pin:
        pcf8574: pcf8574_device2
        number: 0
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch4_on
  - platform: gpio
    name: ch4_off
    id: ch4_off
    internal: True
    pin:
        pcf8574: pcf8574_device2
        number: 1
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch4_off
  - platform: template
    name: "Ch3 Relay"
    id: relay3
    restore_mode: disabled
    lambda: |-
      if (id(relay3_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ch3_on
    turn_off_action:
      - switch.turn_on: ch3_off
  - platform: template
    name: "Ch4 Relay"
    id: relay4
    restore_mode: disabled
    lambda: |-
      if (id(relay4_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ch4_on
    turn_off_action:
      - switch.turn_on: ch4_off

  - platform: gpio
    name: ch5_on
    id: ch5_on
    internal: True
    pin:
        pcf8574: pcf8574_device3
        number: 6
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch5_on
  - platform: gpio
    name: ch5_off
    id: ch5_off
    internal: True
    pin:
        pcf8574: pcf8574_device3
        number: 5
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch5_off
  - platform: gpio
    name: ch6_on
    id: ch6_on
    internal: True
    pin:
        pcf8574: pcf8574_device3
        number: 0
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch6_on
  - platform: gpio
    name: ch6_off
    id: ch6_off
    internal: True
    pin:
        pcf8574: pcf8574_device3
        number: 1
        mode:
          output: true
        inverted: true
    on_turn_on:
      - delay: 500ms
      - switch.turn_off: ch6_off
  - platform: template
    name: "Ch5 Relay"
    id: relay5
    restore_mode: disabled
    lambda: |-
      if (id(relay5_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ch5_on
    turn_off_action:
      - switch.turn_on: ch5_off
  - platform: template
    name: "Ch6 Relay"
    id: relay6
    restore_mode: disabled
    lambda: |-
      if (id(relay6_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: ch6_on
    turn_off_action:
      - switch.turn_on: ch6_off

pcf8574:
  - id: 'pcf8574_device1'
    i2c_id: bus_b
    address: 0x20
  - id: 'pcf8574_device2'
    i2c_id: bus_b
    address: 0x27
  - id: 'pcf8574_device3'
    i2c_id: bus_b
    address: 0x22
binary_sensor:
  - platform: gpio
    id: relay1_status
    internal: True
    name: "relay1_status"
    pin:
      pcf8574: pcf8574_device1
      number: 4
      mode:
        input: true
      inverted: true
    on_state:
      then:
        lvgl.widget.update:
          id: lvgl_ch1_switch
          state:
            checked: !lambda return x;
  - platform: gpio
    id: relay2_status
    internal: True
    name: "relay2_status"
    pin:
      pcf8574: pcf8574_device1
      number: 3
      mode:
        input: true
      inverted: true
    on_state:
      then:
        lvgl.widget.update:
          id: lvgl_ch2_switch
          state:
            checked: !lambda return x;
  - platform: gpio
    name: ch1_button
    internal: True
    pin:
      pcf8574: pcf8574_device1
      number: 7
      mode:
        input: true
      inverted: true
    on_click:
      then:
        - switch.toggle: relay1
  - platform: gpio
    name: ch2_button
    internal: True
    pin:
      pcf8574: pcf8574_device1
      number: 2
      mode:
        input: true
      inverted: true
    on_click:
      then:
        - switch.toggle: relay2
  - platform: gpio
    id: relay3_status
    internal: True
    name: "relay3_status"
    pin:
      pcf8574: pcf8574_device2
      number: 4
      mode:
        input: true
      inverted: true
    on_state:
      then:
        lvgl.widget.update:
          id: lvgl_ch3_switch
          state:
            checked: !lambda return x;
  - platform: gpio
    id: relay4_status
    internal: True
    name: "relay4_status"
    pin:
      pcf8574: pcf8574_device2
      number: 3
      mode:
        input: true
      inverted: true
    on_state:
      then:
        lvgl.widget.update:
          id: lvgl_ch4_switch
          state:
            checked: !lambda return x;
  - platform: gpio
    name: ch3_button
    internal: True
    pin:
      pcf8574: pcf8574_device2
      number: 7
      mode:
        input: true
      inverted: true
    on_click:
      then:
        - switch.toggle: relay3
  - platform: gpio
    name: ch4_button
    internal: True
    pin:
      pcf8574: pcf8574_device2
      number: 2
      mode:
        input: true
      inverted: true
    on_click:
      then:
        - switch.toggle: relay4
  - platform: gpio
    id: relay5_status
    internal: True
    name: "relay5_status"
    pin:
      pcf8574: pcf8574_device3
      number: 4
      mode:
        input: true
      inverted: true
  - platform: gpio
    id: relay6_status
    name: "relay6_status"
    internal: True
    pin:
      pcf8574: pcf8574_device3
      number: 3
      mode:
        input: true
      inverted: true
  - platform: gpio
    name: ch5_button
    internal: True
    pin:
      pcf8574: pcf8574_device3
      number: 7
      mode:
        input: true
      inverted: true
    on_click:
      then:
        - switch.toggle: relay5
  - platform: gpio
    name: ch6_button
    internal: True
    pin:
      pcf8574: pcf8574_device3
      number: 2
      mode:
        input: true
      inverted: true
    on_click:
      then:
        - switch.toggle: relay6

  - platform: homeassistant
    id: light1
    entity_id: light.led_controller_workshop_light2
    publish_initial_state: True
    on_state:
      then:
        lvgl.widget.update:
          id: dash_button1
          state:
            checked: !lambda return x;
  - platform: homeassistant
    id: work_desk_socket
    entity_id: switch.smart_plug_power_switch
    publish_initial_state: True
    on_state:
      then:
        lvgl.widget.update:
          id: dash_button2
          state:
            checked: !lambda return x;

time:
  - platform: sntp
    id: my_time
    servers: *************
    on_time_sync:
      then:
        - lvgl.label.update:
            id: dash_time
            text:
              time_format: "%R"
              time: my_time
    on_time:
      - minutes: "*"
        seconds: 0
        then:
          - lvgl.label.update:
              id: dash_time
              text:
                time_format: "%R"
                time: my_time
text_sensor:
  - platform: wifi_info
    ip_address:
      name: "${name} IP Address"
    ssid:
      name: "${name} Connected SSID"
    bssid:
      name: "${name} Connected BSSID"
    mac_address:
      name: "${name} Mac Wifi Address"
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: "${name} DNS Address"

# button:
#   - platform: template
#     name: "复位能量值"
#     id: reset_energy_btn
#     icon: "mdi:autorenew"
#     on_press:
#       then:
#         - uart.write: [0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7]
#         # - delay: 10ms
#         - uart.write: [0xCA, 0x9F, 0x5A, 0x5A, 0x5A, 0xAD]

#     #    - uart.write: [0xCA, 0x9E, 0x00, 0x00, 0x00, 0x61]
