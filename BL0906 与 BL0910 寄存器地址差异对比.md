### BL0906 与 BL0910 寄存器地址差异对比

#### 一、核心差异概述
两款芯片的寄存器地址分配存在显著差异，主要源于 **BL0910 支持更多电流通道**（10通道 vs BL0906的6通道），导致地址映射和功能寄存器分布发生偏移。以下是具体对比：

#### 二、电参量寄存器地址差异
| 功能类别               | BL0906 地址范围 | BL0910 地址范围 | 差异说明                     |
|------------------------|----------------|----------------|----------------------------|
| 电流波形寄存器         | 2-9, A         | 1-A            | BL0910 支持10通道，地址整体前移 |
| 电压波形寄存器         | B              | B              | 地址相同（均为0xB）          |
| 电流有效值寄存器       | D-14           | C-15           | BL0910 因多通道，地址范围扩展 |
| 快速有效值寄存器       | 18-1F, 21      | 17-20, 21      | BL0910 起始地址提前1个单位    |
| 有功功率寄存器         | 23-2A, 2C      | 22-2B, 2C      | 总功率寄存器地址相同（0x2C）  |
| 夹角寄存器             | 3D-44          | 3C-45          | BL0910 支持10通道，地址扩展   |

#### 三、校表寄存器关键差异
1. **相位校正寄存器**
   - BL0906：地址64-68对应6个电流通道，分散配置
   - BL0910：地址64-68采用合并配置（每寄存器2个通道），支持10个电流通道

2. **有效值增益调整**
   - BL0906：RMSGN[1]-[6]（地址6D-74）+ 电压通道（76）
   - BL0910：RMSGN[1]-[11]（地址6C-76），电压作为第11通道

3. **新增寄存器**
   - BL0910 地址5F新增 `TPS2`（外部温度值寄存器）
   - BL0906 独有 OTP 寄存器组（地址A0-B5）

#### 四、典型地址偏移示例
| 寄存器功能         | BL0906 地址 | BL0910 地址 | 偏移原因               |
|--------------------|------------|------------|----------------------|
| 电流1有效值        | D          | C          | BL0910 通道数增加导致地址前移 |
| 有功功率通道1      | 23         | 22         | 多通道压缩地址空间        |
| 快速有效值阈值寄存器 | 8B         | -          | BL0910 未发现对应寄存器    |

#### 五、总结建议
1. **硬件适配**：若从BL0906迁移至BL0910，需重新映射电流通道（1-6 → 1-10）的寄存器地址
2. **软件驱动**：需特别注意0x17-0x2B区间的地址偏移，以及相位校正寄存器的合并配置格式
3. **功能扩展**：BL0910的10通道设计适合多负载监测场景，但需注意新增的外部温度寄存器（0x5F）的应用
        